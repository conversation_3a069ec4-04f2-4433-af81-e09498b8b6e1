package com.ruoyi.web.controller.idle;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.idle.domain.DataMeetingRecord;
import com.ruoyi.idle.domain.bo.DataMeetingRecordQueryBo;
import com.ruoyi.idle.service.IDataMeetingRecordService;
import io.swagger.annotations.ApiImplicitParam;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 会议记录 Controller
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
@Api(value = "会议记录 控制器", tags = {"会议记录 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/meeting/record")
public class DataMeetingRecordController extends BaseController {

	private final IDataMeetingRecordService iDataMeetingRecordService;

	/**
	 * 查询会议记录 列表
	 */
	@ApiOperation("查询会议记录 列表")
//    @PreAuthorize("@ss.hasPermi('idle:meeting:list')")
	@GetMapping("/list")
	public TableDataInfo<DataMeetingRecord> list(@Validated DataMeetingRecordQueryBo bo) {
		return iDataMeetingRecordService.queryPageList(bo);
	}

	/**
	 * 导出会议记录 列表
	 */
	@ApiOperation("导出会议记录 列表")
//    @PreAuthorize("@ss.hasPermi('idle:meeting:export')")
	@Log(title = "会议记录 ", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataMeetingRecord> export(@Validated DataMeetingRecordQueryBo bo) {
		List<DataMeetingRecord> list = iDataMeetingRecordService.queryList(bo);
		ExcelUtil<DataMeetingRecord> util = new ExcelUtil<DataMeetingRecord>(DataMeetingRecord.class);
		return util.exportExcel(list, "会议记录 ");
	}

	/**
	 * 获取会议记录 详细信息
	 */
	@ApiOperation("获取会议记录 详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:meeting:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataMeetingRecord> getInfo(@NotNull(message = "主键不能为空")
												 @PathVariable("id") String id) {
		return AjaxResult.success(iDataMeetingRecordService.queryById(id));
	}

	/**
	 * 新增会议记录
	 */
	@ApiOperation("新增会议记录 ")
//    @PreAuthorize("@ss.hasPermi('idle:meeting:add')")
	@Log(title = "会议记录 ", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataMeetingRecord bo) {
		return toAjax(iDataMeetingRecordService.insertByAddBo(bo) ? 1 : 0);
	}

	/**
	 * 修改会议记录
	 */
	@ApiOperation("修改会议记录 ")
//    @PreAuthorize("@ss.hasPermi('system:meeting:edit')")
	@Log(title = "会议记录 ", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataMeetingRecord bo) {
		return toAjax(iDataMeetingRecordService.updateByEditBo(bo) ? 1 : 0);
	}

	/**
	 * 删除会议记录
	 */
	@ApiOperation("删除会议记录 ")
//    @PreAuthorize("@ss.hasPermi('system:meeting:remove')")
	@Log(title = "会议记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataMeetingRecordService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	@ApiOperation("获取会议记录树形数据")
	@GetMapping("/getTreeData")
	@Log(title = "会议记录")
	@ApiImplicitParam(name = "taskId", value = "巡查任务编号", dataTypeClass = String.class, required = true)
	public AjaxResult getMeetingTreeData(String taskId) {
		if (null == taskId) return AjaxResult.error("巡查任务编号不能为空");
		// step.1 根据巡查任务编号获取所有的会议记录
		LambdaQueryWrapper<DataMeetingRecord> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataMeetingRecord::getTaskId, taskId);
		List<DataMeetingRecord> recordList = iDataMeetingRecordService.list(queryWrapper);
		List<Tree<String>> treeNodes = new ArrayList<>();
		// step.2 循环会议记录列表，通过唯一编号查询该次会议的与会人员名单
		if (recordList.size() != 0) {
			//配置
			TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
			// 自定义属性名 都要默认值的
			treeNodeConfig.setIdKey("id");
			// 最大递归深度
			// treeNodeConfig.setDeep(3);
		}
		// step.3 构建树形结构数据
		return null;
	}
}
