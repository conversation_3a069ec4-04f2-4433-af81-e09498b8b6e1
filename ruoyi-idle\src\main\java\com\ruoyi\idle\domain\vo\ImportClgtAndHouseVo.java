package com.ruoyi.idle.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@ApiModel("国土空间和房屋外业核查导入")
@ExcelIgnoreUnannotated
public class ImportClgtAndHouseVo {
	private static final long serialVersionUID = 1L;
	/**
	 * 闲置土地所在市
	 */
	@ExcelProperty(value = "闲置土地所在市")
	private String city;

	/**
	 * 闲置土地所在县代码
	 */
	@ExcelProperty(value = "闲置土地所在县代码")
	private String countyCode;

	/**
	 * 闲置土地所在县名称
	 */
	@ExcelProperty(value = "闲置土地所在县名称")
	private String countyName;

	/**
	 * 合同编号
	 */
	@ExcelProperty(value = "合同编号")
	private String contractNo;

	/**
	 * 供应方式
	 */
	@ExcelProperty(value = "供应方式")
	private String supplyType;

	/**
	 * 项目名称
	 */
	@ExcelProperty(value = "项目名称")
	private String projectName;

	/**
	 * 数据年度
	 */
	@ExcelProperty(value = "数据年度")
	private String year;

	/**
	 * 数据季度
	 */
	@ExcelProperty(value = "数据季度")
	private String quarter;

	/**
	 * 电子监管号
	 */
	@ExcelProperty(value = "电子监管号")
	private String supervisionNo;

	/**
	 * 土地用途
	 */
	@ExcelProperty(value = "土地用途")
	private String landUse;

	/**
	 * 土地面积（公顷）
	 */
	@ExcelProperty(value = "土地面积（公顷）")
	private double landArea;

	/**
	 * 签订日期
	 */
	@ExcelProperty(value = "签订日期")
	private Date signDate;

	/**
	 * 约定动工时间
	 */
	@ExcelProperty(value = "约定动工时间")
	private Date agreedStartTime;

	/**
	 * 实际动工时间
	 */
	@ExcelProperty(value = "实际动工时间")
	private Date actualStartTime;

	/**
	 * 实际竣工时间
	 */
	@ExcelProperty(value = "实际竣工时间")
	private Date actualEndTime;

	/**
	 * 闲置状态
	 */
	@ExcelProperty(value = "闲置状态")
	private String idleStatus;

	/**
	 * 图斑编号
	 */
	@ExcelProperty(value = "图斑编号")
	private String spotNumber;

	/**
	 * 内业备注
	 */
	@ExcelProperty(value = "内业备注")
	private String insideRemark;

	/**
	 * 消化类型
	 */
	@ExcelProperty(value = "消化类型")
	private String digestionType;

	/**
	 * 项目详细位置
	 */
	@ExcelProperty(value = "项目详细位置")
	private String address;

	/**
	 * 项目空间数据
	 */
	@ExcelProperty(value = "项目空间数据")
	private String geoData;

	/**
	 * 宗地号
	 */
	@ExcelProperty(value = "宗地号")
	private String parcelNum;

	/**
	 * 项目中心点经度
	 */
	@ExcelProperty(value = "项目中心点经度")
	private String longitude;

	/**
	 * 项目中心点纬度
	 */
	@ExcelProperty(value = "项目中心点纬度")
	private String latitude;

	/**
	 * 是否已分配巡查任务
	 */
	@ExcelProperty(value = "是否已分配巡查任务")
	private String isAllot;

	/**
	 * 不动产权证号
	 */
	@ExcelProperty(value = "不动产权证号")
	private String warrantNum;

	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注")
	private String remark;

	/**
	 * 建筑编号
	 */
	@ExcelProperty(value = "建筑编号")
	private String jzbh;

	/**
	 * 名称
	 */
	@ExcelProperty(value = "名称")
	private String mc;

	/**
	 * 基底面积(平方米)
	 */
	@ExcelProperty(value = "基底面积(平方米)")
	private Double jdmj;

	/**
	 * 地上层数
	 */
	@ExcelProperty(value = "地上层数")
	private String dscs;

	/**
	 * 地上建筑面积(平方米)
	 */
	@ExcelProperty(value = "地上建筑面积(平方米)")
	private Double dsjzmj;

	/**
	 * 建筑高度(米)
	 */
	@ExcelProperty(value = "建筑高度(米)")
	private Double jzgd;

	/**
	 * 异形建筑
	 */
	@ExcelProperty(value = "异形建筑")
	private String yxjz;

	/**
	 * 房屋套数
	 */
	@ExcelProperty(value = "房屋套数")
	private Long fwts;

	/**
	 * 详细地址
	 */
	@ExcelProperty(value = "详细地址")
	private String xxdz;

	/**
	 * 建筑年代
	 */
	@Excel(name = "建筑年代", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("建筑年代")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ExcelProperty(value = "建筑年代")
	private Date jznd;

	/**
	 * 调查时间
	 */
	@Excel(name = "调查时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("调查时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ExcelProperty(value = "调查时间")
	private Date dcsj;

	/**
	 * 建筑状态
	 */
	@ExcelProperty(value = "建筑状态")
	private String jzzt;

	/**
	 * 结构类型
	 */
	@ExcelProperty(value = "结构类型")
	private String jglx;

	/**
	 * 实际用途
	 */
	@ExcelProperty(value = "实际用途")
	private String sjyt;

	/**
	 * 规划用途
	 */
	@ExcelProperty(value = "规划用途")
	private String ghyt;

	/**
	 * 竣工用途
	 */
	@ExcelProperty(value = "竣工用途")
	private String jgyt;

	/**
	 * 建筑分层用途
	 */
	@ExcelProperty(value = "建筑分层用途")
	private String jzfcyt;

	/**
	 * 分用途楼层段
	 */
	@ExcelProperty(value = "分用途楼层段")
	private String fytlcd;

	/**
	 * 分用途建筑面积(平方米)
	 */
	@ExcelProperty(value = "分用途建筑面积(平方米)")
	private Double fytjzmj;

	/**
	 * 地下层数
	 */
	@ExcelProperty(value = "地下层数")
	private String dxcs;

	/**
	 * 地下建筑面积(平方米)
	 */
	@ExcelProperty(value = "地下建筑面积(平方米)")
	private Double dxjzmj;

	/**
	 * 房屋性质
	 */
	@ExcelProperty(value = "房屋性质")
	private String fwxz;

	/**
	 * 闲置时间
	 */
	@ExcelProperty(value = "闲置时间")
	private String xzsj;

	/**
	 * 用地所有权
	 */
	@ExcelProperty(value = "用地所有权")
	private String ydsyq;

	/**
	 * 填写说明
	 */
	@ExcelProperty(value = "填写说明")
	private String txsm;

	/**
	 * 删除标志
	 */
	@Excel(name = "删除标志")
	@ApiModelProperty("删除标志")
	private String deleteFlag;

	/**
	 * 任务id
	 */
	private String taskId;

	/**
	 * 国土空间表id
	 */
	private String clgtId;

	/**
	 * 巡查结果
	 */
	@ExcelProperty(value = "巡查结果")
	private String patrolResult;

	/**
	 * 巡查意见
	 */
	@ExcelProperty(value = "巡查意见")
	private String patrolOpinion;
}
