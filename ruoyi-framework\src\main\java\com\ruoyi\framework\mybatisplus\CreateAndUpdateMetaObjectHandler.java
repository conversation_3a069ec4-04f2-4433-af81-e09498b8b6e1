package com.ruoyi.framework.mybatisplus;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * MP注入处理器
 *
 * <AUTHOR>
 * @date 2021/4/25
 */
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {

	@Override
	public void insertFill(MetaObject metaObject) {
		this.setFieldValByName("createTime", new Date(), metaObject);
		String createBy = SecurityUtils.getUsername() == null ? "admin" : SecurityUtils.getUsername();
		this.setFieldValByName("createBy", createBy, metaObject);
		//根据属性名字设置要填充的值
	}

	@Override
	public void updateFill(MetaObject metaObject) {
		String updateBy = SecurityUtils.getUsername() == null ? "admin" : SecurityUtils.getUsername();
		this.setFieldValByName("updateBy", updateBy, metaObject);
		this.setFieldValByName("updateTime", new Date(), metaObject);
	}

}
