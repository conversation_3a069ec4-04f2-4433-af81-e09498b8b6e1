package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataVerifyRecord;
import com.ruoyi.idle.domain.bo.DataVerifyRecordQueryBo;
import com.ruoyi.idle.mapper.DataVerifyRecordMapper;
import com.ruoyi.idle.service.IDataVerifyRecordService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 核查记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-17
 */
@Service
public class DataVerifyRecordServiceImpl extends ServicePlusImpl<DataVerifyRecordMapper, DataVerifyRecord> implements IDataVerifyRecordService {

	@Override
	public DataVerifyRecord queryById(String id) {
		return getVoById(id, DataVerifyRecord.class);
	}

	@Override
	public TableDataInfo<DataVerifyRecord> queryPageList(DataVerifyRecordQueryBo bo) {
		PagePlus<DataVerifyRecord, DataVerifyRecord> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataVerifyRecord.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataVerifyRecord> queryList(DataVerifyRecordQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataVerifyRecord.class);
	}

	private LambdaQueryWrapper<DataVerifyRecord> buildQueryWrapper(DataVerifyRecordQueryBo bo) {
		LambdaQueryWrapper<DataVerifyRecord> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getTaskId()), DataVerifyRecord::getTaskId, bo.getTaskId());
		lqw.eq(StrUtil.isNotBlank(bo.getDataId()), DataVerifyRecord::getDataId, bo.getDataId());
		lqw.eq(StrUtil.isNotBlank(bo.getProjectType()), DataVerifyRecord::getProjectType, bo.getProjectType());
		lqw.eq(StrUtil.isNotBlank(bo.getCatalogId()), DataVerifyRecord::getCatalogId, bo.getCatalogId());
		lqw.like(StrUtil.isNotBlank(bo.getCatalogName()), DataVerifyRecord::getCatalogName, bo.getCatalogName());
		lqw.eq(StrUtil.isNotBlank(bo.getIsProcess()), DataVerifyRecord::getIsProcess, bo.getIsProcess());
		lqw.eq(StrUtil.isNotBlank(bo.getVideoIsReal()), DataVerifyRecord::getVideoIsReal, bo.getVideoIsReal());
		lqw.eq(StrUtil.isNotBlank(bo.getSiteIsReal()), DataVerifyRecord::getSiteIsReal, bo.getSiteIsReal());
		lqw.eq(StrUtil.isNotBlank(bo.getHandleIsCorrect()), DataVerifyRecord::getHandleIsCorrect, bo.getHandleIsCorrect());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataVerifyRecord bo) {
		DataVerifyRecord add = BeanUtil.toBean(bo, DataVerifyRecord.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataVerifyRecord bo) {
		DataVerifyRecord update = BeanUtil.toBean(bo, DataVerifyRecord.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataVerifyRecord entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

}
