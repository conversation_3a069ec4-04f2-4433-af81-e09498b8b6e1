<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataLandNotProvidedMapper">

    <resultMap type="com.ruoyi.idle.domain.DataLandNotProvided" id="DataLandNotProvidedResult">
        <result property="id" column="id"/>
        <result property="city" column="city"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="supervisionNo" column="supervision_no"/>
        <result property="projectName" column="project_name"/>
        <result property="year" column="year"/>
        <result property="quarter" column="quarter"/>
        <result property="contractNo" column="contract_no"/>
        <result property="supplyType" column="supply_type"/>
        <result property="providedArea" column="provided_area"/>
        <result property="isAllot" column="is_allot"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="address" column="address"/>
        <result property="landUse" column="land_use"/>
        <result property="parcelNum" column="parcel_num"/>
    </resultMap>

    <select id="selectSupervisionNoByTaskId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT supervision_no
        FROM `data_land_not_provided`
        where id = (SELECT idle_land_id FROM data_patrol_task where id = #{taskId})
    </select>


</mapper>
