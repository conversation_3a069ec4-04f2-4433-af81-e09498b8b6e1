package com.ruoyi.idle.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.vo.ProjectPackageVerifyVo;
import com.ruoyi.idle.domain.vo.ProjectPackageVo;
import com.ruoyi.idle.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Created with IntelliJ IDEA.
 * @Title: PackageAsyncTaskImpl
 * @Description: com.ruoyi.idle.service.impl
 * @Author: HongDeng
 * @Date: 2021/12/7 9:34
 * @Version: 1.0.0
 **/
@Component
public class PackageAsyncTaskImpl {

	IDataIdleLandService idleLandService;
	IDataLandNotProvidedService notProvidedService;
	IDataPatrolTaskService patrolTaskService;
	IDataAttachmentService attachmentService;
	IDataFileCatalogService fileCatalogService;
	IDataDigestTypeService digestTypeService;
	IDataExportRecordService exportRecordService;

	public PackageAsyncTaskImpl(){
		idleLandService = SpringUtils.getBean(IDataIdleLandService.class);
		notProvidedService = SpringUtils.getBean(IDataLandNotProvidedService.class);
		patrolTaskService = SpringUtils.getBean(IDataPatrolTaskService.class);
		attachmentService = SpringUtils.getBean(IDataAttachmentService.class);
		fileCatalogService = SpringUtils.getBean(IDataFileCatalogService.class);
		digestTypeService = SpringUtils.getBean(IDataDigestTypeService.class);
		exportRecordService = SpringUtils.getBean(IDataExportRecordService.class);
	}

	/**
	 * 按照项目的Id打包
	 * @param vo
	 * @return
	 */
	@Async
	public Future<String> packageByTaskId(ProjectPackageVo vo) throws InterruptedException{
		long begin = System.currentTimeMillis();
		try{
			// 根据日期创建最外级文件夹
			String dateNow = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
			String parentDir = RuoYiConfig.getPackagePath() + dateNow + "_Export";
			FileUtil.mkdir(parentDir);
			// step.1 循环taskId列表并添加到压缩包内
			for (String taskId : vo.getTaskIdList()){
				ProjectPackageVerifyVo verifyVo = packageVerify(taskId);
				//if(!verifyVo.getIsSuccess())return AjaxResult.error(verifyVo.getErrorMsg());
				// step.2 赋值会用到的参数
				DataPatrolTask patrolTask = verifyVo.getPatrolTask();
				List<DataFileCatalog> catalogList = verifyVo.getCatalogList();
				String supervisionNo = verifyVo.getSupervisionNo();
				supervisionNo += verifyVo.getPatrolTask().getProjectName();
				supervisionNo = supervisionNo.replace("/", "_");
				String projectPath = RuoYiConfig.getPackageTempPath()+supervisionNo;

				String HB_CR ="";
				String ContractNo = verifyVo.getPatrolTask().getContractNo();
				if(ContractNo==null){
					ContractNo="";
				}
				if(ContractNo.startsWith("HB")){
					HB_CR="HB";
				}else if(ContractNo.startsWith("CR")){
					HB_CR="CR";
				}else if(ContractNo.startsWith("LQ")){
					HB_CR="";
				}else if(ContractNo.startsWith("西山出")){
					HB_CR="CR";
				}
				// step.3 获取压缩包文件
				File zipFile =  getZipFile(projectPath,catalogList,patrolTask);
				// step.4 删除未打包的临时文件夹
				FileUtil.del(projectPath);
				// step.5 移动zip文件到最外级文件夹
				String packagePath = parentDir+"/"+supervisionNo+"_"+HB_CR+".zip";
				File parentFile = FileUtil.file(packagePath);
				FileUtil.copy(zipFile,parentFile,true);
			}
			// step.6 压缩最外级文件夹为zip文件
			File parentZipFile = ZipUtil.zip(parentDir);
			// step.7 删除最外级文件夹
			FileUtil.del(parentDir);
			// step.7 保存打包记录到数据库
			savePackageRecord(parentZipFile,parentDir+".zip","2",begin);
			return new AsyncResult<String>("按照项目的Id打包成功");
		}catch (Exception ex){
			return new AsyncResult<String>("按照项目的Id打包失败，失败原因："+ex.getMessage());
		}
	}
	/**
	 * 按照行政区打包
	 * @param vo
	 * @return
	 */
	@Async
	public Future<String> packageByRegion(ProjectPackageVo vo) throws InterruptedException{
		long begin = System.currentTimeMillis();
		try{
			// 根据日期创建最外级文件夹
			String dateNow = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
			String parentDir = RuoYiConfig.getPackagePath() + dateNow + "_Export";
			FileUtil.mkdir(parentDir);
			// step.1 循环行政区列表
			for (String regionCode : vo.getRegionList()){
				String regionPath = parentDir+"/"+regionCode;
				// step.1.1 循环年度
				for (String year : vo.getYearList()){
					String yearPath = regionPath+"/"+year;
					// step.1.2 循环季度
					for (String quarter : vo.getQuarterList()){
						String quarterPath = yearPath+"/"+quarter;
						List<DataPatrolTask> taskList = getPatrolTaskList(regionCode,year,quarter,vo);
						for (DataPatrolTask task : taskList){
							ProjectPackageVerifyVo verifyVo = packageVerify(task.getId());
							//if(!verifyVo.getIsSuccess())return AjaxResult.error(verifyVo.getErrorMsg());
							// step.2 赋值会用到的参数
							DataPatrolTask patrolTask = verifyVo.getPatrolTask();
							List<DataFileCatalog> catalogList = verifyVo.getCatalogList();
							String supervisionNo = verifyVo.getSupervisionNo();
							String projectPath = RuoYiConfig.getPackageTempPath()+supervisionNo;
							supervisionNo += verifyVo.getPatrolTask().getProjectName();
							supervisionNo = supervisionNo.replace("/", "_");
							String HB_CR ="";
							String ContractNo = verifyVo.getPatrolTask().getContractNo();
							if(ContractNo==null){
								ContractNo="";
							}
							if(ContractNo.startsWith("HB")){
								HB_CR="HB";
							}else if(ContractNo.startsWith("CR")){
								HB_CR="CR";
							}else if(ContractNo.startsWith("LQ")){
								HB_CR="";
							}else if(ContractNo.startsWith("西山出")){
								HB_CR="CR";
							}
							// step.3 获取压缩包文件
							File zipFile =  getZipFile(projectPath,catalogList,patrolTask);
							// step.4 删除未打包的临时文件夹
							FileUtil.del(projectPath);
							// step.5 移动zip文件到最外级文件夹
							String packagePath = quarterPath+"/"+supervisionNo+"_"+HB_CR+".zip";
							File quarterFile = FileUtil.file(packagePath);
							FileUtil.move(zipFile,quarterFile,true);
						}
					}
				}
			}
			// step.6 压缩最外级文件夹为zip文件
			File parentZipFile = ZipUtil.zip(parentDir);
			// step.7 删除最外级文件夹
			FileUtil.del(parentDir);
			// step.8 保存打包记录到数据库
			savePackageRecord(parentZipFile,parentDir+".zip","1",begin);
			return new AsyncResult<String>("按照行政区打包成功");
		}catch (Exception ex){
			return new AsyncResult<String>("按照行政区打包失败，失败原因："+ex.getMessage());
		}

	}

	/**
	 * 获取单个项目的压缩包
	 * @param projectPath
	 * @param catalogList
	 * @param patrolTask
	 * @return
	 */
	private File getZipFile(String projectPath,List<DataFileCatalog> catalogList,DataPatrolTask patrolTask){
		String uploadPath = RuoYiConfig.getProfile();
		String rootPath = projectPath + "/项目附件/";
		FileUtil.mkdir(rootPath);
		// 先创建parentId等于0的文件夹并拼接到目录
		DataFileCatalog parentCatalog = getParentCatalog(catalogList);
		String inSideTempPath = rootPath + parentCatalog.getName();

		int fileCount = 0;
		for (DataFileCatalog fileCatalog : catalogList){
			// 遍历文件目录
			if (fileCatalog.getParentId().equals(parentCatalog.getId())){
				String childPath = inSideTempPath + "/"+fileCatalog.getName();
				// 获取对应的内业附件列表
				List<DataAttachment> attachmentList = getInSideAttachmentList(patrolTask.getId(),fileCatalog.getId());
				for (DataAttachment attachment : attachmentList){
					String fileSrcPath = uploadPath + attachment.getLocation();
					String fileDestPath = childPath + "/"+attachment.getRemark();
					if (FileUtil.exist(fileSrcPath)){
						FileUtil.copy(fileSrcPath,fileDestPath,true);
						fileCount++;
					}

				}
			}
		}
		// step.4 获取项目的附件
		if(patrolTask.getProjectType().equals("0")){
			String outSideTempPath = projectPath + "/举证照片";
			FileUtil.mkdir(outSideTempPath);
			// step.4.1 闲置土地获取内业和外业巡查附件并创建存放举证照片的文件夹
			List<DataAttachment> ouSideAttachmentList = getOutSideAttachmentList(patrolTask.getId());
			for (DataAttachment attachment : ouSideAttachmentList){
				String fileSrcPath = uploadPath + attachment.getLocation();
				String fileDestPath = outSideTempPath + "/"+attachment.getRemark();
				if (FileUtil.exist(fileSrcPath)){
					FileUtil.copy(fileSrcPath,fileDestPath,true);
					fileCount++;
				}
			}
		}

		return ZipUtil.zip(projectPath);
	}

	/**
	 * 保存记录
	 * @param zipFile
	 * @param zipType
	 */
	private DataExportRecord savePackageRecord(File zipFile, String zipUrl, String zipType, Long start){
		DataExportRecord record = new DataExportRecord();
		long end = System.currentTimeMillis();
		record.setStartTime(DateUtil.date(start));
		record.setEndTime(DateUtil.date(end));
		long costTime = end - start;
		record.setTakeUpTime("耗时:"+costTime+"ms");
		record.setZipName(zipFile.getName());
		record.setZipSize(FileUtil.size(zipFile));
		int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
		String currentDir = StrUtil.subSuf(zipUrl, dirLastIndex);
		String pathFileName = Constants.RESOURCE_PREFIX + "/" + currentDir;
		record.setZipUrl(pathFileName);
		record.setZipType(zipType);
		exportRecordService.save(record);
		return record;
	}

	/**
	 * 获取符合条件的任务列表
	 * @param region
	 * @param year
	 * @param quarter
	 * @param vo
	 * @return
	 */
	private List<DataPatrolTask> getPatrolTaskList(String region,String year,String quarter,ProjectPackageVo vo){
		List<DataPatrolTask> taskList = new ArrayList<>();
		String projectType = vo.getProjectType();
		List<String> idList = new ArrayList<>();
		if (projectType.equals("0")){
			idList = getIdleLandIdList(region,year,quarter);
		}else if (projectType.equals("1")){
			// 只要批而未供
			idList = getNotProvideIdList(region,year,quarter);
		}else if (projectType.equals("2")){
			// 都要
			List<String> idleIdList = getIdleLandIdList(region,year,quarter);
			List<String> notProvideIdList = getNotProvideIdList(region,year,quarter);
			if (idleIdList.size() == 0 && notProvideIdList.size() == 0){
				// 两个都为空就不合并了
			}else{
				idList = Stream.of(idleIdList, notProvideIdList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
			}
		}
		if (idList.size() != 0){
			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<>();
			if (projectType.equals("2")){
				taskQueryWrapper.in(DataPatrolTask :: getProjectType, Arrays.asList("0","1"));
			}else{
				taskQueryWrapper.eq(DataPatrolTask :: getProjectType,projectType);
			}
			taskQueryWrapper.in(DataPatrolTask :: getIdleLandId,idList);
			taskQueryWrapper.eq(DataPatrolTask :: getIsPatrol,"1");
			taskList = patrolTaskService.list(taskQueryWrapper);
		}
		return taskList;
	}

	/**
	 * 获取闲置土地IdList
	 * @param region
	 * @param year
	 * @param quarter
	 * @return
	 */
	private List<String> getIdleLandIdList(String region,String year,String quarter){
		// 只要闲置土地
		LambdaQueryWrapper<DataIdleLand> idleQueryWrapper = new LambdaQueryWrapper<>();
		idleQueryWrapper.eq(DataIdleLand :: getCountyCode,region)
			.eq(DataIdleLand :: getYear,year)
			.eq(DataIdleLand :: getQuarter,quarter);
		List<DataIdleLand> idleLands = idleLandService.list(idleQueryWrapper);
		// 获取id列表
		return idleLands.stream().map(DataIdleLand::getId).collect(Collectors.toList());
	}

	/**
	 * 获取批而未供IdList
	 * @param region
	 * @param year
	 * @param quarter
	 * @return
	 */
	private List<String> getNotProvideIdList(String region,String year,String quarter){
		// 只要闲置土地
		LambdaQueryWrapper<DataLandNotProvided> notProvideQueryWrapper = new LambdaQueryWrapper<>();
		notProvideQueryWrapper.eq(DataLandNotProvided :: getCountyCode,region)
			.eq(DataLandNotProvided :: getYear,year)
			.eq(DataLandNotProvided :: getQuarter,quarter);
		List<DataLandNotProvided> notProvideds = notProvidedService.list(notProvideQueryWrapper);
		// 获取id列表
		return notProvideds.stream().map(DataLandNotProvided::getId).collect(Collectors.toList());
	}

	/**
	 * 根据taskId验证打包的项目
	 * @param taskId
	 * @return
	 */
	private ProjectPackageVerifyVo packageVerify(String taskId){
		ProjectPackageVerifyVo vo = new ProjectPackageVerifyVo();
		vo.setIsSuccess(false);
		// step.1 先根据taskId查询是否有对应的任务
		DataPatrolTask patrolTask = patrolTaskService.getById(taskId);
		if (patrolTask == null) {
			vo.setErrorMsg("根据任务编号"+taskId+"没有匹配到对应的任务记录");
			return vo;
		}
		vo.setPatrolTask(patrolTask);
		if (!patrolTask.getIsPatrol().equals("1")) {
			vo.setErrorMsg("任务编号为"+taskId+"的任务尚未审核完成，请审核完成后再打包");
			return vo;
		}
		// step.2 获取项目的文件目录并创建文件夹
		List<DataFileCatalog> catalogList = getCatalogList(patrolTask);
		if (catalogList.size() == 0) {
			vo.setErrorMsg("任务编号为"+taskId+"的任务没有匹配到对应的目录结构");
			return vo;
		}
		vo.setCatalogList(catalogList);
		// step.3 获取电子监管号
		String supervisionNo = getSupervisionNo(patrolTask);
		if (StrUtil.isBlank(supervisionNo)) {
			vo.setErrorMsg("任务编号为"+taskId+"的任务获取电子监管号失败，请检查对应的项目是否有电子监管号");
			return vo;
		}
		vo.setSupervisionNo(supervisionNo);
		vo.setIsSuccess(true);
		return vo;
	}

	/**
	 * 获取电子监管号
	 * @param patrolTask
	 * @return
	 */
	private String getSupervisionNo(DataPatrolTask patrolTask){
		String supervisionNo = "";
		if (patrolTask.getProjectType().equals("0")){
			DataIdleLand idleLand = idleLandService.getById(patrolTask.getIdleLandId());
			if (idleLand == null) AjaxResult.error("该任务没有匹配到对应的闲置土地记录");
			supervisionNo = idleLand.getSupervisionNo();
		}else{
			DataLandNotProvided notProvided = notProvidedService.getById(patrolTask.getIdleLandId());
			if (notProvided == null) AjaxResult.error("该任务没有匹配到对应的批而未供记录");
			supervisionNo = notProvided.getSupervisionNo();
		}
		return supervisionNo;
	}

	/**
	 * 获取项目对应的文件目录
	 * @param patrolTask
	 * @return
	 */
	private List<DataFileCatalog> getCatalogList(DataPatrolTask patrolTask){
		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
		if (patrolTask.getProjectType().equals("1")){
			queryWrapper.eq(DataFileCatalog::getCatalogType, "批而未供");
		}else{

			String catalogType = idleLandService.getDigestionTypeByTaskId(patrolTask.getId());
			// 通过项目的消化类型找到对应的固定的父节点
			String parentCatalog = getParentCatalog(catalogType);
			catalogType = StringUtils.isNotBlank(parentCatalog) ? parentCatalog : catalogType;
			// 先按照文件目录类型catalogType查询目录数据
			queryWrapper.eq(DataFileCatalog::getCatalogType, catalogType);
		}
		queryWrapper.orderByAsc(DataFileCatalog :: getOrderNum);
		return fileCatalogService.list(queryWrapper);
	}

	/**
	 * 根据消化类型获取目录类型
	 * @param tempCatalog
	 * @return
	 */
	private String getParentCatalog(String tempCatalog){
		if (StringUtils.isBlank(tempCatalog)) return "";
		DataDigestType digestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType :: getTypeName,tempCatalog),false);
		if (digestType == null) return "";
		DataDigestType parentDigestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType :: getId,digestType.getParentId()),false);
		if (parentDigestType == null) return "";
		return parentDigestType.getTypeName();
	}

	/**
	 * 获取最顶级目录
	 * @param catalogList
	 * @return
	 */
	private DataFileCatalog getParentCatalog(List<DataFileCatalog> catalogList){
		DataFileCatalog fileCatalog = null;
		if (catalogList.size() != 0){
			for (DataFileCatalog catalog : catalogList){
				if (catalog.getParentId().equals("0")){
					fileCatalog = catalog;
					break;
				}
			}
		}
		return fileCatalog;
	}

	/**
	 * 根据任务编号和目录编号获取内业附件列表
	 * @param taskId
	 * @param catalogId
	 * @return
	 */
	private List<DataAttachment> getInSideAttachmentList(String taskId,String catalogId){
		LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataAttachment :: getPatrolTaskId,taskId)
			.eq(DataAttachment:: getCatalogId,catalogId)
			.eq(DataAttachment :: getIsSiteData,"0");
		return attachmentService.list(queryWrapper);
	}
	/**
	 * 根据任务编号获取外业举证照片列表
	 * @param taskId
	 * @return
	 */
	private List<DataAttachment> getOutSideAttachmentList(String taskId){
		LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataAttachment :: getPatrolTaskId,taskId)
			.eq(DataAttachment :: getIsSiteData,"1");
		return attachmentService.list(queryWrapper);
	}

}
