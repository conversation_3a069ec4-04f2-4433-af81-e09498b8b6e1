package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: PatrolTaskUserVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/7/12 15:32
 * @Version: 1.0.0
 **/
@Data
public class PatrolTaskUserVo {
	/**
	 * 任务编号
	 */
	@ApiModelProperty(value = "任务编号")
	private String taskId;

	/**
	 * 巡查组
	 */
	@ApiModelProperty(value = "巡查组")
	private String patrolGroup;

	/**
	 * 巡查组人员
	 */
	@ApiModelProperty(value = "巡查组人员")
	private List<IdleAllotTaskUserVo> userList;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
