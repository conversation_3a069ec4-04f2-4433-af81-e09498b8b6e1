package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 巡查附件 对象 data_attachment
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_attachment")
public class DataAttachment implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 附件名称
	 */
	@ApiModelProperty(value = "附件名称")
	@Excel(name = "附件名称")
	private String name;

	/**
	 * 附件大小
	 */
	@ApiModelProperty(value = "附件大小")
	@Excel(name = "附件大小")
	private String size;

	/**
	 * 附件类型
	 */
	@ApiModelProperty(value = "附件类型")
	@Excel(name = "附件类型")
	private String type;

	/**
	 * 访问地址
	 */
	@ApiModelProperty(value = "访问地址")
	@Excel(name = "访问地址")
	private String url;

	/**
	 * 存储位置
	 */
	@ApiModelProperty(value = "存储位置")
	@Excel(name = "存储位置")
	private String location;

	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	@Excel(name = "巡查任务编号")
	private String patrolTaskId;

	/**
	 * 目录编号
	 */
	@ApiModelProperty(value = "目录编号")
	private String catalogId;

	/**
	 * 是否现场照片或视频
	 */
	@ApiModelProperty(value = "是否现场照片或视频")
	private String isSiteData;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Excel(name = "备注")
	private String remark;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	@Excel(name = "状态")
	private String status;

	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	@Excel(name = "经度")
	private Double longitude;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	@Excel(name = "纬度")
	private Double latitude;

	/**
	 * 角度
	 */
	@ApiModelProperty(value = "角度")
	@Excel(name = "角度")
	private Double angle;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	@Excel(name = "创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
