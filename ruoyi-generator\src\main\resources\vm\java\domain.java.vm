package ${packageName}.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("${tableName}")
public class ${ClassName} implements Serializable{

private static final long serialVersionUID=1L;

#foreach ($column in $columns)

/** $column.columnComment */
    #if($column.javaField=="createBy"||$column.javaField=="createTime")
    @TableField(fill = FieldFill.INSERT)
    #end
    #if($column.javaField=="updateBy"||$column.javaField=="updateTime")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    #end
    #if($column.javaField=='delFlag')
    @TableLogic
    #end
    #if($column.javaField=='version')
    @Version
    #end
    #if($column.isPk==1)
    @TableId(value = "$column.columnName")
    #end
private $column.javaType $column.javaField;
#end

}
