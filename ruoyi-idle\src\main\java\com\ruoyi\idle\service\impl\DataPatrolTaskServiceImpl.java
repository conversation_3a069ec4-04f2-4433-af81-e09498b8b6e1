package com.ruoyi.idle.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.bo.MyTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import com.ruoyi.idle.mapper.DataIdleLandMapper;
import com.ruoyi.idle.mapper.DataLandNotProvidedMapper;
import com.ruoyi.idle.mapper.DataPatrolTaskMapper;
import com.ruoyi.idle.mapper.DataVerifyRecordMapper;
import com.ruoyi.idle.service.IDataPatrolTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.token.TokenService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡查任务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataPatrolTaskServiceImpl extends ServicePlusImpl<DataPatrolTaskMapper, DataPatrolTask> implements IDataPatrolTaskService {

	@Autowired
	DataVerifyRecordMapper verifyRecordMapper;

	@Autowired
	DataPatrolTaskMapper patrolTaskMapper;

	@Autowired
	DataIdleLandMapper idleLandMapper;

	@Autowired
	DataLandNotProvidedMapper notProvidedMapper;

	@Override
	public DataPatrolTask queryById(String id) {
		return getVoById(id, DataPatrolTask.class);
	}

	@Override
	public List<Map<String, Object>> countByPatrol(String pId) {
		return patrolTaskMapper.countByPatrol(pId);
	}
	@Override
	public Map<String, String> appPulLTaskStatusist(  String regionCode,   String userId){

		//lqw.eq(StrUtil.isNotBlank(bo.getRegionCode()), DataPatrolTask::getRegionCode, bo.getRegionCode());
		Map<String, String> userMap = patrolTaskMapper.selectList(
			new LambdaQueryWrapper<DataPatrolTask>()
				.eq(DataPatrolTask::getRegionCode, regionCode)
				.eq(DataPatrolTask::getPublishStatus, "1")
				.ne(DataPatrolTask::getIsPatrol, "0")
				.select(DataPatrolTask::getId, DataPatrolTask::getIsPatrol) // 指定查询的字段
		) .stream()
			.collect(Collectors.toMap(DataPatrolTask::getId, DataPatrolTask::getIsPatrol)); // 直接转换，注意这里的getId和getName要与实体类中的方法名相对应
		return userMap;
//		return patrolTaskMapper.appPulList(regionCode,userId);
	}
	@Override
	public List<DataPatrolTask> appPulList(  String regionCode,   String userId){
		return patrolTaskMapper.appPulList(regionCode,userId);
	}

	@Override
	public TableDataInfo<DataPatrolTask> queryPageList(DataPatrolTaskQueryBo bo) {

		LambdaQueryWrapper<DataPatrolTask> queryWrapper1 = buildQueryWrapper(bo);

		PagePlus<DataPatrolTask, DataPatrolTask> result = pageVo(PageUtils.buildPagePlus(), queryWrapper1, DataPatrolTask.class);
		List<DataPatrolTask> xxx = new ArrayList<>();
		if(result==null || result.getRecordsVo()==null){
			 return PageUtils.buildDataInfo(xxx,0);
		}
		if(  result.getRecordsVo().size()<1){
			return PageUtils.buildDataInfo(xxx,0);
		}
		// 查询
		if (StringUtils.isNotBlank(bo.getYear()) && bo.getProjectType().equals("0")){
			//List<DataPatrolTask> xxx = new ArrayList<>();

			List<String> idleLandIdIds =  result.getRecordsVo().stream().map(DataPatrolTask::getIdleLandId).collect(Collectors.toList());
			LambdaQueryWrapper<DataIdleLand> lqwidleLand = Wrappers.lambdaQuery();
			lqwidleLand.in(DataIdleLand::getId,idleLandIdIds);
			List<DataIdleLand> idleLandList = idleLandMapper.selectList(lqwidleLand);
			for (DataIdleLand idleLand : idleLandList){
				for (DataPatrolTask task : result.getRecordsVo()){
					if (idleLand != null){
						String x1 = idleLand.getId();
						String x2 = task.getIdleLandId();
						if(x1.equals(x2) ){
							task.setContractNo(idleLand.getContractNo());
							task.setSupervisionNo(idleLand.getSupervisionNo());
							task.setDigestionType(idleLand.getDigestionType());
							task.setYear(idleLand.getYear());
						}
					}
//					String taskYear = task.getYear();
//					String boYear = bo.getYear();
//					if (taskYear.equals(boYear)){
////						xxx.add(task);
//					}
				}

			}
/*
			for (DataPatrolTask task : result.getRecordsVo()){
				DataIdleLand idleLand = idleLandMapper.selectById(task.getIdleLandId());
				if (idleLand != null){
					task.setContractNo(idleLand.getContractNo());
					task.setSupervisionNo(idleLand.getSupervisionNo());
					task.setDigestionType(idleLand.getDigestionType());
					task.setYear(idleLand.getYear());
				}
				String taskYear = task.getYear();
				String boYear = bo.getYear();
				if (taskYear.equals(boYear)){
					xxx.add(task);
				}
			}
			System.out.println(xxx);
			*/
			return PageUtils.buildDataInfo(result.getRecordsVo(),result.getTotal());
		}else if (StringUtils.isNotBlank(bo.getYear()) && bo.getProjectType().equals("1")){
			// 批而未供
			List<String> idleLandIdIds =  result.getRecordsVo().stream().map(DataPatrolTask::getIdleLandId).collect(Collectors.toList());
			LambdaQueryWrapper<DataLandNotProvided> lqwDataLandNotProvided = Wrappers.lambdaQuery();
			lqwDataLandNotProvided.in(DataLandNotProvided::getId,idleLandIdIds);
			List<DataLandNotProvided> dataLandNotProvidedList = notProvidedMapper.selectList(lqwDataLandNotProvided);
			for (DataLandNotProvided idleLand : dataLandNotProvidedList){
				for (DataPatrolTask task : result.getRecordsVo()){
					if (idleLand != null){
						String x1 = idleLand.getProjectName();
						String x2 = task.getProjectName();
						if(x1.equals(x2) ){
							task.setContractNo(idleLand.getContractNo());
							task.setSupervisionNo(idleLand.getSupervisionNo());
//						task.setDigestionType(idleLand.getDigestionType());
							task.setYear(idleLand.getYear());
						}
					}
//					String taskYear = task.getYear();
//					String boYear = bo.getYear();
//					if (taskYear.equals(boYear)){
////						xxx.add(task);
//					}
				}

			}
/*			List<DataPatrolTask> xxx = new ArrayList<>();
			for (DataPatrolTask task : result.getRecordsVo()){
				DataLandNotProvided notProvided = notProvidedMapper.selectById(task.getIdleLandId());
				if (notProvided != null){
					task.setDigestionType("批而未供");
					task.setContractNo(notProvided.getContractNo());
					task.setSupervisionNo(notProvided.getSupervisionNo());
					task.setYear(notProvided.getYear());
				}
				String taskYear = task.getYear();
				String boYear = bo.getYear();
				if (taskYear.equals(boYear)){
					xxx.add(task);
				}
			}*/
			return PageUtils.buildDataInfo(result.getRecordsVo(),result.getTotal());
		}
		if (StringUtils.isNotBlank(bo.getProjectType()) && bo.getProjectType().equals("0")) {
			// 闲置土地
			List<String> idleLandIdIds =  result.getRecordsVo().stream().map(DataPatrolTask::getIdleLandId).collect(Collectors.toList());
			LambdaQueryWrapper<DataIdleLand> lqwidleLand = Wrappers.lambdaQuery();
			lqwidleLand.in(DataIdleLand::getId,idleLandIdIds);
			List<DataIdleLand> idleLandList = idleLandMapper.selectList(lqwidleLand);
			for (DataIdleLand idleLand : idleLandList){
				for (DataPatrolTask task : result.getRecordsVo()){
					if (idleLand != null){
						String x1 = idleLand.getId();
						String x2 = task.getIdleLandId();
						if(x1.equals(x2) ){
							task.setContractNo(idleLand.getContractNo());
							task.setSupervisionNo(idleLand.getSupervisionNo());
							task.setDigestionType(idleLand.getDigestionType());
							task.setYear(idleLand.getYear());
						}
					}
//					String taskYear = task.getYear();
//					String boYear = bo.getYear();
//					if (taskYear.equals(boYear)){
//						xxx.add(task);
//					}
				}

			}
/*			for (DataPatrolTask task : result.getRecordsVo()){
				DataIdleLand idleLand = idleLandMapper.selectById(task.getIdleLandId());
				if (idleLand != null){
					task.setContractNo(idleLand.getContractNo());
					task.setSupervisionNo(idleLand.getSupervisionNo());
					task.setDigestionType(idleLand.getDigestionType());
				}
			}
			*/
		}else if (StringUtils.isNotBlank(bo.getProjectType()) && bo.getProjectType().equals("1")){
			// 批而未供
			List<String> idleLandIdIds =  result.getRecordsVo().stream().map(DataPatrolTask::getIdleLandId).collect(Collectors.toList());
			LambdaQueryWrapper<DataLandNotProvided> lqwDataLandNotProvided = Wrappers.lambdaQuery();
			lqwDataLandNotProvided.in(DataLandNotProvided::getId,idleLandIdIds);
			List<DataLandNotProvided> dataLandNotProvidedList = notProvidedMapper.selectList(lqwDataLandNotProvided);
			for (DataLandNotProvided idleLand : dataLandNotProvidedList){
				for (DataPatrolTask task : result.getRecordsVo()){
					if (idleLand != null){
						String x1 = idleLand.getProjectName();
						String x2 = task.getProjectName();
						if(x1.equals(x2) ){
							task.setContractNo(idleLand.getContractNo());
							task.setSupervisionNo(idleLand.getSupervisionNo());
//						task.setDigestionType(idleLand.getDigestionType());
							task.setYear(idleLand.getYear());
							task.setDigestionType("批而未供");
						}
					}

				}

			}
/*
			for (DataPatrolTask task : result.getRecordsVo()){
				DataLandNotProvided notProvided = notProvidedMapper.selectById(task.getIdleLandId());
				if (notProvided != null){
					task.setContractNo(notProvided.getContractNo());
					task.setSupervisionNo(notProvided.getSupervisionNo());
					task.setDigestionType("批而未供");
				}
			}
			*/
		}
		if (StringUtils.isNotBlank(bo.getIsPatrol()) && bo.getIsPatrol().equals("1")){
			// 查询已完成巡查的需要查询对应的认定处置结果
			List<String> taskIds =  result.getRecordsVo().stream().map(DataPatrolTask::getId).collect(Collectors.toList());
			LambdaQueryWrapper<DataVerifyRecord> lqwDataVerifyRecord = Wrappers.lambdaQuery();
			lqwDataVerifyRecord.in(DataVerifyRecord::getTaskId,taskIds);
			List<DataVerifyRecord> dataVerifyRecordList = verifyRecordMapper.selectList(lqwDataVerifyRecord);
			for (DataVerifyRecord dataVerifyRecord : dataVerifyRecordList){
				for (DataPatrolTask task : result.getRecordsVo()){
					if (dataVerifyRecord != null && task!=null){
						 if(dataVerifyRecord.getTaskId().equals(task.getId())){
							 task.setHandleIsCorrect(dataVerifyRecord.getHandleIsCorrect());
						 }
					}

				}

			}

/*			for (DataPatrolTask task : result.getRecordsVo()){
				List<DataVerifyRecord> verifyList = verifyRecordMapper.selectList(new LambdaQueryWrapper< DataVerifyRecord>().eq(DataVerifyRecord :: getTaskId,task.getId()));
				if (verifyList.size() != 0){
					task.setHandleIsCorrect(verifyList.get(0).getHandleIsCorrect());
				}
			}*/
		}

		return PageUtils.buildDataInfo(result);
	}

	@Override
	public TableDataInfo<DataPatrolTask> queryPageList1(DataPatrolTaskQueryBo bo) {
		IPage<DataPatrolTask> page = new Page<>(Objects.isNull(bo.getPageNum()) ? 1 : bo.getPageNum(), Objects.isNull(bo.getPageSize()) ? 10 : bo.getPageSize());
		IPage<DataPatrolTask> taskIPage = patrolTaskMapper.selectTaskList(page, bo);
		return TableDataInfo.build(taskIPage);
	}

	@Override
	public TableDataInfo<DataPatrolTaskVo> submitTaskList(DataPatrolTaskQueryBo bo) {
		IPage<DataPatrolTask> page = new Page<>(Objects.isNull(bo.getPageNum()) ? 1 : bo.getPageNum(), Objects.isNull(bo.getPageSize()) ? 10 : bo.getPageSize());
		IPage<DataPatrolTaskVo> taskIPage = patrolTaskMapper.submitTaskList(page, bo);
		return TableDataInfo.build(taskIPage);
	}

	@Override
	public void submitTaskListImages(DataPatrolTaskQueryBo bo) throws FileNotFoundException {
		String dateNow = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
		// 根据日期创建最外级文件夹
		String parentDir = "D:\\举证照片"+dateNow+"\\";
		FileUtil.mkdir(parentDir);
		String profilePath = RuoYiConfig.getProfile();
		List<Map<String,Object>> files = patrolTaskMapper.submitTaskListImages(bo);
		for (Map<String,Object> file : files) {
			//以下代码为获取图片inputStream
			Object jzybh = file.get("jzybh");
			String s = "";
			if(ObjectUtil.isNotNull(jzybh)&&ObjectUtil.isNotEmpty(jzybh)){
				s = jzybh.toString();
			}else {
				s= file.get("id").toString()+file.get("xxdz").toString();
			}
			String fileSrcPath = profilePath + file.get("location");
			if (FileUtil.exist(fileSrcPath)){
				FileUtil.writeFromStream(new FileInputStream(fileSrcPath),new File(parentDir+s),true);
			}

		}

	}

	@Override
	public List<Map<String, Object>> getImages(DataPatrolTaskQueryBo bo) {
		return patrolTaskMapper.submitTaskListImages(bo);
	}

	@Override
	public void dowloadToZip(DataPatrolTaskQueryBo bo, HttpServletResponse response) throws Exception {
		int i = 0;
		String profilePath = RuoYiConfig.getProfile();
		List<Map<String,Object>> files = patrolTaskMapper.submitTaskListImages(bo);
		List<Map<String,Object>> notNullfiles = new ArrayList<>();
		for (Map<String,Object> file : files) {
			 Object location = profilePath + file.get("location");
			 if (ObjectUtil.isNotNull(location)&&ObjectUtil.isNotEmpty(location)){
			 	String s = location.toString();
				  File f = new File(s);
				 if(f.exists()){
					 notNullfiles.add(file);
				 }
			 }
		}
		//如果有附件 进行zip处理
		if (notNullfiles != null && notNullfiles.size() > 0) {
			//被压缩文件流集合
			InputStream[] srcFiles = new InputStream[notNullfiles.size()];
			//被压缩文件名称
			String[] srcFileNames = new String[notNullfiles.size()];
			for (Map<String,Object> file : notNullfiles) {
				//以下代码为获取图片inputStream
				String url = profilePath  + file.get("location");
				Object jzybh = file.get("jzybh");
				String s = "";
				if(ObjectUtil.isNotNull(jzybh)&&ObjectUtil.isNotEmpty(jzybh)){
					s = jzybh.toString();
				}else {
					s= file.get("id").toString()+file.get("regionName").toString();
				}
				InputStream inputStream = new FileInputStream(url);
				if (inputStream == null) {
					continue;
				}
				//塞入流数组中
				srcFiles[i] = inputStream;
				srcFileNames[i] = s+"\\"+file.get("name").toString()+"."+file.get("type").toString();
				i++;
			}
			String dateNow = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(dateNow +"举证照片下载.zip", "UTF-8"));
			//多个文件压缩成压缩包返回
			ZipUtil.zip(response.getOutputStream(), srcFileNames, srcFiles);

		}else {
			 throw new Exception("所选数据无照片");
		}

	}

	@Override
	public TableDataInfo<DataPatrolTask> queryPageToDoList(MyTaskQueryBo bo, SysUser sysUser, String type) {
		IPage<DataPatrolTask> page = new Page<>(Objects.isNull(bo.getPageNum()) ? 1 : bo.getPageNum(), Objects.isNull(bo.getPageSize()) ? 10 : bo.getPageSize());
		IPage<DataPatrolTask> taskIPage = patrolTaskMapper.selectToDoList(page, bo,sysUser.getUserId(),type);
		return TableDataInfo.build(taskIPage);
	}

	@Override
	public List<DataPatrolTask> queryList(DataPatrolTaskQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataPatrolTask.class);
	}

	private LambdaQueryWrapper<DataPatrolTask> buildQueryWrapper(DataPatrolTaskQueryBo bo) {
		LambdaQueryWrapper<DataPatrolTask> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getIdleLandId() != null, DataPatrolTask::getIdleLandId, bo.getIdleLandId());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataPatrolTask::getProjectName, bo.getProjectName());
		lqw.eq(StrUtil.isNotBlank(bo.getProjectType()), DataPatrolTask::getProjectType, bo.getProjectType());
		lqw.eq(StrUtil.isNotBlank(bo.getPatrolType()), DataPatrolTask::getPatrolType, bo.getPatrolType());
		lqw.like(StringUtils.isNotBlank(bo.getMeetingTime()), DataPatrolTask::getMeetingTime, bo.getMeetingTime());
		lqw.eq(StringUtils.isNotBlank(bo.getMeetingEndTime()), DataPatrolTask::getMeetingEndTime, bo.getMeetingEndTime());
		lqw.eq(StrUtil.isNotBlank(bo.getRegionCode()), DataPatrolTask::getRegionCode, bo.getRegionCode());
		lqw.like(StrUtil.isNotBlank(bo.getRegionName()), DataPatrolTask::getRegionName, bo.getRegionName());
		lqw.eq(StrUtil.isNotBlank(bo.getPatrolGroup()), DataPatrolTask::getPatrolGroup, bo.getPatrolGroup());
		lqw.eq(StrUtil.isNotBlank(bo.getPublishStatus()), DataPatrolTask::getPublishStatus, bo.getPublishStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getIsPatrol()), DataPatrolTask::getIsPatrol, bo.getIsPatrol());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataPatrolTask::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataPatrolTask::getStatus, bo.getStatus());

		lqw.eq(StringUtils.isNotBlank(bo.getYear()), DataPatrolTask::getYear, bo.getYear());

		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataPatrolTask::getCreateBy, bo.getCreateBy());
		lqw.like(bo.getCreateTime() != null, DataPatrolTask::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataPatrolTask::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataPatrolTask::getUpdateTime, bo.getUpdateTime());
		// 根据创建时间排序
		lqw.orderByDesc(DataPatrolTask::getCreateTime);


		return lqw;
	}

	@Override
	public Boolean insert(DataPatrolTask bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataPatrolTask bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataPatrolTask entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public List<DataPatrolTask> getAllPatrolTaskList(String startTime) {
		return patrolTaskMapper.selectAllPatrolTaskList(startTime);
	}

	@Override
	public List<DataPatrolTaskVo> appCheckList(String regionCode) {
		return patrolTaskMapper.appCheckList(regionCode);
	}
}
