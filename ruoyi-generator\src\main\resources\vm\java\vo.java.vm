package ${packageName}.vo;

import com.ruoyi.common.annotation.Excel;
    #foreach ($import in $importList)
    import ${import};
    #end
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * ${functionName}视图对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@ApiModel("${functionName}视图对象")
public class ${ClassName}Vo {

    private static final long serialVersionUID = 1L;

    /** $pkColumn.columnComment */
    @ApiModelProperty("$pkColumn.columnComment")
    private ${pkColumn.javaType} ${pkColumn.javaField};

    #foreach ($column in $columns)
        #if($column.isList && $column.isPk!=1)
            /** $column.columnComment */
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
            @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
            #else
            @Excel(name = "${comment}")
            #end
        @ApiModelProperty("$column.columnComment")
        private $column.javaType $column.javaField;

        #end
    #end

}
