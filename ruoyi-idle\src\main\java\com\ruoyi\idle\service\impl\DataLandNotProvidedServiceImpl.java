package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.DataLandNotProvided;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.DataLandNotProvidedQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;
import com.ruoyi.idle.mapper.DataIdleLandMapper;
import com.ruoyi.idle.mapper.DataLandNotProvidedMapper;
import com.ruoyi.idle.mapper.DataPatrolTaskMapper;
import com.ruoyi.idle.mapper.SysRegionMapper;
import com.ruoyi.idle.service.IDataLandNotProvidedService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Collection;

/**
 * 批而未供Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataLandNotProvidedServiceImpl extends ServicePlusImpl<DataLandNotProvidedMapper, DataLandNotProvided> implements IDataLandNotProvidedService {

	private final DataLandNotProvidedMapper notProvidedMapper;

	private final DataPatrolTaskMapper taskMapper;

	private final SysRegionMapper regionMapper;


	@Override
	public DataLandNotProvided queryById(String id) {
		return getVoById(id, DataLandNotProvided.class);
	}

	@Override
	public TableDataInfo<DataLandNotProvided> queryPageList(DataLandNotProvidedQueryBo bo) {
		PagePlus<DataLandNotProvided, DataLandNotProvided> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataLandNotProvided.class);
		List<DataLandNotProvided> dataIdleLands = result.getRecordsVo();
		if (dataIdleLands.size() != 0){
			for (DataLandNotProvided idleLand : dataIdleLands){
				// 查询对应的任务状态
				DataPatrolTask task = taskMapper.selectOne(new LambdaQueryWrapper<DataPatrolTask>()
					.eq(DataPatrolTask ::getIdleLandId,idleLand.getId()));
				if (task != null){
					idleLand.setIsPatrol(task.getIsPatrol());
				}
			}
		}
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataLandNotProvided> queryList(DataLandNotProvidedQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataLandNotProvided.class);
	}

	private LambdaQueryWrapper<DataLandNotProvided> buildQueryWrapper(DataLandNotProvidedQueryBo bo) {
		LambdaQueryWrapper<DataLandNotProvided> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getCity()), DataLandNotProvided::getCity, bo.getCity());
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataLandNotProvided::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataLandNotProvided::getCountyName, bo.getCountyName());
		lqw.like(StrUtil.isNotBlank(bo.getSupervisionNo()), DataLandNotProvided::getSupervisionNo, bo.getSupervisionNo());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataLandNotProvided::getProjectName, bo.getProjectName());
		lqw.like(StrUtil.isNotBlank(bo.getContractNo()), DataLandNotProvided::getContractNo, bo.getContractNo());
		lqw.eq(bo.getProvidedArea() != null, DataLandNotProvided::getProvidedArea, bo.getProvidedArea());
		lqw.eq(bo.getIsAllot() != null, DataLandNotProvided::getIsAllot, bo.getIsAllot());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataLandNotProvided::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataLandNotProvided::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataLandNotProvided::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataLandNotProvided::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataLandNotProvided::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataLandNotProvided::getUpdateTime, bo.getUpdateTime());
		lqw.eq(bo.getSupplyType() != null, DataLandNotProvided::getSupplyType, bo.getSupplyType());
		lqw.like(bo.getAddress() != null, DataLandNotProvided::getAddress, bo.getAddress());
		lqw.like(bo.getLandUse() != null, DataLandNotProvided::getLandUse, bo.getLandUse());
		lqw.eq(bo.getParcelNum() != null, DataLandNotProvided::getParcelNum, bo.getParcelNum());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataLandNotProvided bo) {
		return save(bo);
	}

	@Override
	public Boolean updateByEditBo(DataLandNotProvided bo) {
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataLandNotProvided entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public List<StatisticsVo> unprovidedStat(String regionCode) {
		List<StatisticsVo> voList = new ArrayList<>();
		if (StringUtils.isNotBlank(regionCode)){
			// 按照指定行政区过滤
			StatisticsVo vo = getStatisticsVo(regionCode);
			voList.add(vo);
		}else {
			// 所有行政区
			// 获取所有行政区列表并循环获取
			LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(SysRegion :: getLevel,"2");
			List<SysRegion> regionList = regionMapper.selectList(queryWrapper);
			for (SysRegion region : regionList){
				StatisticsVo vo = getStatisticsVo(region.getAreaCode());
				voList.add(vo);
			}
		}
		return voList;
	}

	private Integer getTaskCount(String regionCode,String type){
		int count = 0;
		LambdaQueryWrapper<DataPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(StringUtils.isNotBlank(regionCode),DataPatrolTask :: getRegionCode,regionCode)
			.eq(DataPatrolTask :: getProjectType,"1")
			.eq(StringUtils.isNotBlank(type),DataPatrolTask :: getIsPatrol,type);
		count = taskMapper.selectCount(queryWrapper);
		return count;
	}
	private Integer getIdleCount(String regionCode,String type){
		int count = 0;
		LambdaQueryWrapper<DataLandNotProvided> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(StringUtils.isNotBlank(regionCode),DataLandNotProvided :: getCountyCode,regionCode)
			.eq(StringUtils.isNotBlank(type),DataLandNotProvided :: getIsAllot,type);
		count = notProvidedMapper.selectCount(queryWrapper);
		return count;
	}

	private StatisticsVo getStatisticsVo(String regionCode){
		StatisticsVo vo = new StatisticsVo();
		vo.setRegionCode(regionCode);
		LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(StringUtils.isNotBlank(regionCode),SysRegion :: getAreaCode,regionCode);
		SysRegion region = regionMapper.selectOne(queryWrapper);
		if (region != null)vo.setRegionName(region.getName());
		vo.setStatType("批而未供");
		vo.setTotalCount(getIdleCount(regionCode,""));
		vo.setAllocatedCount(getIdleCount(regionCode,"1"));
		vo.setUnAllocatedCount(getIdleCount(regionCode,"0"));
		vo.setUnPatrolledCount(getTaskCount(regionCode,"0"));
		vo.setPatrolledCount(getTaskCount(regionCode,"1"));
		vo.setAuditCount(getTaskCount(regionCode,"2"));
		return vo;
	}
}
