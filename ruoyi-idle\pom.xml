<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-vue-plus</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>2.4.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-idle</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>


    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>junit</groupId>-->
<!--            <artifactId>junit</artifactId>-->
<!--            <version>4.13.1</version>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.74</version>
        </dependency>
    </dependencies>

<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>osgeo</id>-->
<!--            <name>OSGeo Release Repository</name>-->
<!--            <url>https://repo.osgeo.org/repository/release/</url>-->
<!--            <snapshots>-->
<!--                <enabled>false</enabled>-->
<!--            </snapshots>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--        </repository>-->
<!--        <repository>-->
<!--            <id>osgeo-snapshot</id>-->
<!--            <name>OSGeo Snapshot Repository</name>-->
<!--            <url>https://repo.osgeo.org/repository/snapshot/</url>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--            </snapshots>-->
<!--            <releases>-->
<!--                <enabled>false</enabled>-->
<!--            </releases>-->
<!--        </repository>-->
<!--    </repositories>-->

</project>
