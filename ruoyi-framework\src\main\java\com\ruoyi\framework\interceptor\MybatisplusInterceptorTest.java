package com.ruoyi.framework.interceptor;

import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.SQLException;

public class MybatisplusInterceptorTest implements InnerInterceptor {


	@Override
	public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {

	}

	@Override
	public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) throws SQLException {

	}

	@Override
	public void beforePrepare(StatementH<PERSON><PERSON> sh, Connection connection, Integer transactionTimeout) {

	}
}
