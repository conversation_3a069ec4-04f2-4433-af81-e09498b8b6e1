package com.ruoyi.meet.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.idle.domain.DataMeetingRecord;
import com.ruoyi.idle.domain.DataMeetingUser;
import com.ruoyi.idle.service.IDataMeetingRecordService;
import com.ruoyi.idle.service.IDataMeetingUserService;
import com.ruoyi.meet.MeetVo;
import com.ruoyi.meet.MeetuserVo;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: MeetingRecordUtils
 * @Description: com.ruoyi.meet.utils
 * @Author: HongDeng
 * @Date: 2021/8/12 9:30
 * @Version: 1.0.0
 **/
public class MeetingRecordUtils {

	private final Logger log = LoggerFactory.getLogger("WebSocket");

	/**
	 * 会议管理的服务
	 */
	IDataMeetingRecordService iDataMeetingRecordService;
	/**
	 * 与会人员管理的服务
	 */
	IDataMeetingUserService iDataMeetingUserService;
	/**
	 * 用户管理的服务
	 */
	ISysUserService userService;

	public MeetingRecordUtils() {
		userService = SpringUtils.getBean(ISysUserService.class);
		iDataMeetingRecordService = SpringUtils.getBean(IDataMeetingRecordService.class);
		iDataMeetingUserService = SpringUtils.getBean(IDataMeetingUserService.class);
	}

	public void saveMeetingRecord(MeetVo meetVo) {
		if (null != meetVo) {
			DataMeetingRecord record = new DataMeetingRecord();
			record.setTaskId(meetVo.getPatrolTaskId());
			record.setMeetingUuid(meetVo.getMeetingUuid());
			record.setTaskName(meetVo.getProjectName());
			// 根据用户手机查询用户信息
			SysUser user = userService.selectUserByUserPhone(meetVo.getCreatePhone());
			if (null != user) {
				record.setCreateUserId(user.getUserId());
				record.setCreateUserName(user.getUserName());
				record.setCreateUserPhone(user.getPhonenumber());
			} else {
				record.setCreateUserName(meetVo.getCreatorName());
				record.setCreateUserPhone(meetVo.getCreatePhone());
			}
			record.setMeetingNum(meetVo.getMtNo());
			record.setMeetingLongId(meetVo.getMeetLongId());
			record.setLongitude(meetVo.getLongitude());
			record.setLatitude(meetVo.getLatitude());
			record.setDeleteFlag("0");
			record.setStatus("0");
			record.setCreateBy(SecurityUtils.getUsername());
			// 保存会议记录
			iDataMeetingRecordService.save(record);
			// 保存与会人员
			DataMeetingUser meetingUser = new DataMeetingUser();
			meetingUser.setMeetingId(record.getId());
			meetingUser.setUserId(user.getUserId());
			meetingUser.setUserName(user.getUserName());
			meetingUser.setUserPhone(user.getPhonenumber());
			meetingUser.setMeetingUuid(record.getMeetingUuid());
			meetingUser.setLongitude(meetVo.getLongitude());
			meetingUser.setLatitude(meetVo.getLatitude());
			meetingUser.setCreateBy(SecurityUtils.getUsername());
			iDataMeetingUserService.save(meetingUser);
			List<MeetuserVo> meetUserList = meetVo.getPnoneUsers();
			if (null != meetUserList && meetUserList.size() != 0) {
				// 需要循环保存对应的与会人员
				for (MeetuserVo meetuserVo : meetUserList) {
					SysUser tempUser = userService.selectUserByUserPhone(meetuserVo.getPhone());
					if (null != tempUser) {
						DataMeetingUser tempMeetingUser = new DataMeetingUser();
						tempMeetingUser.setMeetingId(record.getId());
						tempMeetingUser.setUserId(tempUser.getUserId());
						meetingUser.setMeetingUuid(record.getMeetingUuid());
						tempMeetingUser.setCreateBy(SecurityUtils.getUsername());
						iDataMeetingUserService.save(tempMeetingUser);
					}
				}
			}
		}
	}

	public void saveMeetingUser(MeetVo meetVo, String userName, String phone) {
		// 保存与会人员
		if (null != meetVo) {
			DataMeetingUser meetingUser = new DataMeetingUser();
			// 根据用户手机查询用户信息
			SysUser user = userService.selectUserByUserPhone(phone);
			if (null != user) {
				meetingUser.setUserId(user.getUserId());
				meetingUser.setUserName(user.getUserName());
				meetingUser.setUserPhone(user.getPhonenumber());
			}
			// 根据会议记录唯一编号查询对应的记录编号
			LambdaQueryWrapper<DataMeetingRecord> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataMeetingRecord::getMeetingUuid, meetVo.getMeetingUuid());
			DataMeetingRecord record = iDataMeetingRecordService.getOne(queryWrapper);
			if (null != record) {
				log.info("找不到对应的会议记录");
				meetingUser.setMeetingId(record.getId());
			}
			meetingUser.setLongitude(meetVo.getLongitude());
			meetingUser.setLatitude(meetVo.getLatitude());
			meetingUser.setMeetingUuid(meetVo.getMeetingUuid());
			meetingUser.setCreateBy(SecurityUtils.getUsername());
			iDataMeetingUserService.save(meetingUser);
		}
	}
}
