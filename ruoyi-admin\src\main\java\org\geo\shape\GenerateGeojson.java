package org.geo.shape;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;



@Slf4j
public class GenerateGeojson {

    private static final String Geotype = "FeatureCollection";
    private static final String featureType="Feature";


    public static JSONObject generatePolygonFeature(JSONObject geometryJson,JSONObject proJson){

        JSONObject featureJson=new JSONObject();
        featureJson.put("type",featureType);
        featureJson.put("geometry",geometryJson);
        featureJson.put("properties",proJson);

        return featureJson;
    }
    /**
     * geotoolsSHP生成geojson
     */
    public static JSONObject  structureSHPGeojson( JSONArray features){
     JSONObject Geojson=new JSONObject();
     Geojson.put("type",Geotype);
     Geojson.put("features",features);
     return Geojson;

    }
    /**
     * 普通方法生成geojson
     */
    public void structureGeojson(){

    }
}
