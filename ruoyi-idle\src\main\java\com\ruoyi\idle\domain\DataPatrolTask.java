package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
//import com.sun.org.apache.xpath.internal.operations.Bool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

/**
 * 巡查任务 对象 data_patrol_task
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_patrol_task")
public class DataPatrolTask implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 闲置项目编号
	 */
	@ApiModelProperty(value = "闲置项目编号")
	private String idleLandId;

	/**
	 * 项目季度
	 */
	@ApiModelProperty(value = "项目季度")
	@Excel(name = "项目季度")
	private String quarter;

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	@Excel(name = "项目名称")
	private String projectName;

	/**
	 * 项目类型
	 */
	@ApiModelProperty(value = "项目类型（0闲置土地 1批而未供）")
	@Excel(name = "项目类型")
	private String projectType;

	/**
	 * 行政区代码
	 */
	@ApiModelProperty(value = "行政区代码")
	@Excel(name = "行政区代码")
	private String regionCode;

	/**
	 * 行政区名称
	 */
	@ApiModelProperty(value = "行政区名称")
	@Excel(name = "行政区名称")
	private String regionName;

	/**
	 * 巡查类型（0自行举证 1连线核查 2内业核查）
	 */
	@ApiModelProperty(value = "巡查类型")
	private String patrolType;

	/**
	 * 在线核查开始时间
	 */
	@ApiModelProperty(value = "在线核查开始时间")
	@Excel(name = "核查时间")
	private String meetingTime;

	/**
	 * 在线核查预计结束时间
	 */
	@ApiModelProperty(value = "在线核查预计结束时间")
	private String meetingEndTime;

	/**
	 * 巡查组
	 */
	@ApiModelProperty(value = "巡查组")
	@Excel(name = "巡查组")
	private String patrolGroup;

	/**
	 * 巡查组名称
	 */
	@ApiModelProperty(value = "巡查组名称")
	@Excel(name = "巡查组名称")
	private String patrolGroupName;

	/**
	 * 发布状态
	 */
	@ApiModelProperty(value = "发布状态")
	private String publishStatus;

	/**
	 * 是否已巡查
	 */
	@ApiModelProperty(value = "是否已巡查")
	@Excel(name = "是否已巡查")
	private String isPatrol;

	/**
	 * 巡查结果说明
	 */
	@ApiModelProperty(value = "巡查结果说明")
	@Excel(name = "巡查结果说明")
	private String patrolResult;

	/**
	 * 外业巡查意见
	 */
	@ApiModelProperty(value = "外业巡查意见")
	@Excel(name = "外业巡查意见")
	private String patrolOpinion;


	/**
	 * 提交的时候填写的备注
	 */
	@ApiModelProperty(value = "提交的时候填写的备注")
	private String submitRemark;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 处置是否合规
	 */
	@ApiModelProperty(value = "处置是否合规", hidden = true)
	@TableField(exist = false)
	private String handleIsCorrect;


	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	@TableField(exist = false)
	private String contractNo;

	/**
	 * 电子监管号
	 */
	@ApiModelProperty(value = "电子监管号")
	@TableField(exist = false)
	private String supervisionNo;

	/**
	 * 是否有视频连线
	 */
	@ApiModelProperty(value = "是否有视频连线")
	@TableField(exist = false)
	private String hasOnlineVideo;

	/**
	 * 消化类型
	 */
	@ApiModelProperty(value = "消化类型")
	@TableField(exist = false)
	private String digestionType;

	/**
	 * 消化类型
	 */
	@ApiModelProperty(value = "年份")
	//@TableField(exist = false) 2024-1-6 增加了该字段
	private String year;

	@ApiModelProperty(value = "国土空间表")
	@TableField(exist = false)
	private DataClgtLand dataClgtLand;

	@ApiModelProperty(value = "房屋调查表")
	@TableField(exist = false)
	private DataHouseCheck dataHouseCheck;

}
