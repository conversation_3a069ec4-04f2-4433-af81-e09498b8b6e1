package com.ruoyi.web.controller.idle;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.bo.DataFileCatalogQueryBo;
import com.ruoyi.idle.service.IDataFileCatalogService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 巡查附件目录 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查附件目录控制器", tags = {"巡查附件目录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/file/catalog")
public class DataFileCatalogController extends BaseController {

	private final IDataFileCatalogService iDataFileCatalogService;

	private final TokenService tokenService;

	/**
	 * 查询巡查附件目录 列表
	 */
//    @ApiOperation("查询巡查附件目录列表")
////    @PreAuthorize("@ss.hasPermi('idle:catalog:list')")
//    @GetMapping("/list")
//	@ApiIgnore
//    public TableDataInfo<DataFileCatalog> list(@Validated DataFileCatalogQueryBo bo) {
//        return iDataFileCatalogService.queryPageList(bo);
//    }

	/**
	 * 查询巡查附件目录 列表
	 */
	@ApiOperation("查询巡查附件目录列表")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:list')")
	@GetMapping("/list")
	public AjaxResult<List<DataFileCatalog>> queryList(@Validated DataFileCatalog bo) {
		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.like(StringUtils.isNotBlank(bo.getName()), DataFileCatalog::getName, bo.getName())
			.like(StringUtils.isNotBlank(bo.getCatalogType()), DataFileCatalog::getCatalogType, bo.getCatalogType())
			.eq(StringUtils.isNotBlank(bo.getStatus()), DataFileCatalog::getStatus, bo.getStatus())
			.like(StringUtils.isNotBlank(bo.getRemark()), DataFileCatalog::getRemark, bo.getRemark())
			.orderByAsc(DataFileCatalog::getOrderNum);
		List<DataFileCatalog> catalogList = iDataFileCatalogService.list(queryWrapper);
		return AjaxResult.success("获取数据成功", catalogList);
	}

	/**
	 * 获取巡查附件目录 详细信息
	 */
	@ApiOperation("获取巡查附件目录详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataFileCatalog> getInfo(@NotNull(message = "主键不能为空")
											   @PathVariable("id") String id) {
		return AjaxResult.success(iDataFileCatalogService.queryById(id));
	}

	/**
	 * 新增巡查附件目录
	 */
	@ApiOperation("新增巡查附件目录")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:add')")
	@Log(title = "巡查附件目录", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataFileCatalog bo) {
		bo.setCreateBy(SecurityUtils.getUsername());
		bo.setDeleteFlag("0");
		return toAjax(iDataFileCatalogService.insert(bo) ? 1 : 0);
	}

	/**
	 * 修改巡查附件目录
	 */
	@ApiOperation("修改巡查附件目录")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:edit')")
	@Log(title = "巡查附件目录", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataFileCatalog bo) {
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iDataFileCatalogService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除巡查附件目录
	 */
	@ApiOperation("删除巡查附件目录")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:remove')")
	@Log(title = "巡查附件目录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataFileCatalogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	/**
	 * 附件目录树型数据
	 */
	@ApiOperation("附件目录树型数据")
//    @PreAuthorize("@ss.hasPermi('idle:catalog:list')")
	@GetMapping("/treeData")
	public AjaxResult treeNodeList() {
		List<DataFileCatalog> catalogList = iDataFileCatalogService.list();
		//配置
		TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
		// 自定义属性名 都要默认值的
		 treeNodeConfig.setWeightKey("orderNum");
		treeNodeConfig.setIdKey("id");
		// 最大递归深度
		treeNodeConfig.setDeep(4);

		//转换器
		List<Tree<String>> treeNodes = TreeUtil.build(catalogList, "0", treeNodeConfig,
			(treeNode, tree) -> {
				tree.setId(treeNode.getId().toString());
				tree.setParentId(treeNode.getParentId().toString());
				tree.setName(treeNode.getName());
				// 扩展属性 ...
				tree.putExtra("catalogType", treeNode.getCatalogType());
				tree.putExtra("catalogName", treeNode.getName());
				tree.putExtra("orderNum", treeNode.getOrderNum());
				tree.putExtra("remark", treeNode.getRemark());
				tree.putExtra("status", treeNode.getStatus());
			});

		return AjaxResult.success("附件目录数据生成树节点成功", treeNodes);
	}
}
