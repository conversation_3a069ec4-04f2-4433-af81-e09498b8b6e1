package com.ruoyi.common.config;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig {
	/**
	 * 项目名称
	 */
	private String name;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 版权年份
	 */
	private String copyrightYear;

	/**
	 * 实例演示开关
	 */
	private boolean demoEnabled;

	/**
	 * 上传路径
	 */
	@Getter
	private static String profile;

	/**
	 * 获取地址开关
	 */
	@Getter
	private static boolean addressEnabled;

	public void setProfile(String profile) {
		RuoYiConfig.profile = profile;
	}

	public void setAddressEnabled(boolean addressEnabled) {
		RuoYiConfig.addressEnabled = addressEnabled;
	}

	/**
	 * 获取头像上传路径
	 */
	public static String getAvatarPath() {
		return getProfile() + "/avatar";
	}

	/**
	 * 获取下载路径
	 */
	public static String getDownloadPath() {
		return getProfile() + "/download/";
	}

	/**
	 * 获取上传路径
	 */
	public static String getUploadPath() {
		return getProfile();
	}

	/**
	 * 获取备份路径
	 */
	public static String getBackUpPath() {
		return getProfile() + "/backup/";
	}

	/**
	 * 获取打包的临时地址
	 */
	public static String getPackageTempPath() {
		return getProfile() + "/packageTemp/";
	}

	/**
	 * 获取打包的地址
	 */
	public static String getPackagePath() {
		return getProfile() + "/package/";
	}

}
