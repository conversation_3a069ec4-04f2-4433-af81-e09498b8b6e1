package com.ruoyi.system.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.SysNotice;

import java.util.List;

/**
 * 公告 服务层
 *
 * <AUTHOR>
 */
public interface ISysNoticeService extends IServicePlus<SysNotice> {


	TableDataInfo<SysNotice> selectPageNoticeList(SysNotice notice);

	/**
	 * 查询公告信息
	 *
	 * @param noticeId 公告ID
	 * @return 公告信息
	 */
	public SysNotice selectNoticeById(String noticeId);

	/**
	 * 查询公告列表
	 *
	 * @param notice 公告信息
	 * @return 公告集合
	 */
	public List<SysNotice> selectNoticeList(SysNotice notice);

	/**
	 * 新增公告
	 *
	 * @param notice 公告信息
	 * @return 结果
	 */
	public int insertNotice(SysNotice notice);

	/**
	 * 修改公告
	 *
	 * @param notice 公告信息
	 * @return 结果
	 */
	public int updateNotice(SysNotice notice);

	/**
	 * 删除公告信息
	 *
	 * @param noticeId 公告ID
	 * @return 结果
	 */
	public int deleteNoticeById(String noticeId);

	/**
	 * 批量删除公告信息
	 *
	 * @param noticeIds 需要删除的公告ID
	 * @return 结果
	 */
	public int deleteNoticeByIds(String[] noticeIds);
}
