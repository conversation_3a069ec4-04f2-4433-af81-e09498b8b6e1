package com.ruoyi.idle.domain.bo;

import com.ruoyi.idle.domain.DataPatrolTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 巡查任务 分页查询对象 data_patrol_task
 *
 * <AUTHOR>
 * @date 2021-07-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("巡查任务 分页查询对象")
public class DataPatrolTaskQueryBo extends DataPatrolTask {

	/**
	 * 分页大小
	 */
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	/**
	 * 当前页数
	 */
	@ApiModelProperty("当前页数")
	private Integer pageNum;
	/**
	 * 排序列
	 */
	@ApiModelProperty("排序列")
	private String orderByColumn;
	/**
	 * 排序的方向desc或者asc
	 */
	@ApiModelProperty(value = "排序的方向", example = "asc,desc")
	private String isAsc;

	/**
	 * 建筑编号
	 */
	@ApiModelProperty("建筑编号")
	private String jzbh;

	/**
	 * 建筑预编号
	 */
	@ApiModelProperty("建筑预编号")
	private String jzybh;

	/**
	 * 行政区
	 */
	@ApiModelProperty("行政区")
	private List<String> regionCodes;

}
