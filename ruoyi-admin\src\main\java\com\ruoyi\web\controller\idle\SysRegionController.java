package com.ruoyi.web.controller.idle;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.DataPatrolGroup;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.SysRegionQueryBo;
import com.ruoyi.idle.domain.vo.RegionGroupTreeVo;
import com.ruoyi.idle.domain.vo.RegionTreeVo;
import com.ruoyi.idle.service.IDataPatrolGroupService;
import com.ruoyi.idle.service.ISysRegionService;
import com.ruoyi.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.io.FileUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 行政区Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "行政区控制器", tags = {"行政区管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/region")
public class SysRegionController extends BaseController {

	private final ISysRegionService iSysRegionService;

	private final IDataPatrolGroupService iDataPatrolGroupService;

	private final TokenService tokenService;

	private final ISysDictDataService iSysDictDataService;

	/**
	 * 查询行政区列表
	 */
	@ApiOperation("查询行政区列表")
//    @PreAuthorize("@ss.hasPermi('idle:region:list')")
	@GetMapping("/list")
	public AjaxResult<List<SysRegion>> list(@Validated SysRegionQueryBo bo) {
		return AjaxResult.success("成功获取行政区列表数据", iSysRegionService.queryList(bo));
	}

	/**
	 * 获取行政区树
	 */
	@ApiOperation("获取行政区树")
//	@PreAuthorize("@ss.hasPermi('idle:region:tree')")
	@GetMapping("/tree")
	@ApiIgnore
	public AjaxResult<List<Tree<String>>> tree(@Validated SysRegion vo) {
		LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<SysRegion>();
		queryWrapper.like(SysRegion::getAreaCode, "5301");
		queryWrapper.in(SysRegion::getLevel, Arrays.asList("1", "2"));
		queryWrapper.orderByAsc(SysRegion::getAreaCode);
		List<SysRegion> regionList = iSysRegionService.list(queryWrapper);
		//配置
		TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
		// 自定义属性名 都要默认值的
		treeNodeConfig.setWeightKey("areaCode");
		treeNodeConfig.setIdKey("id");
		// 最大递归深度
		treeNodeConfig.setDeep(4);
		//转换器
		List<Tree<String>> treeNodes = TreeUtil.build(regionList, "616854", treeNodeConfig,
			(treeNode, tree) -> {
				tree.setId(treeNode.getId());
				tree.setParentId(treeNode.getParentId());
				tree.setName(treeNode.getName());
				// 扩展属性 ...
				tree.putExtra("level", treeNode.getLevel());
				tree.putExtra("areaCode", treeNode.getAreaCode());
				tree.putExtra("zipCode", treeNode.getZipCode());
				tree.putExtra("cityCode", treeNode.getCityCode());
				tree.putExtra("lng", treeNode.getLng());
				tree.putExtra("lat", treeNode.getLat());
			});

		return AjaxResult.success("成功生成行政区树节点", treeNodes);
	}

	/**
	 * 获取行政区巡查组树
	 */
	@ApiOperation("获取行政区巡查组树")
//	@PreAuthorize("@ss.hasPermi('idle:region:tree')")
	@GetMapping("/regionGroupTree")
	public AjaxResult regionGroupTree() {
		List<RegionGroupTreeVo> treeVoList = new ArrayList<>();
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<SysRegion>();
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				queryWrapper.eq(SysRegion::getAreaCode, sysUser.getRegionCode());
			} else {
				queryWrapper.like(SysRegion::getAreaCode, "5301");
				queryWrapper.eq(SysRegion::getLevel, "2");
			}
			queryWrapper.orderByAsc(SysRegion::getAreaCode);

			List<SysRegion> regionList = iSysRegionService.list(queryWrapper);
			if (regionList == null || regionList.size() == 0) return AjaxResult.error("请您补充齐全您的个人信息");
			for (SysRegion region : regionList) {
				RegionGroupTreeVo groupTreeVo = new RegionGroupTreeVo();
				BeanUtils.copyProperties(region, groupTreeVo);
				LambdaQueryWrapper<DataPatrolGroup> queryWrapper1 = new LambdaQueryWrapper<>();
				queryWrapper1.eq(DataPatrolGroup::getCountyCode, region.getAreaCode());
				queryWrapper1.orderByAsc(DataPatrolGroup::getId);
				List<DataPatrolGroup> groupList = iDataPatrolGroupService.list(queryWrapper1);
				groupTreeVo.setChildren(groupList);
				treeVoList.add(groupTreeVo);
			}
			return AjaxResult.success(treeVoList);

		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	/**
	 * 获取行政区详细信息
	 */
	@ApiOperation("获取行政区详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:region:query')")
	@GetMapping("/{id}")
	public AjaxResult<SysRegion> getInfo(@NotNull(message = "主键不能为空")
										 @PathVariable("id") String id) {
		return AjaxResult.success(iSysRegionService.queryById(id));
	}


	/**
	 * 新增行政区
	 */
	@ApiOperation("新增行政区")
//    @PreAuthorize("@ss.hasPermi('idle:region:add')")
	@Log(title = "行政区", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult add(@Validated @RequestBody SysRegion bo) {
		// 先判断一下行政区代码，行政区名称有没有重复的
		int count = 0;
		count = iSysRegionService.count(new LambdaQueryWrapper<SysRegion>().eq(SysRegion::getAreaCode, bo.getAreaCode()));
		if (count > 0) return AjaxResult.error("行政区代码" + bo.getAreaCode() + "已经存在");
		count = iSysRegionService.count(new LambdaQueryWrapper<SysRegion>().eq(SysRegion::getName, bo.getName()));
		if (count > 0) return AjaxResult.error("行政区名称" + bo.getName() + "已经存在");
		bo.setCreateBy(SecurityUtils.getUsername());
		bo.setDeleteFlag("0");
		return toAjax(iSysRegionService.insert(bo) ? 1 : 0);
	}

	/**
	 * 修改行政区
	 */
	@ApiOperation("修改行政区")
//    @PreAuthorize("@ss.hasPermi('idle:region:edit')")
	@Log(title = "行政区", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody SysRegion bo) {
		int count = 0;
		SysRegion sysRegion = iSysRegionService.queryById(bo.getId());
		if (!sysRegion.getAreaCode().equals(bo.getAreaCode())) {
			count = iSysRegionService.count(new LambdaQueryWrapper<SysRegion>().eq(SysRegion::getAreaCode, bo.getAreaCode()));
			if (count > 0) return AjaxResult.error("行政区代码" + bo.getAreaCode() + "已经存在");
		}
		if (!sysRegion.getName().equals(bo.getName())) {
			count = iSysRegionService.count(new LambdaQueryWrapper<SysRegion>().eq(SysRegion::getName, bo.getName()));
			if (count > 0) return AjaxResult.error("行政区名称" + bo.getName() + "已经存在");
		}
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iSysRegionService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除行政区
	 */
	@ApiOperation("删除行政区")
//    @PreAuthorize("@ss.hasPermi('idle:region:remove')")
	@Log(title = "行政区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iSysRegionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	/**
	 * 异步获取行政区树
	 */
	//@PreAuthorize("@ss.hasPermi('idle:region:list')")
	@GetMapping("/regionTreeAsync")
	@Log(title = "行政区")
	@ApiOperation("异步获取行政区树")
	public AjaxResult getRegionTreeAsync(@RequestParam(name = "pId", required = false)
											 String pId) {
		List<RegionTreeVo> list = iSysRegionService.getRegionTreeAsync(pId);
		return AjaxResult.success(list);
	}

	@PostMapping ("/exportJsonFile")
	@ApiOperation("导出字典和行政区为json数据")
	public void exportJsonFile(@RequestParam(value = "types", required = false) List<String> types, HttpServletResponse response) {
		//行政区数据
		LambdaQueryWrapper<SysRegion> lmq = new LambdaQueryWrapper<>();
		List<SysRegion> regionList = iSysRegionService.list(lmq);
		// 将查询到的结果序列化为json格式
		Map<String, Object> objectMap = new HashMap<>();
		objectMap.put("version", "1");
		objectMap.put("count", "1");
		objectMap.put("msg", "行政区和字典");
		objectMap.put("regionList", regionList);

//		if (types == null) {
//			types = new ArrayList<>();
//			//结构类型
//			types.add("jglx");
//			//房屋性质
//			types.add("fwxz");
//			//权利性质
//			types.add("qllx");
//			//房屋用途
//			types.add("fwyt");
//			//建筑分类
//			types.add("jzfl");
//		}
//		for (String t:types) {
//
//		}
		//字典值数据
		LambdaQueryWrapper<SysDictData> lqw1 = new LambdaQueryWrapper<>();
		List<SysDictData> dictDataList = iSysDictDataService.list(lqw1);
		objectMap.put("dictDataList", dictDataList);
		// 导出内容到浏览器
		try {
//			String jsonStr = JSONUtil.toJsonStr(objectMap);
			//格式化json内容
			String jsonStr = JSONUtil.toJsonPrettyStr(objectMap);
			FileUtil.appendString("d:/regionList.json",jsonStr);
			byte[] jsonBytes = jsonStr.getBytes(StandardCharsets.UTF_8);
			// 重置响应，清除可能存在的干扰头
			response.reset();
			// 设置响应头
			response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
			response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
			response.setHeader("Content-Type", "application/octet-stream");
			response.setHeader("Content-Disposition", "attachment; filename=\"export.json\"");
			response.setContentLength(jsonBytes.length);
			// 写入数据
			OutputStream outputStream = response.getOutputStream();
			outputStream.write(jsonBytes);
			outputStream.flush();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取行政区树到村级行政区
	 */
	@ApiOperation("获取行政区树到村级行政区")
//	@PreAuthorize("@ss.hasPermi('idle:region:tree')")
	@GetMapping("/regionTree")
	@ApiIgnore
	public AjaxResult<List<Tree<String>>> regionTree(@Validated SysRegion vo) {
		List<SysRegion> regionList = iSysRegionService.list();
		//配置
		TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
		// 自定义属性名 都要默认值的
		treeNodeConfig.setWeightKey("areaCode");
		treeNodeConfig.setIdKey("id");
		// 最大递归深度
		treeNodeConfig.setDeep(4);
		//转换器
		List<Tree<String>> treeNodes = TreeUtil.build(regionList, "0", treeNodeConfig,
			(treeNode, tree) -> {
				tree.setId(treeNode.getId());
				tree.setParentId(treeNode.getParentId());
				tree.setName(treeNode.getName());
				// 扩展属性 ...
				tree.putExtra("level", treeNode.getLevel());
				tree.putExtra("areaCode", treeNode.getAreaCode());
				tree.putExtra("parentCode", treeNode.getParentCode());
				tree.putExtra("zipCode", treeNode.getZipCode());
				tree.putExtra("cityCode", treeNode.getCityCode());
			});

		return AjaxResult.success("成功生成行政区树节点", treeNodes);
	}
}
