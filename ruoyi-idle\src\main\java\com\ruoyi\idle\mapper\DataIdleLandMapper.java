package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 闲置土地 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
//@CacheNamespace(implementation = MybatisPlusRedisCache.class, eviction = MybatisPlusRedisCache.class)
public interface DataIdleLandMapper extends BaseMapperPlus<DataIdleLand> {

	DataIdleLand selectDataBySupervisionNo(@Param(value = "supervisionNo") String supervisionNo);

	String selectDigestionTypeByTaskId(@Param(value = "taskId") String taskId);

	String selectSupervisionNoByTaskId(@Param(value = "taskId") String taskId);

	@Select("SELECT l.* from data_clgt_land l \n" +
		"INNER JOIN data_patrol_task t on t.idle_land_id =l.id \n" +
		"INNER JOIN data_task_user u on u.task_id=t.id \n" +
		"where u.user_id=#{userId} \n" +
		"and t.region_code=#{regionCode}")
	List<DataIdleLand> appPulList(@Param("regionCode") String regionCode, @Param("userId") String userId);

	List<DataIdleLand> selectAllIdleLandList(@Param("startTime") String startTime);

}
