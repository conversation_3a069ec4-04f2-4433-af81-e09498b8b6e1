package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 角色表 sys_role
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_role")
public class SysRole implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 角色ID
	 */
	@Excel(name = "角色序号", cellType = ColumnType.NUMERIC)
	@TableId(value = "role_id", type = IdType.ASSIGN_UUID)
	private String roleId;

	/**
	 * 角色名称
	 */
	@Excel(name = "角色名称")
	@NotBlank(message = "角色名称不能为空")
	@Size(min = 0, max = 30, message = "角色名称长度不能超过30个字符")
	private String roleName;

	/**
	 * 角色权限
	 */
	@Excel(name = "角色权限")
	@NotBlank(message = "权限字符不能为空")
	@Size(min = 0, max = 100, message = "权限字符长度不能超过100个字符")
	private String roleKey;

	/**
	 * 角色排序
	 */
	@Excel(name = "角色排序")
	@NotBlank(message = "显示顺序不能为空")
	private String roleSort;

	/**
	 * 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
	 */
	@Excel(name = "数据范围", readConverterExp = "1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5=仅本人数据权限")
	private String dataScope;

	/**
	 * 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）
	 */
	private boolean menuCheckStrictly;

	/**
	 * 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）
	 */
	private boolean deptCheckStrictly;

	/**
	 * 角色状态（0正常 1停用）
	 */
	@Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableLogic
	private String delFlag;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 请求参数
	 */
	@TableField(exist = false)
	private Map<String, Object> params = new HashMap<>();

	/**
	 * 用户是否存在此角色标识 默认不存在
	 */
	@TableField(exist = false)
	private boolean flag = false;

	/**
	 * 菜单组
	 */
	@TableField(exist = false)
	private String[] menuIds;

	/**
	 * 部门组（数据权限）
	 */
	@TableField(exist = false)
	private String[] deptIds;

	public SysRole(String roleId) {
		this.roleId = roleId;
	}

	public boolean isAdmin() {
		return isAdmin(this.roleId);
	}

	public static boolean isAdmin(String roleId) {
		return StringUtils.isNotBlank(roleId) && "1".equals(roleId);
	}

}
