<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataExportRecordMapper">

    <resultMap type="com.ruoyi.idle.domain.DataExportRecord" id="DataExportRecordResult">
            <result property="id" column="id"/>
            <result property="startTime" column="start_time"/>
            <result property="endTime" column="end_time"/>
            <result property="takeUpTime" column="take_up_time"/>
            <result property="zipName" column="zip_name"/>
            <result property="zipSize" column="zip_size"/>
            <result property="zipUrl" column="zip_url"/>
            <result property="zipType" column="zip_type"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="status" column="status"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
