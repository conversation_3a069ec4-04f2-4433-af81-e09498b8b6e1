package com.ruoyi.web.controller.system;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.UserRegisterBo;
import com.ruoyi.idle.service.ISysRegionService;
import com.ruoyi.meet.utils.Des;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.service.*;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(value = "登录控制器", tags = {"登录管理"})
@RestController
@Slf4j
public class SysLoginController {
	@Autowired
	private SysLoginService loginService;

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	@Autowired
	private ISysUserService userService;

	@Autowired
	private ISysMenuService menuService;

	@Autowired
	private SysPermissionService permissionService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private ISysDictTypeService dictTypeService;

	@Autowired
	private ISysRegionService  sysRegionService;

	@Autowired
	private ISysDictDataService  sysDictDataService;

	@GetMapping("/testo")
	@ApiOperation("testo")
	public AjaxResult testo() {

		SimpleDateFormat sdf=new SimpleDateFormat("EEE MMM dd HH:mm:ss 'GMT+08:00' yyyy", Locale.ENGLISH);

		String modile_localdb_en = "\\w{3}\\s\\w{3}\\s\\d{1,2}\\s\\d{2}:\\d{2}:\\d{2}\\sGMT\\+08:\\d{2}\\s\\d{4}";
		String source="Tue Jul 01 00:00:00 GMT+08:00 2025";
		if (source.matches(modile_localdb_en)) {
			try {
				Date date2= sdf.parse(source);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		String modile_localdb_en2 = "\\w{3}\\s\\w{3}\\s\\d{2}\\d{2}:\\d{2}:\\d{2}\\sGMT\\+08:\\d{2}\\s\\d{4}";
		SimpleDateFormat sdf2=new SimpleDateFormat("EEE MMM ddHH:mm:ss 'GMT+08:00' yyyy", Locale.ENGLISH);
		String source2="Tue Jul 0100:00:00 GMT+08:00 2025";
		if (source2.matches(modile_localdb_en2)) {
			try {
				Date date22= sdf2.parse(source2);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		Date date3 = new Date();

//		String modile_localdb_en3 = "\\w{3}\\s\\w{3}\\s\\d{2}\\s\\d{4}\\s\\d{2}:\\d{2}:\\d{2}\\sCST";
//		SimpleDateFormat format1 = new SimpleDateFormat("E MMM dd yyyy HH:mm:ss z", Locale.US);
//		String str1 = format1.format(date3);
//		if (modile_localdb_en3.matches(str1)) {
//			try {
//				Date date23= format1.parse(str1);
//			} catch (ParseException e) {
//				e.printStackTrace();
//			}
//		}
//
//		String modile_localdb_en4 = "\\w{3}\\s\\w{3}\\s\\d{2}\\s\\d{4}\\s\\d{2}:\\d{2}:\\d{2}";
//		SimpleDateFormat format2 = new SimpleDateFormat("EEE MMM dd yyyy hh:mm:ss", Locale.ENGLISH);
//		String str2 = format2.format(date3);
//		if (modile_localdb_en4.matches(str2)) {
//			try {
//				Date date23= format2.parse(str2);
//			} catch (ParseException e) {
//				e.printStackTrace();
//			}
//		}
		String str1="",str2="";
		try {
			//万能转换时间

			Date  pars1e = cn.hutool.core.date.DateUtil.parse(str1);
			System.out.println(pars1e.toString());
			Date  pars2e = cn.hutool.core.date.DateUtil.parse(str2);
			System.out.println(pars2e.toString());
		}catch (Exception e){
			System.out.println(e.toString());
		}


		return AjaxResult.success(str2);
		/*
		LambdaQueryWrapper<SysRegion> lqw = Wrappers.lambdaQuery();
		lqw.eq(true, SysRegion::getLevel, "2");
		List<SysRegion> list1=sysRegionService.list(lqw);

		LambdaQueryWrapper<SysRegion> lqw2 = Wrappers.lambdaQuery();
		lqw2.eq(true, SysRegion::getLevel, "3");
		List<SysRegion> list2=sysRegionService.list(lqw2);

		StringBuffer sb=new StringBuffer();
		sb.append("DaoSession daoSession = BaseApplication.getInstance().getDaoSession();\n");
		sb.append("List<SysRegion> listRegions=new ArrayList<>();\n");
		int i=1;
		for (SysRegion sysRegion:list1
			 ) {
			i=i+1;
			sb.append("SysRegion region"+i+"=new SysRegion();\n");
			sb.append("region"+i+".setId(\""+sysRegion.getId()+"\");\n");
			sb.append("region"+i+".setLevel(\""+sysRegion.getLevel()+"\");\n");
			sb.append("region"+i+".setParentId(\""+sysRegion.getParentId()+"\");\n");
			sb.append("region"+i+".setParentCode(\""+sysRegion.getParentCode()+"\");\n");
			sb.append("region"+i+".setAreaCode(\""+sysRegion.getAreaCode()+"\");\n");
			sb.append("region"+i+".setName(\""+sysRegion.getName()+"\");\n");
			sb.append("region"+i+".setEnabled(\""+sysRegion.getEnabled()+"\");\n");
			sb.append("region"+i+".setDeleteFlag(\""+sysRegion.getDeleteFlag()+"\");\n");
			sb.append("listRegions.add(region"+i+");\n");
		}
		for (SysRegion sysRegion:list2
		) {
			i=i+1;
			sb.append("SysRegion region"+i+"=new SysRegion();\n");
			sb.append("region"+i+".setId(\""+sysRegion.getId()+"\");\n");
			sb.append("region"+i+".setLevel(\""+sysRegion.getLevel()+"\");\n");
			sb.append("region"+i+".setParentId(\""+sysRegion.getParentId()+"\");\n");
			sb.append("region"+i+".setParentCode(\""+sysRegion.getParentCode()+"\");\n");
			sb.append("region"+i+".setAreaCode(\""+sysRegion.getAreaCode()+"\");\n");
			sb.append("region"+i+".setName(\""+sysRegion.getName()+"\");\n");
			sb.append("region"+i+".setEnabled(\""+sysRegion.getEnabled()+"\");\n");
			sb.append("region"+i+".setDeleteFlag(\""+sysRegion.getDeleteFlag()+"\");\n");
			sb.append("listRegions.add(region"+i+");\n");
		}

		sb.append("SysRegionDao sysRegionDao =daoSession.getSysRegionDao();\n");
		sb.append("sysRegionDao.insertOrReplaceInTx(listRegions);\n");

		LambdaQueryWrapper<SysDictData> lqwDict = Wrappers.lambdaQuery();
		lqwDict.eq(SysDictData::getDictType, "jzfl")
			.or()
			.eq(SysDictData::getDictType, "qllx")
			.or()
			.eq(SysDictData::getDictType, "fwyt")
			.or()
			.eq(SysDictData::getDictType, "fwxz")
			.or()
			.eq(SysDictData::getDictType, "jglx")
			.or()
			.eq(SysDictData::getDictType, "sys_user_sex");
		List<SysDictData> lise3 =sysDictDataService.list(lqwDict);

		sb.append("List<SysDictData> listSysDictDatas=new ArrayList<>();\n");

		for (SysDictData sysDictData:lise3
		) {
			i=i+1;
			sb.append("SysDictData sysDictData"+i+"=new SysDictData();\n");
			sb.append("sysDictData"+i+".setDictCode(\""+sysDictData.getDictCode()+"\");\n");
			sb.append("sysDictData"+i+".setDictType(\""+sysDictData.getDictType()+"\");\n");
			sb.append("sysDictData"+i+".setDictLabel(\""+sysDictData.getDictLabel()+"\");\n");
			sb.append("sysDictData"+i+".setDictValue(\""+sysDictData.getDictValue()+"\");\n");
			sb.append("sysDictData"+i+".setStatus(\""+sysDictData.getStatus()+"\");\n");

			sb.append("listSysDictDatas.add(sysDictData"+i+");\n");
		}
		sb.append("SysDictDataDao sysDictDataDao =daoSession.getSysDictDataDao();\n");
		sb.append("sysDictDataDao.insertOrReplaceInTx(listRegions);\n");

		try (FileWriter writer = new FileWriter("e:/data2.java");
			 BufferedWriter bufferedWriter = new BufferedWriter(writer)) {
			bufferedWriter.write(sb.toString());
			System.out.println("Text written successfully.");

		} catch (IOException e) {
			e.printStackTrace();
		}

			return AjaxResult.success("成功获 户类型" );

		 */

	}

	/**
	 * 登录方法
	 *
	 * @param loginBody 登录信息
	 * @return 结果
	 */
	@PostMapping("/login")
	@ApiOperation("PC端登录")
	public AjaxResult login(@RequestBody LoginBody loginBody) {
		Map<String, Object> ajax = new HashMap<>();
		// 生成令牌
		String token = "";
		if (loginBody.getUsername() != null) {
			token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
				loginBody.getUuid());
			ajax.put(Constants.TOKEN, token);
			return AjaxResult.success(ajax);
		} else if (loginBody.getUserPhone() != null) {
			SysUser user = userService.selectUserByUserPhone(loginBody.getUserPhone());
			if (user == null) return AjaxResult.error("根据手机号没有匹配到用户");
			token = loginService.login(user.getUserName(), loginBody.getPassword(), loginBody.getCode(),
				loginBody.getUuid());
			ajax.put(Constants.TOKEN, token);
			return AjaxResult.success(ajax);
		} else {
			return AjaxResult.error("请检查用户名或用户手机号是否填写完整");
		}
	}

	/**
	 * 检查Token是否有效
	 *
	 * @param token Token信息
	 * @return 结果
	 */
	@PostMapping("/checkToken")
	@ApiOperation("检查Token是否有效")
	public AjaxResult checkToken(String token) {
		LoginUser loginUser = tokenService.getLoginUser(token);
		if (null == loginUser) return AjaxResult.error("请给定正确的Token");
		long expireTime = loginUser.getExpireTime();
		return AjaxResult.success(expireTime);
	}

	/**
	 * 检查Token是否有效
	 *
	 * @param token Token信息
	 * @return 结果
	 */
	@PostMapping("/crToken")
	@ApiOperation("检查及刷新Token")
	public AjaxResult checkAndRefreshToken(String token) {
		LoginUser loginUser = tokenService.getLoginUser(token);
		if (null == loginUser) return AjaxResult.error("请给定正确的Token");
		long expireTime = loginUser.getExpireTime();
		tokenService.refreshToken(loginUser);
		return AjaxResult.success();
	}

	/**
	 * 获取用户的类型（内业外业）
	 *
	 * @return 结果
	 */
	@GetMapping("/getUserType")
	@ApiOperation("获取用户的类型")
	public AjaxResult getUserType() {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			return AjaxResult.success("成功获取用户类型", sysUser.getUserType());
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}


	/**
	 * 用户注册
	 *
	 * @param registerBo 用户注册对象
	 * @return
	 */
	@PostMapping("/register")
	@ApiOperation("用户注册")
	@Transactional
	public AjaxResult register(@RequestBody UserRegisterBo registerBo) throws Exception {
		if (registerBo != null) {
			SysUser user = new SysUser();
			BeanUtils.copyProperties(registerBo, user);
			if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
				return AjaxResult.error("新增用户'" + registerBo.getUserName() + "'失败，登录账号已存在");
			} else if (Validator.isNotEmpty(user.getPhonenumber())
				&& UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
				return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
			} else if (Validator.isNotEmpty(user.getEmail())
				&& UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
				return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
			}
			if (user.getRegionCode() != null && user.getRegionCode().equals("5301")) {
				user.setUserType("0");
			} else {
				user.setUserType("1");
			}
			user.setStatus("2");// 待审核的用户标识
			user.setNickName(user.getUserName());
			user.setSex("2");
			user.setDeptId("105");
			user.setDelFlag("0");

			String pass = Des.decrypt(user.getPassword(), "f1f3h6h6");
			user.setPassword(SecurityUtils.encryptPassword(pass));
			String msg = userService.saveUser(user) == 1 ? "注册成功" : "注册失败";
			return AjaxResult.success(msg);
		} else {
			return AjaxResult.error("注册填写的信息不能为空");
		}
	}

	/**
	 * 获取单位列表
	 *
	 * @return
	 */
	@GetMapping("/getOrgList")
	@ApiOperation("获取单位列表")
	public AjaxResult getOrgList() {
		List<SysDictData> data = dictTypeService.selectDictDataByType("sys_org");
		if (Validator.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success(data);
	}


	/**
	 * 登录方法
	 *
	 * @param userPhone 用户手机号码
	 * @param password  用户密码
	 * @return 结果
	 */
	@PostMapping("/appLogin")
	@ApiOperation("App登录")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userPhone", value = "用户手机号码", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "password", value = "用户密码", dataTypeClass = String.class, required = true),
	})
	public AjaxResult appLogin(String userPhone, String password) {
		Map<String, Object> ajax = new HashMap<>();
		// 先使用手机号码和密码判断用户是否存在级密码的正确性
		//SysUser user = userService.getOne(Wrappers.<SysUser>lambdaQuery()
		//	.eq(SysUser::getDelFlag, 0).eq(SysUser::getPhonenumber, userPhone));
		List<SysUser> userList = userService.list(Wrappers.<SysUser>lambdaQuery()
			.eq(SysUser::getDelFlag, 0).eq(SysUser::getPhonenumber, userPhone));
		if (userList.size() > 1) return AjaxResult.error("存在多条相同手机号的用户记录，请联系管理员删除");
		SysUser user = userList.size() == 1 ? userList.get(0) : null;
		if (user != null) {
			if (user.getStatus().equals("1")) {
				return AjaxResult.error("该账户已停用");
			} else if (user.getStatus().equals("2")) {
				return AjaxResult.error("该账号正在审核阶段");
			}
		} else {
			return AjaxResult.error("用户不存在");
		}
		if (StringUtils.isNotBlank(password)) {
			boolean isMatch = SecurityUtils.matchesPassword(password, user.getPassword());
			if (!isMatch) {
				return AjaxResult.error("用户密码错误");
			}
		}
		// 生成令牌
		String token = loginService.appLogin(user.getUserName(), password);
		ajax.put(Constants.TOKEN, token);
		ajax.put("user", user);
		return AjaxResult.success(ajax);
	}

	/**
	 * 短信验证码登录方法
	 *
	 * @param userPhone 用户手机号码
	 * @param code      验证码
	 * @return 结果
	 */
	@PostMapping("/smsLogin")
	@ApiOperation("短信验证码登录方法")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userPhone", value = "用户手机号码", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "code", value = "验证码", dataTypeClass = String.class, required = true),
	})
	public AjaxResult smsLogin(@RequestParam("userPhone") String userPhone, @RequestParam("code") String code) {
		Map<String, Object> ajax = new HashMap<>();
		// 先使用手机号码和密码判断用户是否存在级密码的正确性
		SysUser user = userService.getOne(Wrappers.<SysUser>lambdaQuery()
			.eq(SysUser::getDelFlag, 0).eq(SysUser::getPhonenumber, userPhone));
		if (user != null) {
			if (user.getStatus().equals("1")) {
				return AjaxResult.error("该账户已停用");
			} else if (user.getStatus().equals("2")) {
				return AjaxResult.error("该账号正在审核阶段");
			}
		} else {
			return AjaxResult.error("用户不存在");
		}

		// 获取到操作String的对象
		ValueOperations<String, String> stringR = redisTemplate.opsForValue();
		// 根据Key进行查询
		String redisCode = stringR.get(userPhone);
		if (code.equals(redisCode)) {
			// 生成令牌
			LoginUser loginUser = new LoginUser();
			loginUser.setUser(user);
			loginUser.setToken(IdUtil.fastUUID());
			String token = tokenService.createToken(loginUser);
			//String token = loginService.appLogin(user.getUserName(),user.getPassword());
			ajax.put(Constants.TOKEN, token);
			ajax.put("user", user);
			return AjaxResult.success(ajax);
		} else {
			return AjaxResult.error(redisCode == null ? "请先发送验证码!" : "验证码错误");
		}


	}


	/**
	 * 获取用户信息
	 *
	 * @return 用户信息
	 */
	@GetMapping("getInfo")
	public AjaxResult getInfo() {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser user = loginUser.getUser();
		// 角色集合
		Set<String> roles = permissionService.getRolePermission(user);
		// 权限集合
		Set<String> permissions = permissionService.getMenuPermission(user);
		Map<String, Object> ajax = new HashMap<>();
		ajax.put("user", user);
		ajax.put("roles", roles);
		ajax.put("permissions", permissions);
		return AjaxResult.success(ajax);
	}

	/**
	 * 获取路由信息
	 *
	 * @return 路由信息
	 */
	@GetMapping("getRouters")
	public AjaxResult getRouters() {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		// 用户信息
		SysUser user = loginUser.getUser();
		List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
		return AjaxResult.success(menuService.buildMenus(menus));
	}
}
