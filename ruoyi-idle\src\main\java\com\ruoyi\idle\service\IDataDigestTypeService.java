package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
        import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataDigestType;
import com.ruoyi.idle.domain.bo.DataDigestTypeQueryBo;
import com.ruoyi.idle.domain.vo.DataDigestTypeVo;

import java.util.Collection;
import java.util.List;

/**
 * 消化类型对应关系 Service接口
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
public interface IDataDigestTypeService extends IServicePlus<DataDigestType> {
    /**
     * 查询单个
     * @return
     */
        DataDigestTypeVo queryById(String id);

            /**
         * 查询列表
         */
        TableDataInfo<DataDigestTypeVo> queryPageList(DataDigestTypeQueryBo bo);

    /**
     * 查询列表
     */
    List<DataDigestTypeVo> queryList(DataDigestTypeQueryBo bo);

    /**
     * 根据新增业务对象插入消化类型对应关系
     * @param bo 消化类型对应关系 新增业务对象
     * @return
     */
    Boolean insertByAddBo(DataDigestType bo);

    /**
     * 根据编辑业务对象修改消化类型对应关系
     * @param bo 消化类型对应关系 编辑业务对象
     * @return
     */
    Boolean updateByEditBo(DataDigestType bo);

    /**
     * 校验并删除数据
     * @param ids 主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
