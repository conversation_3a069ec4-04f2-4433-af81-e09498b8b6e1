package com.ruoyi.meet.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.meet.tencentyun.TLSSigAPIv2;
import com.ruoyi.meet.utils.Des;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "腾讯实时视频", tags = {"腾讯实时视频"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/phone/meet")
public class TencentController {
	private static final int SDKAPPID = 1400740778;
//	private static final int SDKAPPID = 1400520838;
//	private static final int SDKAPPID = 1400546310;// 1400520838;
	//private static final String SECRETKEY = "8c33dda1a599c9e86b84fb1c7200409ea14a7f689682e3e1cd0a4adc6050c4d6";
//	private static final String SECRETKEY = "dd600754894327e95ed36b399d2f7b3851df6caad4e13c29bd726afb99c8781a"; //"598811f54989583322ce5042ea5c99820de9a654563cf858e8a568796d5343ab";
//	private static final String SECRETKEY = "598811f54989583322ce5042ea5c99820de9a654563cf858e8a568796d5343ab";

	private static final String SECRETKEY ="1b028d06b3902756bb26080e6395ec95020c18b111789dfeacf95a0564f47ecb";
	/**
	 * 签名过期时间，建议不要设置的过短
	 * <p>
	 * 时间单位：秒
	 * 默认时间：7 x 24 x 60 x 60 = 604800 = 7 天
	 */
	private static final int EXPIRETIME = 604800;

	@ApiOperation("获取密钥")
	@PostMapping("/gensct")
	public AjaxResult genSig(@RequestParam("uid") String uid) {
		if (StringUtils.isNotBlank(uid)) {
			try {
				String uid2 = Des.decrypt(uid, "f2f4h6h9");


				JSONObject object1 = new JSONObject();
				TLSSigAPIv2 api = new TLSSigAPIv2(SDKAPPID, SECRETKEY);
				String sig = api.genUserSig(uid2, EXPIRETIME);
				object1.put("sig", sig);
				System.out.println("genSig: " + sig);

				AjaxResult ajax = AjaxResult.success(object1);
				return ajax;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		AjaxResult ajax = AjaxResult.error("提交失败");
		return ajax;
	}
}
