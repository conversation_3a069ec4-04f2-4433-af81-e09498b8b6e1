package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.idle.domain.DataExportRecord;
import com.ruoyi.idle.domain.bo.DataExportRecordQueryBo;
import com.ruoyi.idle.service.IDataExportRecordService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 成果导出记录 Controller
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Api(value = "成果导出记录 控制器", tags = {"成果导出记录 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/export/record")
public class DataExportRecordController extends BaseController {

	private final IDataExportRecordService iDataExportRecordService;

	/**
	 * 查询成果导出记录 列表
	 */
	@ApiOperation("查询成果导出记录 列表")
	@GetMapping("/list")
	public TableDataInfo<DataExportRecord> list(@Validated DataExportRecordQueryBo bo) {
		return iDataExportRecordService.queryPageList(bo);
	}

	/**
	 * 获取成果导出记录 详细信息
	 */
	@ApiOperation("获取成果导出记录 详细信息")
	@GetMapping("/{id}")
	public AjaxResult<DataExportRecord> getInfo(@NotNull(message = "主键不能为空")
												@PathVariable("id") Long id) {
		return AjaxResult.success(iDataExportRecordService.queryById(id));
	}


	/**
	 * 删除成果导出记录
	 */
	@ApiOperation("删除成果导出记录 ")
	@Log(title = "成果导出记录 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable Long[] ids) {
		return toAjax(iDataExportRecordService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}
}
