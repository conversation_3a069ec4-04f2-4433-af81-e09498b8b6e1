<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataAttachmentMapper">

    <resultMap type="com.ruoyi.idle.domain.DataAttachment" id="DataAttachmentResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="size" column="size"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="location" column="location"/>
        <result property="patrolTaskId" column="patrol_task_id"/>
        <result property="catalogId" column="catalog_id"/>
        <result property="isSiteData" column="is_site_data"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <delete id="realDeleteRecord">
        delete
        from `data_attachment`
        where `name` = #{fileName}
          and patrol_task_id = #{taskId}
    </delete>

</mapper>
