package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataGroupUser;
import com.ruoyi.idle.domain.bo.DataGroupUserQueryBo;
import com.ruoyi.idle.mapper.DataGroupUserMapper;
import com.ruoyi.idle.service.IDataGroupUserService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 巡查组用户关系 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataGroupUserServiceImpl extends ServicePlusImpl<DataGroupUserMapper, DataGroupUser> implements IDataGroupUserService {


	@Override
	public DataGroupUser queryById(String id) {
		return getVoById(id, DataGroupUser.class);
	}

	@Override
	public TableDataInfo<DataGroupUser> queryPageList(DataGroupUserQueryBo bo) {
		PagePlus<DataGroupUser, DataGroupUser> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataGroupUser.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataGroupUser> queryList(DataGroupUserQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataGroupUser.class);
	}

	private LambdaQueryWrapper<DataGroupUser> buildQueryWrapper(DataGroupUserQueryBo bo) {
		LambdaQueryWrapper<DataGroupUser> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getGroupId() != null, DataGroupUser::getGroupId, bo.getGroupId());
		lqw.eq(bo.getUserId() != null, DataGroupUser::getUserId, bo.getUserId());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataGroupUser::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataGroupUser::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataGroupUser::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataGroupUser::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insert(DataGroupUser bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataGroupUser bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataGroupUser entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public List<String> getUserIdsByGroupId(String groupId) {
		return baseMapper.getUserIdsByGroupId(groupId);
	}
}
