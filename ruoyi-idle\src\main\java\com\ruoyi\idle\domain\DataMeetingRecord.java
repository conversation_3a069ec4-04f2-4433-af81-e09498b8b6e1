package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 会议记录 对象 data_meeting_record
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_meeting_record")
public class DataMeetingRecord implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;

	/**
	 * 会议记录唯一编号
	 */
	@ApiModelProperty(value = "会议记录唯一编号")
	private String meetingUuid;

	/**
	 * 巡查任务名称
	 */
	@ApiModelProperty(value = "巡查任务名称")
	@Excel(name = "巡查任务名称")
	private String taskName;

	/**
	 * 会议创建人编号
	 */
	@ApiModelProperty(value = "会议创建人编号")
	private String createUserId;

	/**
	 * 会议创建人名称
	 */
	@ApiModelProperty(value = "会议创建人名称")
	@Excel(name = "创建人名称")
	private String createUserName;

	/**
	 * 会议创建人联系电话
	 */
	@ApiModelProperty(value = "会议创建人联系电话")
	@Excel(name = "创建人联系电话")
	private String createUserPhone;

	/**
	 * 会议号
	 */
	@ApiModelProperty(value = "会议号")
	@Excel(name = "会议号")
	private String meetingNum;

	/**
	 * 会议什么编号
	 */
	@ApiModelProperty(value = "会议什么编号")
	private String meetingLongId;

	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	@Excel(name = "经度")
	private Double longitude;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	@Excel(name = "纬度")
	private Double latitude;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
