package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataMeetingUser;
import com.ruoyi.idle.domain.bo.DataMeetingUserQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 与会人员 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
public interface IDataMeetingUserService extends IServicePlus<DataMeetingUser> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataMeetingUser queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataMeetingUser> queryPageList(DataMeetingUserQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataMeetingUser> queryList(DataMeetingUserQueryBo bo);

	/**
	 * 根据新增业务对象插入与会人员
	 *
	 * @param bo 与会人员 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataMeetingUser bo);

	/**
	 * 根据编辑业务对象修改与会人员
	 *
	 * @param bo 与会人员 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataMeetingUser bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
