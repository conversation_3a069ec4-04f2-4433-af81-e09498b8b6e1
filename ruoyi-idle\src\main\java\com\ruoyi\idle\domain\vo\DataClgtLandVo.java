package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 闲置土地 视图对象 data_clgt_land
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@ApiModel("闲置土地 视图对象")
public class DataClgtLandVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty("编号")
	private String id;

	/**
	 * 闲置土地所在市
	 */
	@Excel(name = "闲置土地所在市")
	@ApiModelProperty("闲置土地所在市")
	private String city;

	/**
	 * 闲置土地所在县代码
	 */
	@Excel(name = "闲置土地所在县代码")
	@ApiModelProperty("闲置土地所在县代码")
	private String countyCode;

	/**
	 * 闲置土地所在县名称
	 */
	@Excel(name = "闲置土地所在县名称")
	@ApiModelProperty("闲置土地所在县名称")
	private String countyName;

	/**
	 * 合同编号
	 */
	@Excel(name = "合同编号")
	@ApiModelProperty("合同编号")
	private String contractNo;

	/**
	 * 供应方式
	 */
	@Excel(name = "供应方式")
	@ApiModelProperty("供应方式")
	private String supplyType;

	/**
	 * 项目名称
	 */
	@Excel(name = "项目名称")
	@ApiModelProperty("项目名称")
	private String projectName;

	/**
	 * 数据年度
	 */
	@Excel(name = "数据年度")
	@ApiModelProperty("数据年度")
	private String year;

	/**
	 * 数据季度
	 */
	@Excel(name = "数据季度")
	@ApiModelProperty("数据季度")
	private String quarter;

	/**
	 * 电子监管号
	 */
	@Excel(name = "电子监管号")
	@ApiModelProperty("电子监管号")
	private String supervisionNo;

	/**
	 * 土地用途
	 */
	@Excel(name = "土地用途")
	@ApiModelProperty("土地用途")
	private String landUse;

	/**
	 * 土地面积（公顷）
	 */
	@Excel(name = "土地面积", readConverterExp = "公=顷")
	@ApiModelProperty("土地面积（公顷）")
	private BigDecimal landArea;

	/**
	 * 签订日期
	 */
	@Excel(name = "签订日期", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("签订日期")
	private Date signDate;

	/**
	 * 约定动工时间
	 */
	@Excel(name = "约定动工时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("约定动工时间")
	private Date agreedStartTime;

	/**
	 * 实际动工时间
	 */
	@Excel(name = "实际动工时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("实际动工时间")
	private Date actualStartTime;

	/**
	 * 实际竣工时间
	 */
	@Excel(name = "实际竣工时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("实际竣工时间")
	private Date actualEndTime;

	/**
	 * 闲置状态
	 */
	@Excel(name = "闲置状态")
	@ApiModelProperty("闲置状态")
	private String idleStatus;

	/**
	 * 图斑编号
	 */
	@Excel(name = "图斑编号")
	@ApiModelProperty("图斑编号")
	private String spotNumber;

	/**
	 * 内业备注
	 */
	@Excel(name = "内业备注")
	@ApiModelProperty("内业备注")
	private String insideRemark;

	/**
	 * 消化类型
	 */
	@Excel(name = "消化类型")
	@ApiModelProperty("消化类型")
	private String digestionType;

	/**
	 * 项目详细位置
	 */
	@Excel(name = "项目详细位置")
	@ApiModelProperty("项目详细位置")
	private String address;

	/**
	 * 项目空间数据
	 */
	@Excel(name = "项目空间数据")
	@ApiModelProperty("项目空间数据")
	private String geoData;

	/**
	 * 任务编号
	 */
	@Excel(name = "任务编号")
	@ApiModelProperty("任务编号")
	private String taskNo;

	/**
	 * 宗地号
	 */
	@Excel(name = "宗地号")
	@ApiModelProperty("宗地号")
	private String parcelNum;

	/**
	 * 项目中心点经度
	 */
	@Excel(name = "项目中心点经度")
	@ApiModelProperty("项目中心点经度")
	private String longitude;

	/**
	 * 项目中心点纬度
	 */
	@Excel(name = "项目中心点纬度")
	@ApiModelProperty("项目中心点纬度")
	private String latitude;

	/**
	 * 是否已分配巡查任务
	 */
	@Excel(name = "是否已分配巡查任务")
	@ApiModelProperty("是否已分配巡查任务")
	private String isAllot;

	/**
	 * 不动产权证号
	 */
	@Excel(name = "不动产权证号")
	@ApiModelProperty("不动产权证号")
	private String warrantNum;

	/**
	 * 备注
	 */
	@Excel(name = "备注")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 删除标记
	 */
	@Excel(name = "删除标记")
	@ApiModelProperty("删除标记")
	private String deleteFlag;


}
