<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataUnprovidedVerifyMapper">

    <resultMap type="com.ruoyi.idle.domain.DataUnprovidedVerify" id="DataUnprovidedVerifyResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="unprovidedId" column="unprovided_id"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="projectName" column="project_name"/>
        <result property="year" column="year"/>
        <result property="quarter" column="quarter"/>
        <result property="address" column="address"/>
        <result property="supplyType" column="supply_type"/>
        <result property="supervisionNo" column="supervision_no"/>
        <result property="contractNo" column="contract_no"/>
        <result property="contractName" column="contract_name"/>
        <result property="parcelNum" column="parcel_num"/>
        <result property="landArea" column="land_area"/>
        <result property="landUse" column="land_use"/>
        <result property="hasProjectDoc" column="has_project_doc"/>
        <result property="hasApprovalPlan" column="has_approval_plan"/>
        <result property="hasPlanCondition" column="has_plan_condition"/>
        <result property="hasSupplyPost" column="has_supply_post"/>
        <result property="hasResultPublicity" column="has_result_publicity"/>
        <result property="hasDealBook" column="has_deal_book"/>
        <result property="handleIsCorrect" column="handle_is_correct"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
