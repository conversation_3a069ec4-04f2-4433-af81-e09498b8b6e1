package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 岗位表 sys_post
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_post")
public class SysPost implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 岗位序号
	 */
	@Excel(name = "岗位序号", cellType = ColumnType.NUMERIC)
	@TableId(value = "post_id", type = IdType.ASSIGN_UUID)
	private String postId;

	/**
	 * 岗位编码
	 */
	@Excel(name = "岗位编码")
	@NotBlank(message = "岗位编码不能为空")
	@Size(min = 0, max = 64, message = "岗位编码长度不能超过64个字符")
	private String postCode;

	/**
	 * 岗位名称
	 */
	@Excel(name = "岗位名称")
	@NotBlank(message = "岗位名称不能为空")
	@Size(min = 0, max = 50, message = "岗位名称长度不能超过50个字符")
	private String postName;

	/**
	 * 岗位排序
	 */
	@Excel(name = "岗位排序")
	@NotBlank(message = "显示顺序不能为空")
	private String postSort;

	/**
	 * 状态（0正常 1停用）
	 */
	@Excel(name = "状态", readConverterExp = "0=正常,1=停用")
	private String status;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 请求参数
	 */
	@TableField(exist = false)
	private Map<String, Object> params = new HashMap<>();

	/**
	 * 用户是否存在此岗位标识 默认不存在
	 */
	@TableField(exist = false)
	private boolean flag = false;

}
