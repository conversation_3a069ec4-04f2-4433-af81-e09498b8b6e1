package com.ruoyi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.oas.annotations.EnableOpenApi;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication
@EnableOpenApi
@EnableAsync
@Slf4j
public class RuoYiApplication {
	public static void main(String[] args) {
//		System.setProperty("spring.devtools.restart.enabled", "false");
//		SpringApplication.run(RuoYiApplication.class, args);


		System.setProperty("spring.devtools.restart.enabled", "false");
		SpringApplication application = new SpringApplication(RuoYiApplication.class);
		application.setApplicationStartup(new BufferingApplicationStartup(2048));
		Environment env = application.run(args).getEnvironment();

		//Environment env = app.run(args).getEnvironment();
		System.out.println("服务地址: http://127.0.0.1:" + env.getProperty("server.port"));
		System.out.println("服务地址: " + getIp() + ":" + env.getProperty("server.port"));
		System.out.println("开发地址: " + getIp() + ":" + env.getProperty("server.port") + "/doc.html");
		System.out.println("(♥◠‿◠)ﾉﾞ  clgt 巡查后台管理系统启动成功   ლ(´ڡ`ლ)ﾞ");
//        log.error("测试修改后的东西");
	}


	// 获取项目IP
	private static String getIp() {
		InetAddress address = null;
		try {
			address = InetAddress.getLocalHost();
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}

		return "http://" + address.getHostAddress();
	}
}
