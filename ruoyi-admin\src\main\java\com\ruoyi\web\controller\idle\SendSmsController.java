package com.ruoyi.web.controller.idle;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.idle.service.ISendSmsService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * @Created with IntelliJ IDEA.
 * @Title: SendSmsController
 * @Description: com.ruoyi.web.controller.idle
 * @Author: HongDeng
 * @Date: 2021/7/16 9:35
 * @Version: 1.0.0
 **/
@Api(value = "发送短信控制器", tags = {"发送短信管理"})
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("/idle/sendSms")
public class SendSmsController {
	private final ISendSmsService sendSmsService;

	// 注入redis操作模板
	private final RedisTemplate<String, String> redisTemplate;

	private final ISysUserService sysUserService;

	@GetMapping("/send")
	@ApiOperation("发送验证码")
	@ApiImplicitParam(name = "phoneNum", value = "用户手机号码", dataTypeClass = String.class, required = true)
	public AjaxResult sendSms(@RequestParam("phoneNum") String phoneNum) {
		SysUser user = sysUserService.getOne(Wrappers.<SysUser>lambdaQuery()
			.eq(SysUser::getDelFlag, 0).eq(SysUser::getPhonenumber, phoneNum));
		if (user != null) {
			if (user.getStatus().equals("1")) {
				return AjaxResult.error("该账户已停用");
			} else if (user.getStatus().equals("2")) {
				return AjaxResult.error("该账号正在审核阶段");
			}
		} else {
			return AjaxResult.error("用户不存在");
		}
		// 获取到操作String的对象
		ValueOperations<String, String> stringR = redisTemplate.opsForValue();

		// 根据手机号进行查询
		String phone = stringR.get(phoneNum);

		// 如果手机号在redis中不存在的话才进行发送验证码操作
		if (StringUtils.isEmpty(phone)) {
			// 生成6位随机数
			String code = String.valueOf(Math.random()).substring(5, 9);
			long time = 2;
			// 调用业务层接口 发送验证码
			boolean sendSmsFlag = sendSmsService.sendSms(phoneNum, code);
			//System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+code);
			if (sendSmsFlag) {
				// 发送成功之后往redis中存入该手机号以及验证码 并设置超时时间 3 分钟
				stringR.set(phoneNum, code, 3, TimeUnit.MINUTES);
			}
			return AjaxResult.success("验证码发送成功!");
		} else {
			//redisTemplate.getExpire(phoneNum);
			return AjaxResult.success("验证码已发送!");
		}
	}

	@GetMapping("/checkCode")
	@ApiOperation("手机短信验证")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "phoneNum", value = "用户手机号码", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "code", value = "短信验证码", dataTypeClass = String.class, required = true),
	})
	public AjaxResult checkCode(@RequestParam("phoneNum") String phoneNum, @RequestParam("code") String code) {
		// 获取到操作String的对象
		ValueOperations<String, String> stringR = redisTemplate.opsForValue();
		// 根据Key进行查询
		String redisCode = stringR.get(phoneNum);
		if (code.equals(redisCode)) {
			return AjaxResult.success("验证成功");
		} else {
			return AjaxResult.error(redisCode == null ? "请先发送验证码!" : "验证码错误");
		}
	}
}
