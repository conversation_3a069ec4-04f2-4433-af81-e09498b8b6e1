package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.idle.domain.DataClgtLand;
import com.ruoyi.idle.domain.DataHouseCheck;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.bo.DataClgtLandQueryBo;
import com.ruoyi.idle.domain.vo.DataClgtLandVo;
import com.ruoyi.idle.domain.vo.ImportClgtAndHouseVo;
import com.ruoyi.idle.mapper.DataClgtLandMapper;
import com.ruoyi.idle.mapper.DataHouseCheckMapper;
import com.ruoyi.idle.service.IDataClgtLandService;
import com.ruoyi.idle.service.IDataHouseCheckService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.web.multipart.MultipartFile;


import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 闲置土地 Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class DataClgtLandServiceImpl extends ServicePlusImpl<DataClgtLandMapper, DataClgtLand> implements IDataClgtLandService {

	@Autowired
	private  DataClgtLandMapper dataClgtLandMapper;

	@Autowired
	DataHouseCheckMapper dataHouseCheckMapper;

	@Override
	public String selectSupervisionNoByTaskId(String taskId) {
		return baseMapper.selectSupervisionNoByTaskId(taskId);
	}

	@Override
	public DataClgtLand queryById(String id) {
		return getVoById(id, DataClgtLand.class);
	}

	@Override
	public List<DataClgtLand> appPulList(String regionCode, String userId){
		return dataClgtLandMapper.appPulList(regionCode,userId);
	}

	@Override
	public TableDataInfo<DataClgtLandVo> queryPageList(DataClgtLandQueryBo bo) {
		PagePlus<DataClgtLand, DataClgtLandVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataClgtLandVo.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataClgtLandVo> queryList(DataClgtLandQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataClgtLandVo.class);
	}

	private LambdaQueryWrapper<DataClgtLand> buildQueryWrapper(DataClgtLandQueryBo bo) {
		Map<String, Object> params = bo.getParams();
		LambdaQueryWrapper<DataClgtLand> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getCity()), DataClgtLand::getCity, bo.getCity());
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataClgtLand::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataClgtLand::getCountyName, bo.getCountyName());
		lqw.eq(StrUtil.isNotBlank(bo.getContractNo()), DataClgtLand::getContractNo, bo.getContractNo());
		lqw.eq(StrUtil.isNotBlank(bo.getSupplyType()), DataClgtLand::getSupplyType, bo.getSupplyType());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataClgtLand::getProjectName, bo.getProjectName());
		lqw.eq(StrUtil.isNotBlank(bo.getYear()), DataClgtLand::getYear, bo.getYear());
		lqw.eq(StrUtil.isNotBlank(bo.getQuarter()), DataClgtLand::getQuarter, bo.getQuarter());
		lqw.eq(StrUtil.isNotBlank(bo.getSupervisionNo()), DataClgtLand::getSupervisionNo, bo.getSupervisionNo());
		lqw.eq(StrUtil.isNotBlank(bo.getLandUse()), DataClgtLand::getLandUse, bo.getLandUse());
		lqw.eq(bo.getLandArea() != null, DataClgtLand::getLandArea, bo.getLandArea());
		lqw.eq(bo.getSignDate() != null, DataClgtLand::getSignDate, bo.getSignDate());
		lqw.eq(bo.getAgreedStartTime() != null, DataClgtLand::getAgreedStartTime, bo.getAgreedStartTime());
		lqw.eq(bo.getActualStartTime() != null, DataClgtLand::getActualStartTime, bo.getActualStartTime());
		lqw.eq(bo.getActualEndTime() != null, DataClgtLand::getActualEndTime, bo.getActualEndTime());
		lqw.eq(StrUtil.isNotBlank(bo.getIdleStatus()), DataClgtLand::getIdleStatus, bo.getIdleStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getSpotNumber()), DataClgtLand::getSpotNumber, bo.getSpotNumber());
		lqw.eq(StrUtil.isNotBlank(bo.getInsideRemark()), DataClgtLand::getInsideRemark, bo.getInsideRemark());
		lqw.eq(StrUtil.isNotBlank(bo.getDigestionType()), DataClgtLand::getDigestionType, bo.getDigestionType());
		lqw.eq(StrUtil.isNotBlank(bo.getAddress()), DataClgtLand::getAddress, bo.getAddress());
		lqw.eq(StrUtil.isNotBlank(bo.getGeoData()), DataClgtLand::getGeoData, bo.getGeoData());
		lqw.eq(StrUtil.isNotBlank(bo.getParcelNum()), DataClgtLand::getParcelNum, bo.getParcelNum());
		lqw.eq(StrUtil.isNotBlank(bo.getLongitude()), DataClgtLand::getLongitude, bo.getLongitude());
		lqw.eq(StrUtil.isNotBlank(bo.getLatitude()), DataClgtLand::getLatitude, bo.getLatitude());
		lqw.eq(StrUtil.isNotBlank(bo.getIsAllot()), DataClgtLand::getIsAllot, bo.getIsAllot());
		lqw.eq(StrUtil.isNotBlank(bo.getWarrantNum()), DataClgtLand::getWarrantNum, bo.getWarrantNum());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataClgtLand::getDeleteFlag, bo.getDeleteFlag());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataClgtLand bo) {
		DataClgtLand add = BeanUtil.toBean(bo, DataClgtLand.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataClgtLand bo) {
		DataClgtLand update = BeanUtil.toBean(bo, DataClgtLand.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataClgtLand entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public Boolean importData(MultipartFile file) throws IOException {
		//获取文件的输入流
		int insert=0;
		InputStream inputStream = file.getInputStream();
		List<ImportClgtAndHouseVo> list = EasyExcel.read(inputStream) //调用read方法
			//注册自定义监听器，字段校验可以在监听器内实现
			.head(ImportClgtAndHouseVo.class) //对应导入的实体类
			.sheet(0) //导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
			.headRowNumber(1) //列表头行数，1代表列表头有1行，第二行开始为数据行
			.doReadSync(); //开始读Excel，返回一个List<T>集合，继续后续入库操作
		if (ObjectUtil.isNotNull(list)) {
			for (ImportClgtAndHouseVo l:list) {
				DataClgtLand dataClgtLand = new DataClgtLand();
				dataClgtLand.setCity(l.getCity());
				dataClgtLand.setCountyName(l.getCountyName());
				dataClgtLand.setCountyCode(l.getCountyCode());
				dataClgtLand.setContractNo(l.getContractNo());
				dataClgtLand.setSupplyType(l.getSupplyType());
				dataClgtLand.setProjectName(l.getProjectName());
				dataClgtLand.setYear(l.getYear());
				dataClgtLand.setQuarter(l.getQuarter());
				dataClgtLand.setSupervisionNo(l.getSupervisionNo());
				dataClgtLand.setLandUse(l.getLandUse());
				dataClgtLand.setLandArea(l.getLandArea());
				dataClgtLand.setSignDate(l.getSignDate());
				dataClgtLand.setAgreedStartTime(l.getAgreedStartTime());
				dataClgtLand.setActualStartTime(l.getActualStartTime());
				dataClgtLand.setActualEndTime(l.getActualEndTime());
				dataClgtLand.setIdleStatus(l.getIdleStatus());
				dataClgtLand.setSpotNumber(l.getSpotNumber());
				dataClgtLand.setInsideRemark(l.getInsideRemark());
				dataClgtLand.setDigestionType(l.getDigestionType());
				dataClgtLand.setGeoData(l.getGeoData());
				dataClgtLand.setParcelNum(l.getParcelNum());
				dataClgtLand.setLatitude(l.getLatitude());
				dataClgtLand.setLongitude(l.getLongitude());
				dataClgtLand.setIsAllot(l.getIsAllot());
				dataClgtLand.setWarrantNum(l.getWarrantNum());
				dataClgtLand.setRemark(l.getRemark());
				insert = baseMapper.insert(dataClgtLand);
				//房屋调查表
				DataHouseCheck houseCheck = new DataHouseCheck();
				houseCheck.setJzbh(l.getJzbh());
				houseCheck.setClgtId(dataClgtLand.getId());
				houseCheck.setMc(l.getMc());
				houseCheck.setJdmj(l.getJdmj());
				houseCheck.setDscs(l.getDscs());
				houseCheck.setDsjzmj(l.getDsjzmj());
				houseCheck.setJzgd(l.getJzgd());
				houseCheck.setYxjz(StringUtils.isNotBlank(l.getYxjz()) ?DictUtils.getDictValue("yxjz",l.getYxjz()):l.getYxjz());
				houseCheck.setFwts(l.getFwts());
				houseCheck.setXxdz(l.getXxdz());
				houseCheck.setJznd(l.getJznd());
				houseCheck.setDcsj(l.getDcsj());
				houseCheck.setJzzt(StringUtils.isNotBlank(l.getJzzt()) ?DictUtils.getDictValue("jzzt",l.getJzzt()):l.getJzzt());
				houseCheck.setJglx(StringUtils.isNotBlank(l.getJglx()) ?DictUtils.getDictValue("jglx",l.getJglx()):l.getJglx());
				houseCheck.setSjyt(StringUtils.isNotBlank(l.getSjyt()) ?DictUtils.getDictValue("fwyt",l.getSjyt()):l.getSjyt());
				houseCheck.setGhyt(StringUtils.isNotBlank(l.getGhyt()) ?DictUtils.getDictValue("fwyt",l.getGhyt()):l.getGhyt());
				houseCheck.setJgyt(StringUtils.isNotBlank(l.getJgyt()) ?DictUtils.getDictValue("fwyt",l.getJgyt()):l.getJgyt());
				houseCheck.setJzfcyt(StringUtils.isNotBlank(l.getJzfcyt()) ?DictUtils.getDictValue("fwyt",l.getJzfcyt()):l.getJzfcyt());
				houseCheck.setFytlcd(l.getFytlcd());
				houseCheck.setFytjzmj(l.getFytjzmj());
				houseCheck.setDxcs(l.getDxcs());
				houseCheck.setDxjzmj(l.getDxjzmj());
				houseCheck.setFwxz(StringUtils.isNotBlank(l.getFwxz()) ?DictUtils.getDictValue("fwxz",l.getFwxz()):l.getFwxz());
				houseCheck.setXzsj(l.getXzsj());
				houseCheck.setYdsyq(StringUtils.isNotBlank(l.getYdsyq()) ?DictUtils.getDictValue("qllx",l.getYdsyq()):l.getYdsyq());
				houseCheck.setTxsm(l.getTxsm());
				dataHouseCheckMapper.insert(houseCheck);
			}
		}
		return insert>0;
	}

	@Override
	public Boolean importDataByList(List<ImportClgtAndHouseVo> boList) throws IOException {
		int successCount = 0;
		StringBuilder failMsg = new StringBuilder();
		for (ImportClgtAndHouseVo l : boList) {
			DataClgtLand dataClgtLand = new DataClgtLand();
			dataClgtLand.setCity(l.getCity());
			dataClgtLand.setCountyName(l.getCountyName());
			dataClgtLand.setCountyCode(l.getCountyCode());
			dataClgtLand.setContractNo(l.getContractNo());
			dataClgtLand.setSupplyType(l.getSupplyType());
			dataClgtLand.setProjectName(l.getProjectName());
			dataClgtLand.setYear(l.getYear());
			dataClgtLand.setQuarter(l.getQuarter());
			dataClgtLand.setSupervisionNo(l.getSupervisionNo());
			dataClgtLand.setLandUse(l.getLandUse());
			dataClgtLand.setLandArea(l.getLandArea());
			dataClgtLand.setSignDate(l.getSignDate());
			dataClgtLand.setAgreedStartTime(l.getAgreedStartTime());
			dataClgtLand.setActualStartTime(l.getActualStartTime());
			dataClgtLand.setActualEndTime(l.getActualEndTime());
			dataClgtLand.setIdleStatus(l.getIdleStatus());
			dataClgtLand.setSpotNumber(l.getSpotNumber());
			dataClgtLand.setInsideRemark(l.getInsideRemark());
			dataClgtLand.setDigestionType(l.getDigestionType());
			dataClgtLand.setGeoData(l.getGeoData());
			dataClgtLand.setParcelNum(l.getParcelNum());
			dataClgtLand.setLatitude(l.getLatitude());
			dataClgtLand.setLongitude(l.getLongitude());
			dataClgtLand.setIsAllot(l.getIsAllot());
			dataClgtLand.setWarrantNum(l.getWarrantNum());
			dataClgtLand.setRemark(l.getRemark());
			//房屋调查表
			DataHouseCheck houseCheck = new DataHouseCheck();
			houseCheck.setJzbh(l.getJzbh());
			houseCheck.setClgtId(dataClgtLand.getId());
			houseCheck.setMc(l.getMc());
			houseCheck.setJdmj(l.getJdmj());
			houseCheck.setDscs(l.getDscs());
			houseCheck.setDsjzmj(l.getDsjzmj());
			houseCheck.setJzgd(l.getJzgd());
			houseCheck.setYxjz(l.getYxjz());
			houseCheck.setFwts(l.getFwts());
			houseCheck.setXxdz(l.getXxdz());
			houseCheck.setJznd(l.getJznd());
			houseCheck.setDcsj(l.getDcsj());
			houseCheck.setJzzt(l.getJzzt());
			houseCheck.setJglx(l.getJglx());
			houseCheck.setSjyt(l.getSjyt());
			houseCheck.setGhyt(l.getGhyt());
			houseCheck.setJgyt(l.getJgyt());
			houseCheck.setJzfcyt(l.getJzfcyt());
			houseCheck.setFytlcd(l.getFytlcd());
			houseCheck.setFytjzmj(l.getFytjzmj());
			houseCheck.setDxcs(l.getDxcs());
			houseCheck.setDxjzmj(l.getDxjzmj());
			houseCheck.setFwxz(l.getFwxz());
			houseCheck.setXzsj(l.getXzsj());
			houseCheck.setYdsyq(l.getYdsyq());
			houseCheck.setTxsm(l.getTxsm());

			// 先根据电子监管号判断是否已经存在该项目
			LambdaQueryWrapper<DataClgtLand> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataClgtLand::getSupervisionNo, l.getSupervisionNo());
			queryWrapper.eq(DataClgtLand::getYear, l.getYear());
			queryWrapper.eq(DataClgtLand::getQuarter, l.getQuarter());
			DataClgtLand dataClgtLand1 = this.getOne(queryWrapper,false);
			//若存在项目是否存在查询房屋核查信息
			DataHouseCheck dataHouseCheck = dataHouseCheckMapper.selectByClgtId(dataClgtLand1.getId());
			if(dataHouseCheck !=null){
				houseCheck.setId(dataHouseCheck.getId());
				int i = dataHouseCheckMapper.updateById(houseCheck);
			}

			if (dataClgtLand1 != null) {
				dataClgtLand.setId(dataClgtLand1.getId());
				successCount = baseMapper.updateById(dataClgtLand);
				failMsg.append(l.getSupervisionNo()).append(",");
			} else {
				String contractNo = dataClgtLand.getContractNo();
				if (StringUtils.isNotBlank(contractNo)){
					if (contractNo.contains("CR") || contractNo.contains("cr") || contractNo.contains("Cr") || contractNo.contains("cR")){
						dataClgtLand.setSupplyType("CR");
					}else if (contractNo.contains("HB") || contractNo.contains("hb") || contractNo.contains("Hb") || contractNo.contains("hB")){
						dataClgtLand.setSupplyType("HB");
					}
				}
				dataClgtLand.setCreateBy(SecurityUtils.getUsername());
				dataClgtLand.setDeleteFlag("0");
				if (dataClgtLand.getIsAllot() == null) dataClgtLand.setIsAllot("0");
				successCount = baseMapper.insert(dataClgtLand);

				//保存房屋调查信息
				houseCheck.setClgtId(dataClgtLand.getId());
				int insert = dataHouseCheckMapper.insert(houseCheck);
			}
		}
		return successCount>0;
	}

	@Override
	public String taskNo() {
		String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		Map<String, Object> t = baseMapper.getTaskNo(date);
		return date + String.format("%03d", Integer.parseInt(t.get("taskNo").toString().substring(8, 11)) + 1);
	}
}
