package com.ruoyi.web.controller.idle;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.DataAttachment;
import com.ruoyi.idle.domain.DataDigestType;
import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.bo.DataAttachmentQueryBo;
import com.ruoyi.idle.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.message.ReusableMessage;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 巡查附件 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查附件控制器", tags = {"巡查附件管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/attachment")
public class DataAttachmentController extends BaseController {

	private final IDataAttachmentService iDataAttachmentService;

	private final IDataFileCatalogService iDataFileCatalogService;

	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final IDataIdleLandService idleLandService;

	private final TokenService tokenService;

	private final IDataDigestTypeService digestTypeService;

	private final IDataClgtLandService iDataClgtLandService;

	@Autowired
	private RedisCache redisCache;

	/**
	 * 查询巡查附件 列表
	 */
	@ApiOperation("查询巡查附件列表")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@GetMapping("/list")
	public TableDataInfo<DataAttachment> list(@Validated DataAttachmentQueryBo bo) {
		return iDataAttachmentService.queryPageList(bo);
	}

	/**
	 * 根据巡查任务编号获取对应的巡查附件
	 */
	@ApiOperation("根据任务编号获取巡查附件")
//	@PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@GetMapping("/patrolFileList")
	public TableDataInfo<DataAttachment> getPatrolFileList(String taskId) {
		DataAttachmentQueryBo bo = new DataAttachmentQueryBo();
		bo.setPatrolTaskId(taskId);
		bo.setIsSiteData("1");
		return iDataAttachmentService.queryPageList(bo);
	}

	/**
	 * 导出巡查附件 列表
	 */
	@ApiOperation("导出巡查附件列表")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:export')")
	@Log(title = "巡查附件", businessType = BusinessType.EXPORT)
	@ApiIgnore
	@GetMapping("/export")
	public AjaxResult<DataAttachment> export(@Validated DataAttachmentQueryBo bo) {
		List<DataAttachment> list = iDataAttachmentService.queryList(bo);
		ExcelUtil<DataAttachment> util = new ExcelUtil<DataAttachment>(DataAttachment.class);
		return util.exportExcel(list, "巡查附件");
	}

	/**
	 * 获取巡查附件 详细信息
	 */
	@ApiOperation("获取巡查附件详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataAttachment> getInfo(@NotNull(message = "主键不能为空")
											  @PathVariable("id") String id) {
		return AjaxResult.success(iDataAttachmentService.queryById(id));
	}

	/**
	 * 新增巡查附件
	 */
	@ApiOperation("新增巡查附件")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:add')")
	@Log(title = "巡查附件", businessType = BusinessType.INSERT)
	@ApiIgnore
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataAttachment bo) {
		bo.setCreateBy(SecurityUtils.getUsername());
		bo.setDeleteFlag("0");
		return toAjax(iDataAttachmentService.insert(bo) ? 1 : 0);
	}

	/**
	 * 修改巡查附件
	 */
	@ApiOperation("修改巡查附件")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:edit')")
	@Log(title = "巡查附件", businessType = BusinessType.UPDATE)
	@ApiIgnore
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataAttachment bo) {
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iDataAttachmentService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除巡查附件
	 */
	@ApiOperation("删除巡查附件")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:remove')")
	@Log(title = "巡查附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataAttachmentService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	/**
	 * 上传房屋调查图片
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:upload')")
	@Log(title = "上传房屋调查图片")
	@PostMapping("/upload/houseFile")
	@ApiOperation("上传房屋调查图片")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "fileList", value = "上传的文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true),
		@ApiImplicitParam(name = "taskId", value = "任务编号", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "catalogId", value = "目录编号", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "isSiteData", value = "是否现场照片或视频", dataTypeClass = String.class, required = false),
		@ApiImplicitParam(name = "longitude", value = "经度", dataTypeClass = Double.class, required = true),
		@ApiImplicitParam(name = "latitude", value = "纬度", dataTypeClass = Double.class, required = true),
		@ApiImplicitParam(name = "angle", value = "角度", dataTypeClass = Double.class, required = true)
	})

	public AjaxResult uploadHouseFile(@RequestPart(value = "fileList") MultipartFile[] fileList,
									  @RequestParam(value = "taskId") String taskId,
									  @RequestParam(value = "catalogId") String catalogId,
									  @RequestParam(value = "isSiteData") String isSiteData,
									  @RequestParam(value = "longitude") Double longitude,
									  @RequestParam(value = "latitude") Double latitude,
									  @RequestParam(value = "angle") Double angle) {
		Map<String, Boolean> resultMap = new HashMap<>();
		boolean isSuccess = false;
		try {
			for (MultipartFile file : fileList) {
				if (file != null) {
					String fileName = file.getOriginalFilename();
					String fileNameNoExt = fileName.replaceAll("[.][^.]+$", "");
					// step.1 判断相同文件名称的记录是否存在并删除
					iDataAttachmentService.realDeleteRecord(fileNameNoExt, taskId);
					// 根据巡查任务编号获取对应闲置土地的电子监管号
					String supervisionNo = taskId;
					String tempStr = iDataClgtLandService.selectSupervisionNoByTaskId(taskId);
					supervisionNo = StringUtils.isNotBlank(tempStr) ? tempStr : taskId;
					// step.2 上传文件到服务器
					String vdrul = FileUploadUtils.uploadIdleFile(RuoYiConfig.getUploadPath(), file, taskId, supervisionNo, "1");
					//得保存到数据库
					String type = file.getContentType();
					DataAttachment sysFile = new DataAttachment();
					sysFile.setName(fileNameNoExt);
					sysFile.setSize(String.valueOf(file.getSize()));
					sysFile.setUrl(vdrul);
					String location = vdrul.substring(8);
					sysFile.setLocation(location);
					sysFile.setPatrolTaskId(taskId);
					sysFile.setCatalogId(catalogId);
					if (StringUtils.isNotBlank(isSiteData)) {
						sysFile.setIsSiteData(isSiteData);
					} else {
						sysFile.setIsSiteData("1");
					}
					sysFile.setStatus("0");
					sysFile.setLongitude(longitude);
					sysFile.setLatitude(latitude);
					sysFile.setAngle(angle);
					sysFile.setType(getFileType(fileName));
					LoginUser loginUser = SecurityUtils.getLoginUser();
					if (loginUser != null) {
						//设置创建的用户和创建时间
						sysFile.setCreateBy(loginUser.getUsername());
					}
					sysFile.setDeleteFlag("0");
					sysFile.setRemark(fileName);
					// step.3 保存文件记录到数据库
					isSuccess = iDataAttachmentService.save(sysFile);
					//changeIsPatrol(taskId);
					resultMap.put(fileName, isSuccess);
				}
			}
		} catch (IOException e1) {
			e1.printStackTrace();
			return AjaxResult.error("文件上传失败");
		}
		return isSuccess ? AjaxResult.success("文件上传成功") : AjaxResult.error("数据保存失败");
	}

	/**
	 * 上传项目附件
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:upload')")
	@Log(title = "巡查附件")
	@PostMapping("/upload/file")
	@ApiOperation("上传项目附件")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "fileList", value = "上传的文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true),
		@ApiImplicitParam(name = "taskId", value = "任务编号", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "catalogId", value = "目录编号", dataTypeClass = String.class, required = true)
	})

	public AjaxResult uploadFile(@RequestPart(value = "fileList") MultipartFile[] fileList,
								 @RequestParam(value = "taskId") String taskId,
								 @RequestParam(value = "catalogId") String catalogId) {
		Map<String, Boolean> resultMap = new HashMap<>();
		boolean isSuccess = false;
		try {
			for (MultipartFile file : fileList) {
				if (file != null) {
					String fileName = file.getOriginalFilename();
					// 先根据taskId 判断一下任务类型
					DataPatrolTask tempTask = iDataPatrolTaskService.getById(taskId);
					String supervisionNo = taskId;
					if (tempTask.getProjectType().equals("0")) {
						// 闲置土地
						// 根据巡查任务编号获取对应闲置土地的电子监管号
						supervisionNo = idleLandService.selectSupervisionNoByTaskId(taskId, "0");
					} else {
						// 批而未供
						supervisionNo = idleLandService.selectSupervisionNoByTaskId(taskId, "1");
					}

					String vdrul = FileUploadUtils.uploadIdleFile(RuoYiConfig.getUploadPath(), file, taskId, supervisionNo, "0");
					//得保存到数据库
					String type = file.getContentType();
					DataAttachment sysFile = new DataAttachment();
					sysFile.setName(fileName.replaceAll("[.][^.]+$", ""));
					sysFile.setSize(String.valueOf(file.getSize()));
					sysFile.setUrl(vdrul);
					String location = vdrul.substring(8);
					sysFile.setLocation(location);
					sysFile.setPatrolTaskId(taskId);
					sysFile.setCatalogId(catalogId);
					sysFile.setIsSiteData("0");
					sysFile.setStatus("0");
					sysFile.setType(getFileType(fileName));
					LoginUser loginUser = SecurityUtils.getLoginUser();
					if (loginUser != null) {
						//设置创建的用户和创建时间
						sysFile.setCreateBy(loginUser.getUsername());
					}
					sysFile.setDeleteFlag("0");
					sysFile.setRemark(fileName);
					isSuccess = iDataAttachmentService.save(sysFile);
					//changeIsPatrol(taskId);
					resultMap.put(fileName, isSuccess);
				}
			}
		} catch (IOException e1) {
			e1.printStackTrace();
			return AjaxResult.error("文件上传失败");
		}
		return isSuccess ? AjaxResult.success("文件上传成功") : AjaxResult.error("数据保存失败");
	}


	/**
	 * 上传巡查附件(考虑要不要使用@RepeatSubmit防止重复提交)
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:upload')")
	@Log(title = "巡查附件")
	@PostMapping("/upload/simpleFile")
	@ApiOperation("上传单个巡查附件")
	@Transactional
	@ApiImplicitParams({
		@ApiImplicitParam(name = "file", value = "上传的文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true),
		@ApiImplicitParam(name = "taskId", value = "任务编号", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "catalogId", value = "目录编号", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "isSiteData", value = "是否现场照片或视频", dataTypeClass = String.class, required = false),
		@ApiImplicitParam(name = "longitude", value = "经度", dataTypeClass = Double.class, required = true),
		@ApiImplicitParam(name = "latitude", value = "纬度", dataTypeClass = Double.class, required = true),
		@ApiImplicitParam(name = "angle", value = "角度", dataTypeClass = Double.class, required = true)
	})

	public AjaxResult uploadFile(@RequestPart(value = "file") MultipartFile file,
								 @RequestParam(value = "taskId") String taskId,
								 @RequestParam(value = "catalogId") String catalogId,
								 @RequestParam(value = "isSiteData") String isSiteData,
								 @RequestParam(value = "longitude") Double longitude,
								 @RequestParam(value = "latitude") Double latitude,
								 @RequestParam(value = "angle") Double angle) {
		Map<String, Boolean> resultMap = new HashMap<>();
		boolean isSuccess = false;
		try {
			if (file != null) {
				String fileName = file.getOriginalFilename();
				String fileNameNoExt = fileName.replaceAll("[.][^.]+$", "");
				// step.1 判断相同文件名称的记录是否存在并删除
				iDataAttachmentService.realDeleteRecord(fileNameNoExt, taskId);
				// 根据巡查任务编号获取对应闲置土地的电子监管号
				String supervisionNo = taskId;
				String tempStr = idleLandService.selectSupervisionNoByTaskId(taskId, "0");
				supervisionNo = StringUtils.isNotBlank(tempStr) ? tempStr : taskId;
				// step.2 上传文件到服务器
				String vdrul = FileUploadUtils.uploadIdleFile(RuoYiConfig.getUploadPath(), file, taskId, supervisionNo, "1");
				//得保存到数据库
				String type = file.getContentType();
				DataAttachment sysFile = new DataAttachment();
				sysFile.setName(fileNameNoExt);
				sysFile.setSize(String.valueOf(file.getSize()));
				sysFile.setUrl(vdrul);
				String location = vdrul.substring(8);
				sysFile.setLocation(location);
				sysFile.setPatrolTaskId(taskId);
				sysFile.setCatalogId(catalogId);
				if (StringUtils.isNotBlank(isSiteData)) {
					sysFile.setIsSiteData(isSiteData);
				} else {
					sysFile.setIsSiteData("1");
				}
				sysFile.setStatus("0");
				sysFile.setLongitude(longitude);
				sysFile.setLatitude(latitude);
				sysFile.setAngle(angle);
				sysFile.setType(getFileType(fileName));
				LoginUser loginUser = SecurityUtils.getLoginUser();
				if (loginUser != null) {
					//设置创建的用户和创建时间
					sysFile.setCreateBy(loginUser.getUsername());
				}
				sysFile.setDeleteFlag("0");
				sysFile.setRemark(fileName);
				// step.3 保存文件记录到数据库
				isSuccess = iDataAttachmentService.save(sysFile);
				//changeIsPatrol(taskId);
				resultMap.put(fileName, isSuccess);
			}
		} catch (IOException e1) {
			e1.printStackTrace();
			return AjaxResult.error("文件上传失败");
		}
		return isSuccess ? AjaxResult.success("文件上传成功") : AjaxResult.error("数据保存失败");
	}

	/**
	 * 改变巡查任务的状态
	 *
	 * @param taskId 巡查任务编号
	 */
	private void changeIsPatrol(String taskId) {
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (null == patrolTask) return;
		if (patrolTask.getIsPatrol().equals("0")) {
			patrolTask.setIsPatrol("2");
			iDataPatrolTaskService.updateById(patrolTask);
		}
	}


	private String getFileType(String fileName) {
		if (StringUtils.isNotBlank(fileName)) {
			int begin = fileName.lastIndexOf(".");
			int last = fileName.length();
			String fileType = fileName.substring(begin + 1, last);
			return fileType;
		}
		return "";
	}

	/**
	 * 获取树形文件目录结构
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@GetMapping(value = "/getFileTree")
	@Log(title = "巡查附件")
	@ApiOperation("获取树形文件目录结构")
	@ApiIgnore
	@ApiImplicitParams({
		@ApiImplicitParam(name = "catalogType", value = "文件目录类型", dataTypeClass = String.class, required = true),
		@ApiImplicitParam(name = "taskId", value = "巡查任务编号", dataTypeClass = String.class, required = true),
	})
	public AjaxResult getFileTree(@RequestParam(value = "catalogType", required = true) String catalogType,
								  @RequestParam(value = "taskId", required = true) String taskId) {

		if (StringUtils.isBlank(catalogType)) return AjaxResult.error("文件目录类型不能为空");
		if (null == taskId) return AjaxResult.error("巡查任务编号不能为空");
		List<Tree<String>> treeNodes = new ArrayList<>();
		// 先按照文件目录类型catalogType查询目录数据
//		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
//		queryWrapper.eq(DataFileCatalog::getCatalogType, catalogType);
//		List<DataFileCatalog> fileCatalogList = iDataFileCatalogService.list(queryWrapper);

		List<DataFileCatalog> fileCatalogList = null;
		String DataFileCatalogKey = "DataFileCatalog_catalogType_" + catalogType;
		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataFileCatalog::getCatalogType, catalogType);
		queryWrapper.orderByAsc(DataFileCatalog::getOrderNum);
		fileCatalogList = redisCache.getCacheObject(DataFileCatalogKey);
		if (fileCatalogList != null && fileCatalogList.size() > 0) {

		} else {
			fileCatalogList = iDataFileCatalogService.list(queryWrapper);
			if (fileCatalogList != null && fileCatalogList.size() > 0) {
				redisCache.setCacheObject(DataFileCatalogKey, fileCatalogList, 90, TimeUnit.MINUTES);
			}
		}

		if (fileCatalogList != null && fileCatalogList.size() != 0) {
			//配置
			TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
			// 自定义属性名 都要默认值的
			treeNodeConfig.setIdKey("id");
			// 最大递归深度
			// treeNodeConfig.setDeep(3);
			List<DataAttachment> fileListOfTask = new ArrayList<>();
			LambdaQueryWrapper<DataAttachment> queryWrapperDataAttachment = new LambdaQueryWrapper<>();
			//queryWrapperDataAttachment.eq(DataAttachment::getCatalogId, catalogId);
			queryWrapperDataAttachment.eq(DataAttachment::getPatrolTaskId, taskId);
			fileListOfTask = iDataAttachmentService.list(queryWrapperDataAttachment);
			fileListOfTask.forEach(item -> {
				item.setName(item.getName() + "." + item.getType());
			});

			//转换器
			List<DataAttachment> finalFileListOfTask = fileListOfTask;
			//转换器
			treeNodes = TreeUtil.build(fileCatalogList, "0", treeNodeConfig,
				(treeNode, tree) -> {
					tree.setId(treeNode.getId().toString());
					tree.setParentId(treeNode.getParentId().toString());
					tree.setName(treeNode.getName());
					// 扩展属性 ...
					tree.putExtra("catalogType", treeNode.getCatalogType());
					tree.putExtra("catalogName", treeNode.getName());
					tree.putExtra("remark", treeNode.getRemark());
					if (!treeNode.getParentId().equals("0")) {
						// 不是顶级节点
						List<DataAttachment> fileList = finalFileListOfTask.stream()
							.filter((DataAttachment b) -> treeNode.getId().equals(b.getCatalogId()))
							.collect(Collectors.toList());

//						List<DataAttachment> fileList = getFileList(treeNode.getId(), taskId);
						if (fileList.size() != 0) {
							tree.putExtra("children", fileList);
						}
					}
				});
			return AjaxResult.success(treeNodes);
		} else {
			return AjaxResult.success("没有匹配到该项目的巡查附件");
		}
	}


	/**
	 * 通过任务编号获取树形文件目录结构
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@GetMapping(value = "/getFileTreeByTaskId")
	@Log(title = "巡查附件")
	@ApiOperation("通过任务编号获取树形文件目录结构")
	@ApiImplicitParam(name = "taskId", value = "巡查任务编号", dataTypeClass = String.class, required = true)
	public AjaxResult getFileTree(@RequestParam(value = "taskId", required = true) String taskId) {
		if (null == taskId) return AjaxResult.error("巡查任务编号不能为空");
		DataPatrolTask task = iDataPatrolTaskService.getById(taskId);
		List<Tree<String>> treeNodes = new ArrayList<>();

		List<DataFileCatalog> fileCatalogList = null;
		String DataFileCatalogKey = "DataFileCatalog_pewg_1";
		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
		if (task.getProjectType().equals("1")) {
			// 批而未供
			queryWrapper.eq(DataFileCatalog::getCatalogType, "批而未供");


		} else {
			// step.1 先通过任务编号获取对应闲置土地的消化类型
			String catalogType = idleLandService.getDigestionTypeByTaskId(taskId);
			// 通过项目的消化类型找到对应的固定的父节点
			// TODO
			String parentCatalog = getParentCatalog(catalogType);
			catalogType = StringUtils.isNotBlank(parentCatalog) ? parentCatalog : catalogType;
			// 先按照文件目录类型catalogType查询目录数据
			queryWrapper.eq(DataFileCatalog::getCatalogType, catalogType);
			DataFileCatalogKey = "DataFileCatalog_xz_" + catalogType;
		}
		queryWrapper.orderByAsc(DataFileCatalog::getOrderNum);
		fileCatalogList = redisCache.getCacheObject(DataFileCatalogKey);
		if (fileCatalogList != null && fileCatalogList.size() > 0) {

		} else {
			fileCatalogList = iDataFileCatalogService.list(queryWrapper);
			if (fileCatalogList != null && fileCatalogList.size() > 0) {
				redisCache.setCacheObject(DataFileCatalogKey, fileCatalogList, 90, TimeUnit.MINUTES);
			}
		}


		if (fileCatalogList != null && fileCatalogList.size() != 0) {
			//配置
			TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
			// 自定义属性名 都要默认值的
			treeNodeConfig.setIdKey("id");
			treeNodeConfig.setWeightKey("orderNum");
			// 最大递归深度
			// treeNodeConfig.setDeep(4);

			List<DataAttachment> fileListOfTask = new ArrayList<>();
			LambdaQueryWrapper<DataAttachment> queryWrapperDataAttachment = new LambdaQueryWrapper<>();
			//queryWrapperDataAttachment.eq(DataAttachment::getCatalogId, catalogId);
			queryWrapperDataAttachment.eq(DataAttachment::getPatrolTaskId, taskId);
			fileListOfTask = iDataAttachmentService.list(queryWrapperDataAttachment);
			fileListOfTask.forEach(item -> {
				item.setName(item.getName() + "." + item.getType());
			});

			//转换器
			List<DataAttachment> finalFileListOfTask = fileListOfTask;
			treeNodes = TreeUtil.build(fileCatalogList, "0", treeNodeConfig,
				(treeNode, tree) -> {
					tree.setId(treeNode.getId());
					tree.setParentId(treeNode.getParentId());
					tree.setName(treeNode.getName());
					// 扩展属性 ...
					tree.putExtra("catalogName", treeNode.getName());
					tree.putExtra("catalogType", treeNode.getCatalogType());
					tree.putExtra("orderNum", treeNode.getOrderNum());
					tree.putExtra("remark", treeNode.getRemark());
					if (finalFileListOfTask != null && finalFileListOfTask.size() > 0
						&& StringUtils.isNotBlank(treeNode.getParentId()) && !treeNode.getParentId().equals('0')) {
						// 不是顶级节点
						List<DataAttachment> fileList = finalFileListOfTask.stream()
							.filter((DataAttachment b) -> treeNode.getId().equals(b.getCatalogId()))
							.collect(Collectors.toList());

						//List<DataAttachment> fileList = getFileList(treeNode.getId(), taskId);
						if (fileList.size() != 0) {
							tree.putExtra("children", fileList);
						}
					}
				});
			return AjaxResult.success(treeNodes);
		} else {
			return AjaxResult.success("没有匹配到该项目的巡查附件");
		}
	}

	private String getParentCatalog(String tempCatalog) {
		if (StringUtils.isBlank(tempCatalog)) return "";
		DataDigestType digestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType::getTypeName, tempCatalog), false);
		if (digestType == null) return "";
		DataDigestType parentDigestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType::getId, digestType.getParentId()), false);
		if (parentDigestType == null) return "";
		return parentDigestType.getTypeName();
	}


	private List<DataAttachment> getFileList(String catalogId, String dataId) {
		List<DataAttachment> fileList = new ArrayList<>();
		LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataAttachment::getCatalogId, catalogId);
		queryWrapper.eq(DataAttachment::getPatrolTaskId, dataId);
		fileList = iDataAttachmentService.list(queryWrapper);
		fileList.forEach(item -> {
			item.setName(item.getName() + "." + item.getType());
		});
		return fileList;
	}

	/**
	 * 根据巡查任务编号获取附件
	 */
//	@PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@GetMapping(value = "/getFileList")
	@Log(title = "巡查附件")
	@ApiOperation("根据巡查任务编号获取附件")
	@ApiImplicitParam(name = "taskId", value = "巡查任务编号", dataTypeClass = String.class, required = true)
	public AjaxResult getFileList(@RequestParam(value = "taskId", required = true) String taskId) {
		if (null == taskId) return AjaxResult.error("巡查任务编号不能为空");
		LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataAttachment::getPatrolTaskId, taskId);
		List<DataAttachment> fileList = iDataAttachmentService.list(queryWrapper);
		// 这个设置完整文件名的需要后面考虑一下
		fileList.forEach(item -> {
			item.setName(item.getName() + "." + item.getType());
		});
		if (fileList != null && fileList.size() != 0) {
			return AjaxResult.success(fileList);
		} else {
			return AjaxResult.success("没有匹配到该项目的巡查附件");
		}
	}

}
