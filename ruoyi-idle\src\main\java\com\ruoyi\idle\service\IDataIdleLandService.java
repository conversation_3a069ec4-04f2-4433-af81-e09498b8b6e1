package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.bo.DataIdleLandQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;

import java.util.Collection;
import java.util.List;

/**
 * 闲置土地 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataIdleLandService extends IServicePlus<DataIdleLand> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataIdleLand queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataIdleLand> queryPageList(DataIdleLandQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataIdleLand> queryList(DataIdleLandQueryBo bo);

	List<DataIdleLand> appPulList(  String regionCode,   String userId);
	/**
	 * 根据新增业务对象插入闲置土地
	 *
	 * @param bo 闲置土地 新增业务对象
	 * @return
	 */
	Boolean insert(DataIdleLand bo);

	/**
	 * 根据编辑业务对象修改闲置土地
	 *
	 * @param bo 闲置土地 编辑业务对象
	 * @return
	 */
	Boolean update(DataIdleLand bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 导入用户数据
	 *
	 * @param dataList        用户数据列表
	 * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
	 * @param operaName       操作用户
	 * @return 结果
	 */
	String importIdleLand(List<DataIdleLand> dataList, boolean isUpdateSupport, String operaName);

	String getDigestionTypeByTaskId(String taskId);

	String selectSupervisionNoByTaskId(String taskId,String projectType);

	List<StatisticsVo> idleStat(String regionCode);

	List<DataIdleLand> getAllIdleLandList(String startTime);
}
