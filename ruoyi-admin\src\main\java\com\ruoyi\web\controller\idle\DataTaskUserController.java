package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.lang.generator.UUIDGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.uuid.IdUtils;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.DataTaskUser;
import com.ruoyi.idle.domain.bo.DataTaskUserQueryBo;
import com.ruoyi.idle.domain.vo.IdleAllotTaskUserVo;
import com.ruoyi.idle.domain.vo.PatrolTaskUserVo;
import com.ruoyi.idle.service.IDataPatrolGroupService;
import com.ruoyi.idle.service.IDataPatrolTaskService;
import com.ruoyi.idle.service.IDataTaskUserService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 巡查任务用户关系 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查任务用户关系控制器", tags = {"巡查任务用户关系管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/task/user")
public class DataTaskUserController extends BaseController {

	private final IDataTaskUserService iDataTaskUserService;

	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final IDataPatrolGroupService iDataPatrolGroupService;

	/**
	 * 查询巡查任务用户关系 列表
	 */
	@ApiOperation("查询巡查任务用户关系列表")
	@ApiIgnore
	@GetMapping("/list")
	public TableDataInfo<DataTaskUser> list(@Validated DataTaskUserQueryBo bo) {
		return iDataTaskUserService.queryPageList(bo);
	}

	/**
	 * 根据巡查任务主键编号获取人员列表
	 */
	@ApiOperation("根据任务编号获取人员列表")
	@GetMapping("/patrolUserList")
	public TableDataInfo<DataTaskUser> getPatrolUserList(String taskId) {
		DataTaskUserQueryBo bo = new DataTaskUserQueryBo();
		bo.setTaskId(taskId);
		return iDataTaskUserService.queryPageList(bo);
	}

	/**
	 * 获取巡查任务用户关系 详细信息
	 */
	@ApiOperation("获取巡查任务用户关系详细信息")
	@ApiIgnore
	@GetMapping("/{id}")
	public AjaxResult<DataTaskUser> getInfo(@NotNull(message = "主键不能为空")
											@PathVariable("id") String id) {
		return AjaxResult.success(iDataTaskUserService.queryById(id));
	}

	/**
	 * 新增巡查任务用户关系
	 */
	@ApiOperation("新增巡查任务用户关系")
	@Log(title = "巡查任务用户关系", businessType = BusinessType.INSERT)
	@ApiIgnore
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataTaskUser bo) {
		bo.setCreateBy(SecurityUtils.getUsername());
		bo.setTaskId(IdUtils.fastSimpleUUID());
		return toAjax(iDataTaskUserService.insert(bo) ? 1 : 0);
	}

	/**
	 * 给巡查任务分配巡查人员
	 */
	@ApiOperation("给巡查任务分配巡查人员")
	@Log(title = "巡查任务用户关系", businessType = BusinessType.INSERT)
	@PostMapping("/allotUser")
	@Transactional
	public AjaxResult<Void> allotUser(@Validated @RequestBody PatrolTaskUserVo bo) {
		if (bo == null) return AjaxResult.error("提交的表单内容不能为空");
		LoginUser loginUser = SecurityUtils.getLoginUser();
		// 先根据任务编号修改巡查任务的巡查组编号和名称
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(bo.getTaskId());
		if (null == patrolTask) return AjaxResult.error("没有找到对应的巡查任务");
		patrolTask.setPatrolGroup(bo.getPatrolGroup());
		patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById((bo.getPatrolGroup())));
		if (loginUser != null) {
			//设置创建的用户和创建时间
			patrolTask.setUpdateBy(loginUser.getUsername());
		}
		iDataPatrolTaskService.updateById(patrolTask);
		// 先按照巡查任务编号和业务类型为0查询外业人员配置记录
		LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataTaskUser::getTaskId, bo.getTaskId());
		queryWrapper.eq(DataTaskUser::getBusType, "0");
		// 先删除原先的人员
		iDataTaskUserService.remove(queryWrapper);
		List<IdleAllotTaskUserVo> userList = bo.getUserList();

		if (null != userList && userList.size() != 0) {
			for (IdleAllotTaskUserVo user : userList) {
				DataTaskUser taskUser = new DataTaskUser();
				BeanUtils.copyProperties(user, taskUser);
				taskUser.setTaskId(bo.getTaskId());
				if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("0");
				taskUser.setRemark("外业巡查人员");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					taskUser.setCreateBy(loginUser.getUsername());
				}
				iDataTaskUserService.save(taskUser);
			}
		}
		return AjaxResult.success("分配成功");
	}

	/**
	 * 修改巡查任务用户关系
	 */
	@ApiOperation("修改巡查任务用户关系")
	@Log(title = "巡查任务用户关系", businessType = BusinessType.UPDATE)
	@ApiIgnore
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataTaskUser bo) {
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iDataTaskUserService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除巡查任务用户关系
	 */
	@ApiOperation("删除巡查任务用户关系")
	@Log(title = "巡查任务用户关系", businessType = BusinessType.DELETE)
	@ApiIgnore
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataTaskUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}
}
