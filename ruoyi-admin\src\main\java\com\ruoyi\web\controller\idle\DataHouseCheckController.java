package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.idle.domain.bo.DataHouseCheckQueryBo;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataHouseCheckExportVo;
import com.ruoyi.idle.domain.vo.DataHouseCheckVo;
import com.ruoyi.idle.service.IDataHouseCheckService;
import com.ruoyi.idle.service.ISysRegionService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
        import com.ruoyi.common.core.page.TableDataInfo;
    import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 房屋建筑信息外业调查 Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Api(value = "房屋建筑信息外业调查 控制器", tags = {"房屋建筑信息外业调查 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/check")
public class DataHouseCheckController extends BaseController {

    private final IDataHouseCheckService iDataHouseCheckService;
	private final ISysRegionService iSysRegionService;

/**
 * 查询房屋建筑信息外业调查 列表
 */
@ApiOperation("查询房屋建筑信息外业调查 列表")
@GetMapping("/list")
        public TableDataInfo<DataHouseCheckVo> list(@Validated DataHouseCheckQueryBo bo) {
        return iDataHouseCheckService.queryPageList(bo);
    }

    /**
     * 导出房屋建筑信息外业调查 列表
     */
    @ApiOperation("导出房屋建筑信息外业调查 列表")
    @Log(title = "房屋建筑信息外业调查 ", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<DataHouseCheckExportVo> export(DataPatrolTaskQueryBo bo) {
		List<String> codes = iSysRegionService.queryChildList(bo.getRegionCode());
		if(ObjectUtil.isNotEmpty(codes)){
			bo.setRegionCodes(codes);
		}
        List<DataHouseCheckExportVo> list = iDataHouseCheckService.queryHouseList(bo);
        ExcelUtil<DataHouseCheckExportVo> util = new ExcelUtil<DataHouseCheckExportVo>(DataHouseCheckExportVo.class);
        return util.exportExcel(list, "房屋建筑信息外业调查 ");
    }

    /**
     * 获取房屋建筑信息外业调查 详细信息
     */
    @ApiOperation("获取房屋建筑信息外业调查 详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DataHouseCheckVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable("id") Long id) {
        return AjaxResult.success(iDataHouseCheckService.queryById(id));
    }

    /**
     * 新增房屋建筑信息外业调查
     */
    @ApiOperation("新增房屋建筑信息外业调查 ")
    @Log(title = "房屋建筑信息外业调查 ", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping()
    public AjaxResult<Void> add(@Validated @RequestBody DataHouseCheckQueryBo bo) {
        return toAjax(iDataHouseCheckService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改房屋建筑信息外业调查
     */
    @ApiOperation("修改房屋建筑信息外业调查 ")
    @Log(title = "房屋建筑信息外业调查 ", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping()
    public AjaxResult<Void> edit(@Validated @RequestBody DataHouseCheckQueryBo bo) {
        return toAjax(iDataHouseCheckService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除房屋建筑信息外业调查
     */
    @ApiOperation("删除房屋建筑信息外业调查 ")
    @Log(title = "房屋建筑信息外业调查 ", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
                                   @PathVariable Long[] ids) {
        return toAjax(iDataHouseCheckService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
