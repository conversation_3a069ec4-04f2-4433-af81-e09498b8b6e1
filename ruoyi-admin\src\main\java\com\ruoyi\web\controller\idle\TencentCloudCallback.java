package com.ruoyi.web.controller.idle;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.meet.VideoCallBackDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(value = "腾讯云回调控制器", tags = {"腾讯云回调控制器"})
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("/idle/tccall")
public class TencentCloudCallback {

	@RequestMapping("/do")
	@ApiOperation("回调")
	public AjaxResult videoCallBack(VideoCallBackDataVo vo) {
		System.out.println("====================== ↓↓↓↓↓↓ 腾讯云视频录制回调开始 ↓↓↓↓↓↓ ======================");
		System.out.println("腾讯云视频录制回调控制器接口");
		System.out.println("腾讯云视频录制回调数据");
		System.out.println(String.valueOf(vo));
		System.out.println("====================== ↓↓↓↓↓↓ 腾讯云视频录制回调结束 ↓↓↓↓↓↓ ======================");
		return AjaxResult.success("成功获取录制视频信息");
	}
}
