package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataLandNotProvided;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

/**
 * 批而未供Mapper接口
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
public interface DataLandNotProvidedMapper extends BaseMapperPlus<DataLandNotProvided> {

	String selectSupervisionNoByTaskId(@Param(value = "taskId") String taskId);
}
