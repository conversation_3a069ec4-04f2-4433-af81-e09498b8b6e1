<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult1">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionName" column="region_name"/>
        <result property="orgName" column="org_name"/>
        <result property="userType" column="user_type"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>


    <resultMap type="SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionName" column="region_name"/>
        <result property="orgName" column="org_name"/>
        <result property="userType" column="user_type"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
    </resultMap>

    <resultMap id="RoleResult" type="SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.region_code,
               u.region_name,
               u.org_name,
               u.user_type,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               d.dept_id,
               d.parent_id,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status as dept_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status as role_status
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectPageUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name,u.region_code,u.region_name,u.org_name,u.user_type,
        u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.regionCode != null and user.regionCode != ''">
            AND u.region_code = #{user.regionCode}
        </if>
        <if test="user.regionName != null and user.regionName != ''">
            AND u.region_name = like concat('%', #{user.regionName}, '%')
        </if>
        <if test="user.orgName != null and user.orgName != ''">
            AND u.org_name = like concat('%', #{user.orgName}, '%')
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>
        <if test="user.deptId != null and user.deptId != 0">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
            find_in_set(#{user.deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name,u.region_code,u.region_name,u.org_name,u.user_type,
        u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="regionCode != null and regionCode != ''">
            AND u.region_code = #{regionCode}
        </if>
        <if test="regionName != null and regionName != ''">
            AND u.region_name = like concat('%', #{regionName}, '%')
        </if>
        <if test="orgName != null and orgName != ''">
            AND u.org_name = like concat('%', #{orgName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="params.dataScope != null and params.dataScope != ''">
            AND ( ${params.dataScope} )
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="java.lang.String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0'
    </select>

    <select id="selectUserByUserPhone" parameterType="java.lang.String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.phonenumber = #{userPhone} and u.del_flag = '0'
    </select>

    <select id="selectUserById" parameterType="java.lang.String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId} and u.del_flag = '0'
    </select>

    <select id="selectUserByGroupId" parameterType="GroupUserSearchVo" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        <choose>
            <when test="searchVo.groupId != null">
                where u.user_id in (SELECT user_id FROM data_group_user where group_id = #{searchVo.groupId} group by
                user_id)
                <if test="searchVo.userName != null and searchVo.userName != ''">
                    AND u.user_name like concat('%', #{searchVo.userName}, '%')
                </if>
                <if test="searchVo.nickName != null and searchVo.nickName != ''">
                    AND u.nick_name like concat('%', #{searchVo.nickName}, '%')
                </if>
                <if test="searchVo.regionCode != null and searchVo.regionCode != ''">
                    AND u.region_code = #{searchVo.regionCode}
                </if>
                <if test="searchVo.regionName != null and searchVo.regionName != ''">
                    AND u.region_name like concat('%', #{searchVo.regionName}, '%')
                </if>
                <if test="searchVo.phoneNumber != null and searchVo.phoneNumber != ''">
                    AND u.phonenumber = #{searchVo.phoneNumber}
                </if>
            </when>
            <otherwise>
                where u.user_id in (SELECT user_id FROM data_group_user group by user_id)
                <if test="searchVo.userName != null and searchVo.userName != ''">
                    AND u.user_name like concat('%', #{searchVo.userName}, '%')
                </if>
                <if test="searchVo.nickName != null and searchVo.nickName != ''">
                    AND u.nick_name like concat('%', #{searchVo.nickName}, '%')
                </if>
                <if test="searchVo.regionCode != null and searchVo.regionCode != ''">
                    AND u.region_code = #{searchVo.regionCode}
                </if>
                <if test="searchVo.regionName != null and searchVo.regionName != ''">
                    AND u.region_name like concat('%', #{searchVo.regionName}, '%')
                </if>
                <if test="searchVo.phoneNumber != null and searchVo.phoneNumber != ''">
                    AND u.phonenumber = #{searchVo.phoneNumber}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="selectAllUserList" parameterType="java.lang.String" resultMap="SysUserResult1">
        SELECT * FROM `sys_user` where (create_time >= #{startTime} or update_time >= #{startTime}) and del_flag in ('0','1')
    </select>
</mapper>
