package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.annotation.Excel.Type;
import com.ruoyi.common.annotation.Excels;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_user")
public class SysUser implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@TableId(value = "user_id", type = IdType.ASSIGN_UUID)
	private String userId;

	/**
	 * 部门ID
	 */
	private String deptId;

	/**
	 * 用户账号
	 */
	@NotBlank(message = "用户账号不能为空")
	@Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
	@Excel(name = "登录名称")
	private String userName;

	/**
	 * 用户昵称
	 */
	@Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
	@Excel(name = "用户名称")
	private String nickName;

	/**
	 * 行政区代码
	 */
	@Excel(name = "行政区代码")
	private String regionCode;

	/**
	 * 行政区名称
	 */
	@Excel(name = "行政区名称")
	private String regionName;

	/**
	 * 单位名称
	 */
	@Excel(name = "单位名称")
	private String orgName;

	/**
	 * 用户类型
	 */
	private String userType;

	/**
	 * 用户邮箱
	 */
	@Email(message = "邮箱格式不正确")
	@Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
	@Excel(name = "用户邮箱")
	private String email;

	/**
	 * 手机号码
	 */
	@Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
	@Excel(name = "手机号码")
	private String phonenumber;

	/**
	 * 用户性别
	 */
	@Excel(name = "用户性别",combo={"男","女","未知"}, readConverterExp = "0=男,1=女,2=未知")
	private String sex;

	/**
	 * 用户头像
	 */
	private String avatar;

	/**
	 * 密码
	 */
	private String password;

	@JsonIgnore
	@JsonProperty
	public String getPassword() {
		return password;
	}

	/** 盐加密 */
	// private String salt;

	/**
	 * 帐号状态（0正常 1停用）
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableLogic
	private String delFlag;

	/**
	 * 最后登录IP
	 */
	@Excel(name = "最后登录IP", type = Type.EXPORT)
	private String loginIp;

	/**
	 * 最后登录时间
	 */
	@Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
	private Date loginDate;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 请求参数
	 */
	@TableField(exist = false)
	private Map<String, Object> params = new HashMap<>();

	/**
	 * 部门对象
	 */
	@Excels({
		@Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
		@Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
	})
	@TableField(exist = false)
	private SysDept dept;

	/**
	 * 角色对象
	 */
	@TableField(exist = false)
	private List<SysRole> roles;

	/**
	 * 角色组
	 */
	@TableField(exist = false)
	private String[] roleIds;

	/**
	 * 岗位组
	 */
	@TableField(exist = false)
	private String[] postIds;

	public SysUser(String userId) {
		this.userId = userId;
	}

	public boolean isAdmin() {
		return isAdmin(this.userId);
	}

	public static boolean isAdmin(String userId) {
		return StringUtils.isNotBlank(userId) && "1".equals(userId);
	}

}
