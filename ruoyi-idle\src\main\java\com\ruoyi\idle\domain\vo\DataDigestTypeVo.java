package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;
    import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 消化类型对应关系 视图对象 data_digest_type
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Data
@ApiModel("消化类型对应关系 视图对象")
public class DataDigestTypeVo {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@ApiModelProperty("主键编号")
	private String id;

	/**
	 * 父节点编号
	 */
	@Excel(name = "父节点编号")
	@ApiModelProperty("父节点编号")
	private String parentId;

	/**
	 * 消化类型名称
	 */
	@Excel(name = "消化类型名称")
	@ApiModelProperty("消化类型名称")
	private String typeName;

	/**
	 * 消化类型代码
	 */
	@Excel(name = "消化类型代码")
	@ApiModelProperty("消化类型代码")
	private String typeCode;

	/**
	 * 删除标志
	 */
	@Excel(name = "删除标志")
	@ApiModelProperty("删除标志")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@Excel(name = "状态")
	@ApiModelProperty("状态")
	private String status;


}
