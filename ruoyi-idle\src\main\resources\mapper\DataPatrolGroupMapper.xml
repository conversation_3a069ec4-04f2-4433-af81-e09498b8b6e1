<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataPatrolGroupMapper">

    <resultMap type="com.ruoyi.idle.domain.DataPatrolGroup" id="DataPatrolGroupResult">
        <result property="id" column="id"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="groupName" column="group_name"/>
        <result property="groupAuthor" column="group_author"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectAllPatrolGroupList" parameterType="java.lang.String" resultMap="DataPatrolGroupResult">
        SELECT * FROM `data_patrol_group` where (create_time >= #{startTime} or update_time >= #{startTime}) and delete_flag in ('0','1')
    </select>


</mapper>
