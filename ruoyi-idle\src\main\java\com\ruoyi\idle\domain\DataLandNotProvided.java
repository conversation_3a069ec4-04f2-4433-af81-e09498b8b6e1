package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 批而未供对象 data_land_not_provided
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_land_not_provided")
public class DataLandNotProvided implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 项目所在的州市
	 */
	@ApiModelProperty(value = "项目所在的州市")
	@Excel(name = "项目所在的州市")
	private String city;

	/**
	 * 项目所在区县代码
	 */
	@ApiModelProperty(value = "项目所在区县代码")
	@Excel(name = "项目所在区县代码")
	private String countyCode;

	/**
	 * 项目所在区县名称
	 */
	@ApiModelProperty(value = "项目所在区县名称")
	@Excel(name = "项目所在区县名称")
	private String countyName;

	/**
	 * 电子监管号
	 */
	@ApiModelProperty(value = "电子监管号")
	@Excel(name = "电子监管号")
	private String supervisionNo;

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	@Excel(name = "项目名称")
	private String projectName;

	/**
	 * 数据年度
	 */
	@ApiModelProperty(value = "数据年度")
	private String year;

	/**
	 * 数据季度
	 */
	@ApiModelProperty(value = "数据季度")
	private String quarter;

	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	@Excel(name = "合同编号")
	private String contractNo;

	/**
	 * 供应方式
	 */
	@ApiModelProperty(value = "供应方式")
	@Excel(name = "供应方式")
	private String supplyType;

	/**
	 * 供应面积
	 */
	@ApiModelProperty(value = "供应面积")
	@Excel(name = "供应面积")
	private BigDecimal providedArea;

	/**
	 * 土地坐落
	 */
	@ApiModelProperty(value = "土地坐落")
	@Excel(name = "土地坐落")
	private String address;

	/**
	 * 宗地号
	 */
	@ApiModelProperty(value = "宗地号")
	@Excel(name = "宗地号")
	private String parcelNum;

	/**
	 * 土地用途
	 */
	@ApiModelProperty(value = "土地用途")
	@Excel(name = "土地用途")
	private String landUse;

	/**
	 * 是否已分配核查任务
	 */
	@ApiModelProperty(value = "是否已分配核查任务")
	@Excel(name = "是否已分配核查任务")
	private String isAllot;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Excel(name = "备注")
	private String remark;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 任务是否已认定完成
	 */
	@ApiModelProperty(value = "任务是否已认定完成")
	@TableField(exist = false)
	private String isPatrol;

}
