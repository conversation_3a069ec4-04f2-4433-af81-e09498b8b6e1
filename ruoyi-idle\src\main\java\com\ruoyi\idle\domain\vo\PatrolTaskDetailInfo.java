package com.ruoyi.idle.domain.vo;

import com.ruoyi.idle.domain.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: PatrolTaskDetailInfo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/7/13 11:26
 * @Version: 1.0.0
 **/
@Data
public class PatrolTaskDetailInfo {

	@ApiModelProperty(value = "闲置土地信息")
	DataClgtLand idleLand;

	@ApiModelProperty(value = "房屋外业调查信息")
	DataHouseCheck dataHouseCheck;

	@ApiModelProperty(value = "批而未供信息")
	DataLandNotProvided unprovidedLand;

	@ApiModelProperty(value = "巡查任务信息")
	DataPatrolTask patrolTask;

	@ApiModelProperty(value = "核实人员信息")
	List<DataTaskUser> verifyUserList;

	@ApiModelProperty(value = "巡查人员信息")
	List<DataTaskUser> patrolUserList;

//	@ApiModelProperty(value = "巡查附件信息")
//	DataFileCatalog patrolFileList;

	@ApiModelProperty(value = "巡查附件信息")
	List<DataAttachment> patrolFileList;

	@ApiModelProperty(value = "审核记录信息")
	List<DataAuditRecord> auditRecordList;

	@ApiModelProperty(value = "项目对应的文件目录信息")
	List<DataFileCatalog> fileCatalogList;

	@ApiModelProperty(value = "核查记录信息")
	List<DataVerifyRecord> verifyRecordList;
}
