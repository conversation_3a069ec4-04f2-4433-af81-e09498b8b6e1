package com.ruoyi.web.controller.system;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.vo.GroupUserSearchVo;
import com.ruoyi.idle.service.ISysRegionService;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
@Api(value = "用户管理", tags = {"用户管理"})
public class SysUserController extends BaseController {
	@Autowired
	private ISysUserService userService;

	@Autowired
	private ISysRoleService roleService;

	@Autowired
	private ISysPostService postService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private ISysRegionService iSysRegionService;

	/**
	 * 获取用户列表
	 */
	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysUser user) {
		return userService.selectPageUserList(user);
	}


	@Log(title = "用户管理")
	@ApiOperation("根据行政区分页获取用户")
//	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/getUserListByRegion")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "pageNum", value = "当前页码", dataTypeClass = Integer.class, required = false),
		@ApiImplicitParam(name = "pageSize", value = "每页记录数", dataTypeClass = Integer.class, required = false),
		@ApiImplicitParam(name = "regionCode", value = "行政区代码", dataTypeClass = String.class, required = false),
		@ApiImplicitParam(name = "userType", value = "用户类型", dataTypeClass = String.class, required = true),
	})
	public AjaxResult getUserListByRegion(@RequestParam(name = "pageNum", required = false, defaultValue = "1")
											  Integer pageNum,
										  @RequestParam(name = "pageSize", required = false, defaultValue = "10")
											  Integer pageSize,
										  String regionCode, String userType) {
		AjaxResult<IPage<SysUser>> returnPage = new AjaxResult<IPage<SysUser>>();
		Page<SysUser> page = new Page<SysUser>(pageNum, pageSize);
		LambdaQueryWrapper<SysUser> taskQueryWrapper = new LambdaQueryWrapper<SysUser>();
		if (StringUtils.isNotBlank(regionCode)) taskQueryWrapper.eq(SysUser::getRegionCode, regionCode);
		taskQueryWrapper.eq(SysUser::getUserType, userType);
		taskQueryWrapper.eq(SysUser::getStatus, "0");
		List<String> nameList = new ArrayList<>();
		nameList.add("admin");
		taskQueryWrapper.notIn(SysUser::getUserName, nameList);
		//分页数据
		IPage<SysUser> pageData = userService.page(page, taskQueryWrapper);
		returnPage.setData(pageData);
		return returnPage;
	}

	@Log(title = "用户管理")
	@ApiOperation("根据行政区获取用户列表")
//	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/getUserList")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "regionCode", value = "行政区代码", dataTypeClass = String.class, required = false),
		@ApiImplicitParam(name = "userType", value = "用户类型", dataTypeClass = String.class, required = true),
	})
	public AjaxResult getUserList(String regionCode, String userType) {
		LambdaQueryWrapper<SysUser> taskQueryWrapper = new LambdaQueryWrapper<SysUser>();
//		if (StringUtils.isNotBlank(regionCode)) taskQueryWrapper.eq(SysUser::getRegionCode, regionCode);
		//获取外业人员
		taskQueryWrapper.eq(SysUser::getUserType, "0");
		taskQueryWrapper.eq(SysUser::getStatus, "0");
		List<String> nameList = new ArrayList<>();
		nameList.add("admin");
		taskQueryWrapper.notIn(SysUser::getUserName, nameList);
		//分页数据
		List<SysUser> userList = userService.list(taskQueryWrapper);
		return AjaxResult.success("成功获取用户列表数据", userList);
	}

	@Log(title = "用户管理")
//	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/groupUserList")
	@ApiOperation("根据用户组编号获取用户列表")
	public AjaxResult getGroupUserList(GroupUserSearchVo searchVo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				searchVo.setRegionCode(sysUser.getRegionCode());
			}
		}
		return AjaxResult.success(userService.selectUserByGroupId(searchVo));
	}

	/**
	 * 获取需要审核的用户列表
	 */
	@Log(title = "用户管理")
//	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/verifyUserList")
	@ApiOperation("获取需要审核的用户列表")
	public TableDataInfo getVerifyUserList() {
		SysUser user = new SysUser();
		user.setStatus("2");
		user.setDelFlag("0");
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				user.setRegionCode(sysUser.getRegionCode());
			}
		}
		return userService.selectPageUserList(user);
	}


	@Log(title = "用户管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:user:export')")
	@GetMapping("/export")
	public AjaxResult export(SysUser user) {
		List<SysUser> list = userService.selectUserList(user);
		ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
		return util.exportExcel(list, "用户数据");
	}

	@Log(title = "用户管理", businessType = BusinessType.IMPORT)
//    @PreAuthorize("@ss.hasPermi('system:user:import')")
	@PostMapping("/importData")
	public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
		ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
		List<SysUser> userList = util.importExcel(file.getInputStream());
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		String operName = loginUser.getUsername();
		String message = userService.importUser(userList, updateSupport, operName);
		return AjaxResult.success(message);
	}

	@GetMapping("/importTemplate")
	public AjaxResult importTemplate() {
		ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
		return util.importTemplateExcel("用户数据");
	}

	/**
	 * 根据用户编号获取详细信息
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:query')")
	@GetMapping(value = {"/", "/{userId}"})
	public AjaxResult getInfo(@PathVariable(value = "userId", required = false) String userId) {
		Map<String, Object> ajax = new HashMap<>();
		List<SysRole> roles = roleService.selectRoleAll();
		ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
		ajax.put("posts", postService.selectPostAll());
		if (Validator.isNotNull(userId)) {
			ajax.put("user", userService.selectUserById(userId));
			ajax.put("postIds", postService.selectPostListByUserId(userId));
			ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
		}
		return AjaxResult.success(ajax);
	}

	/**
	 * 新增用户
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:add')")
	@Log(title = "用户管理", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@Validated @RequestBody SysUser user) {
		if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
			return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
		} else if (Validator.isNotEmpty(user.getPhonenumber())
			&& UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
			return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
		} else if (Validator.isNotEmpty(user.getEmail())
			&& UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
			return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
		}
		if (user.getRegionCode() != null && user.getRegionCode().equals("5301")) {
			user.setUserType("0");
		} else {
			user.setUserType("1");
		}
		if (user.getRegionCode() != null && user.getRegionName() == null) {
			// 根据行政区代码获取行政区名称
			user.setRegionName(iSysRegionService.getNameByCode(user.getRegionCode()));
		}
		user.setDeptId("105");
		user.setCreateBy(SecurityUtils.getUsername());
		user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
		return toAjax(userService.insertUser(user));
	}

	/**
	 * 修改用户
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:edit')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@Validated @RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		if (Validator.isNotEmpty(user.getPhonenumber())
			&& UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
			return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
		} else if (Validator.isNotEmpty(user.getEmail())
			&& UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
			return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
		}
		user.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(userService.updateUser(user));
	}

	/**
	 * 删除用户
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:remove')")
	@Log(title = "用户管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
	public AjaxResult remove(@PathVariable String[] userIds) {
		return userService.deleteUserByIds(userIds);
	}

	/**
	 * 重置密码
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PutMapping("/resetPwd")
	public AjaxResult resetPwd(@RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
		user.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(userService.resetPwd(user));
	}

	/**
	 * 状态修改
	 */
//    @PreAuthorize("@ss.hasPermi('system:user:edit')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PutMapping("/changeStatus")
	public AjaxResult changeStatus(@RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		user.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(userService.updateUserStatus(user));
	}
}
