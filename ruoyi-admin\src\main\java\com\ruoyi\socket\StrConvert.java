package com.ruoyi.socket;

import javax.swing.tree.ExpandVetoException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class StrConvert {

	public static String toStr(String value11) {
		if (value11 == null)
			return "";
		else
			return value11;
	}

	public static String toStrTrim(String value11) {
		if (value11 == null)
			return "";
		else
			return value11.trim();
	}

	public static long toLong(String value11) {
		if (value11 == null)
			return 0L;
		else
			return Long.parseLong(value11);
	}

	public static Double toDouble(String value11) {
		if (value11 == null)
			return 0d;
		else
			try {
				return Double.parseDouble(value11);
			} catch (Exception e) {
				return 0d;
			}
	}
	public static Float toFloat(String value11) {
		if (value11 == null)
			return 0f;
		else
			try {
				return Float.parseFloat(value11);
			} catch (Exception e) {
				return 0f;
			}
	}
	public static SimpleDateFormat formatter112 = new SimpleDateFormat("yyyyMMddHHmmss");

	public static long dateToLong(Date dateValue) {
		if (dateValue == null)
			return 0L;
		else {
			String strDate = formatter112.format(dateValue);

			return toLong(strDate);
		}

	}

	public static int toInt(String value11) {
		if (value11 == null)
			return 0;
		else
			return Integer.parseInt(value11);
	}

	public static String getTimeStr(Date date, String strFormat) {
		SimpleDateFormat formatter = new SimpleDateFormat(strFormat);
		String strDate = formatter.format(date);
		return strDate;
	}

	public static String getTimeStr(String strFormat) {
		SimpleDateFormat formatter = new SimpleDateFormat(strFormat);
		String strDate = formatter.format(new Date());
		return strDate;
	}

	public static String getTimeStrDefault(Date date) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String strDate = formatter.format(date);
		return strDate;
	}

	public static String getTimeStampStr() {
		return getTimeStr("yyyy-MM-dd HH:mm:ss");
	}

	public static Date toDate(String value, String format) {
		if ("".equals(value) || value == null) {
			return null;
		}
		SimpleDateFormat d2 = new SimpleDateFormat(format);
		try {
			return d2.parse(value);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date toDateDefault(String value) {
		if ("".equals(value) || value == null) {
			return null;
		}
		String format = "yyyy-MM-dd HH:mm:ss";
		SimpleDateFormat d2 = new SimpleDateFormat(format);
		try {
			return d2.parse(value);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

}
