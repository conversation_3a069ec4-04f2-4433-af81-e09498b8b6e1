package com.ruoyi.idle.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.bo.MyTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 巡查任务 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
//@CacheNamespace(implementation = MybatisPlusRedisCache.class, eviction = MybatisPlusRedisCache.class)
public interface DataPatrolTaskMapper extends BaseMapperPlus<DataPatrolTask> {

	List<DataPatrolTask> selectAllPatrolTaskList(@Param("startTime") String startTime);

	//外业核查,内业审查任务
	IPage<DataPatrolTask> selectTaskList(IPage<DataPatrolTask> page, @Param("bo") DataPatrolTaskQueryBo bo);

	//待办列表
	IPage<DataPatrolTask> selectToDoList(IPage<DataPatrolTask> page, @Param("bo") MyTaskQueryBo bo, @Param("userId") String userId,@Param("type") String type);

	//批而未供审查核查
	IPage<DataPatrolTask> selectNotProTaskList(IPage<DataPatrolTask> page,@Param("bo") DataPatrolTaskQueryBo bo);

	//村级任务总数，未提交，已提交，未巡查任务数
	List<Map<String,Object>> countByPatrol(@Param("pId") String pId);
//	@Select("SELECT t.* from data_patrol_task t \n" +
//		"INNER JOIN data_task_user u on u.task_id=t.id \n" +
//		"where u.user_id=#{userId} \n" +
//		"and t.publish_status = '1' and t.is_patrol = '0' and t.region_code=#{regionCode}")
	@Select("SELECT t.* from data_patrol_task t \n" +
		"where t.publish_status = '1' and t.is_patrol = '0' and t.region_code=#{regionCode}")
	List<DataPatrolTask> appPulList(@Param("regionCode") String regionCode,@Param("userId") String userId);

	List<DataPatrolTaskVo> appCheckList(@Param("regionCode") String regionCode);

	IPage<DataPatrolTaskVo> submitTaskList(IPage<DataPatrolTask> page, @Param("bo") DataPatrolTaskQueryBo bo);

	List<Map<String,Object>> submitTaskListImages( @Param("bo") DataPatrolTaskQueryBo bo);

}
