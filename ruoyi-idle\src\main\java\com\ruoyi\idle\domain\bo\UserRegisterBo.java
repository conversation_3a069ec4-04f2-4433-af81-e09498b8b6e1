package com.ruoyi.idle.domain.bo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @Created with IntelliJ IDEA.
 * @Title: UserRegisterBo
 * @Description: com.ruoyi.idle.domain.bo
 * @Author: HongDeng
 * @Date: 2021/7/12 9:25
 * @Version: 1.0.0
 **/
@Data
public class UserRegisterBo {
	/**
	 * 用户账号
	 */
	private String userName;

	/**
	 * 行政区代码
	 */
	private String regionCode;

	/**
	 * 行政区名称
	 */
	private String regionName;

	/**
	 * 用户单位
	 */
	private String orgName;

	/**
	 * 手机号码
	 */
	@Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
	private String phonenumber;

	/**
	 * 密码
	 */
	private String password;
}
