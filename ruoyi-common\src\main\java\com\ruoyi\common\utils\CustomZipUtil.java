package com.ruoyi.common.utils;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * @Created with IntelliJ IDEA.
 * @Title: ZipUtil
 * @Description: com.ruoyi.common.utils
 * @Author: HongDeng
 * @Date: 2021/12/3 15:52
 * @Version: 1.0.0
 **/
public class CustomZipUtil {
	private ZipInputStream zipIn;       //解压Zip
	private ZipOutputStream zipOut;      //压缩Zip
	private ZipEntry zipEntry;
	private static int bufSize;     //size of bytes
	private byte[] buf;
	private int readedBytes;

	public CustomZipUtil() {
		this(512);
	}

	public CustomZipUtil(int bufSize) {
		this.bufSize = bufSize;
		this.buf = new byte[this.bufSize];
	}

	//压缩文件夹内的文件
	public void doZip(String zipDirectory) { //zipDirectoryPath:需要压缩的文件夹名
		File file;
		File zipDir;
		zipDir = new File(zipDirectory);
		String zipFileName = zipDir.getName() +  ".zip"; //压缩后生成的zip文件名
		try {
			this.zipOut = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(zipFileName)));
			handleDir(zipDir, this.zipOut);
			this.zipOut.close();
		} catch (IOException ioe) {
			ioe.printStackTrace();
		}
	}

	//由doZip调用,递归完成目录文件读取
	private void handleDir(File dir, ZipOutputStream zipOut) throws IOException {
		FileInputStream fileIn;
		File[] files;
		files = dir.listFiles();
		if (files.length == 0) { //如果目录为空,则单独创建之.
			//ZipEntry的isDirectory()方法中,目录以"/"结尾.
			this.zipOut.putNextEntry(new ZipEntry(dir.toString() + "/"));
			this.zipOut.closeEntry();
		} else { //如果目录不为空,则分别处理目录和文件.
			for (File fileName : files) {
				if (fileName.isDirectory()) {
					handleDir(fileName, this.zipOut);
				} else {
					fileIn = new FileInputStream(fileName);
					this.zipOut.putNextEntry(new ZipEntry(fileName.toString()));

					while ((this.readedBytes = fileIn.read(this.buf)) > 0) {
						this.zipOut.write(this.buf, 0, this.readedBytes);
					}

					this.zipOut.closeEntry();
				}
			}
		}
	}

	//解压指定zip文件
	public void unZip(String unZipfileName) { //unZipfileName需要解压的zip文件名
		FileOutputStream fileOut;
		File file;

		try {
			this.zipIn = new ZipInputStream(new BufferedInputStream(new FileInputStream(unZipfileName)));

			while ((this.zipEntry = this.zipIn.getNextEntry()) != null) {
				file = new File(this.zipEntry.getName());
				//System.out.println(file);///

				if (this.zipEntry.isDirectory()) {
					file.mkdirs();
				} else {
					//如果指定文件的目录不存在,则创建之.
					File parent = file.getParentFile();
					if (!parent.exists()) {
						parent.mkdirs();
					}

					fileOut = new FileOutputStream(file);
					while ((this.readedBytes = this.zipIn.read(this.buf)) > 0) {
						fileOut.write(this.buf, 0, this.readedBytes);
					}
					fileOut.close();
				}
				this.zipIn.closeEntry();
			}
		} catch (IOException ioe) {
			ioe.printStackTrace();
		}
	}

	//设置缓冲区大小
	public void setBufSize(int bufSize) {
		this.bufSize = bufSize;
	}
}
