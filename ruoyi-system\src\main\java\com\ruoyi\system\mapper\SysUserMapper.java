package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.idle.domain.vo.GroupUserSearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper extends BaseMapperPlus<SysUser> {

	Page<SysUser> selectPageUserList(@Param("page") Page<SysUser> page, @Param("user") SysUser user);

	/**
	 * 根据条件分页查询用户列表
	 *
	 * @param sysUser 用户信息
	 * @return 用户信息集合信息
	 */
	public List<SysUser> selectUserList(SysUser sysUser);

	/**
	 * 通过用户名查询用户
	 *
	 * @param userName 用户名
	 * @return 用户对象信息
	 */
	public SysUser selectUserByUserName(String userName);

	/**
	 * 通过用户手机号查询用户
	 *
	 * @param userPhone 用户手机号
	 * @return 用户对象信息
	 */
	public SysUser selectUserByUserPhone(String userPhone);

	/**
	 * 通过用户ID查询用户
	 *
	 * @param userId 用户ID
	 * @return 用户对象信息
	 */
	public SysUser selectUserById(String userId);

	/**
	 * 通过巡查组ID查询用户
	 *
	 * @param searchVo 巡查组查询对象
	 * @return 用户对象信息
	 */
	public List<SysUser> selectUserByGroupId(@Param("searchVo") GroupUserSearchVo searchVo);

	List<SysUser> selectAllUserList(@Param("startTime") String  startTime);

}
