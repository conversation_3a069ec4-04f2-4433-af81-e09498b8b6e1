<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataIdleVerifyMapper">

    <resultMap type="com.ruoyi.idle.domain.DataIdleVerify" id="DataIdleVerifyResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="idleId" column="idle_id"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="projectName" column="project_name"/>
        <result property="year" column="year"/>
        <result property="quarter" column="quarter"/>
        <result property="address" column="address"/>
        <result property="supplyType" column="supply_type"/>
        <result property="supervisionNo" column="supervision_no"/>
        <result property="contractNo" column="contract_no"/>
        <result property="parcelNum" column="parcel_num"/>
        <result property="landArea" column="land_area"/>
        <result property="landUse" column="land_use"/>
        <result property="warrantNum" column="warrant_num"/>
        <result property="handleType" column="handle_type"/>
        <result property="hasReturnPact" column="has_return_pact"/>
        <result property="hasReplacePact" column="has_replace_pact"/>
        <result property="hasRevokeData" column="has_revoke_data"/>
        <result property="hasReplaceData" column="has_replace_data"/>
        <result property="hasPayProof" column="has_pay_proof"/>
        <result property="hasNewSupplyData" column="has_new_supply_data"/>
        <result property="hasStartData" column="has_start_data"/>
        <result property="hasStartPhoto" column="has_start_photo"/>
        <result property="videoIsReal" column="video_is_real"/>
        <result property="siteIsReal" column="site_is_real"/>
        <result property="handleIsCorrect" column="handle_is_correct"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
