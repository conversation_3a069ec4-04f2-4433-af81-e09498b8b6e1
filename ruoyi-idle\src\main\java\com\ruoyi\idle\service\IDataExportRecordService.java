package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
        import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataExportRecord;
import com.ruoyi.idle.domain.bo.DataExportRecordQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 成果导出记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
public interface IDataExportRecordService extends IServicePlus<DataExportRecord> {
    /**
     * 查询单个
     * @return
     */
	DataExportRecord queryById(Long id);

            /**
         * 查询列表
         */
        TableDataInfo<DataExportRecord> queryPageList(DataExportRecordQueryBo bo);

    /**
     * 查询列表
     */
    List<DataExportRecord> queryList(DataExportRecordQueryBo bo);

    /**
     * 根据新增业务对象插入成果导出记录
     * @param bo 成果导出记录 新增业务对象
     * @return
     */
    Boolean insertByAddBo(DataExportRecord bo);

    /**
     * 根据编辑业务对象修改成果导出记录
     * @param bo 成果导出记录 编辑业务对象
     * @return
     */
    Boolean updateByEditBo(DataExportRecord bo);

    /**
     * 校验并删除数据
     * @param ids 主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
