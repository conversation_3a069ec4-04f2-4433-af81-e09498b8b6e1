package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单表 数据层
 *
 * <AUTHOR>
 */
public interface SysMenuMapper extends BaseMapperPlus<SysMenu> {

	/**
	 * 根据用户所有权限
	 *
	 * @return 权限列表
	 */
	public List<String> selectMenuPerms();

	/**
	 * 根据用户查询系统菜单列表
	 *
	 * @param menu 菜单信息
	 * @return 菜单列表
	 */
	public List<SysMenu> selectMenuListByUserId(SysMenu menu);

	/**
	 * 根据用户ID查询权限
	 *
	 * @param userId 用户ID
	 * @return 权限列表
	 */
	public List<String> selectMenuPermsByUserId(String userId);

	/**
	 * 根据用户ID查询菜单
	 *
	 * @return 菜单列表
	 */
	public List<SysMenu> selectMenuTreeAll();

	/**
	 * 根据用户ID查询菜单
	 *
	 * @param userId 用户ID
	 * @return 菜单列表
	 */
	public List<SysMenu> selectMenuTreeByUserId(String userId);

	/**
	 * 根据角色ID查询菜单树信息
	 *
	 * @param roleId            角色ID
	 * @param menuCheckStrictly 菜单树选择项是否关联显示
	 * @return 选中菜单列表
	 */
	public List<Integer> selectMenuListByRoleId(@Param("roleId") String roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

}
