package com.ruoyi.web.controller.idle;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.bo.DataIdleLandQueryBo;
import com.ruoyi.idle.domain.vo.DataClgtLandVo;
import com.ruoyi.idle.domain.vo.IdleAllotTaskUserVo;
import com.ruoyi.idle.domain.vo.IdleAllotTaskVo;
import com.ruoyi.idle.domain.vo.StatisticsVo;
import com.ruoyi.idle.service.*;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.message.ReusableMessage;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 闲置土地 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "闲置土地控制器", tags = {"闲置土地管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/land")
@Slf4j
public class DataIdleLandController extends BaseController {
	private final IDataClgtLandService iDataClgtLandService;

	private final IDataHouseCheckService iDataHouseCheckService;

	private final IDataIdleLandService iDataIdleLandService;

	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final IDataPatrolGroupService iDataPatrolGroupService;

	private final IDataTaskUserService iDataTaskUserService;

	private final IDataAttachmentService iDataAttachmentService;

	private final IDataAuditRecordService auditRecordService;

	private final IDataVerifyRecordService verifyRecordService;

	private final TokenService tokenService;

	private final ISysUserService userService;

	@Autowired
	private RedisCache redisCache;


	/**
	 * 查询闲置土地 列表
	 */
	@ApiOperation("查询闲置土地列表")
//    @PreAuthorize("@ss.hasPermi('idle:land:list')")
	@GetMapping("/list")
	public TableDataInfo<DataIdleLand> list(@Validated DataIdleLandQueryBo bo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				bo.setCountyCode(sysUser.getRegionCode());
			}
		}
		return iDataIdleLandService.queryPageList(bo);
	}

	/**
	 * 导出闲置土地 列表
	 */
	@ApiOperation("导出闲置土地列表")
//    @PreAuthorize("@ss.hasPermi('idle:land:export')")
	@Log(title = "闲置土地", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataIdleLand> export(@Validated DataIdleLandQueryBo bo) {
		List<DataIdleLand> list = iDataIdleLandService.queryList(bo);
		ExcelUtil<DataIdleLand> util = new ExcelUtil<DataIdleLand>(DataIdleLand.class);
		return util.exportExcel(list, "闲置土地");
	}

	/**
	 * 获取闲置土地 详细信息
	 */
	@ApiOperation("获取闲置土地详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:land:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataIdleLand> getInfo(@NotNull(message = "主键不能为空")
											@PathVariable("id") String id) {
		return AjaxResult.success(iDataIdleLandService.queryById(id));
	}

	/**
	 * 一键分配闲置巡查任务
	 */
	@ApiOperation("一键分配闲置巡查任务")
	@PostMapping("/allotAllTask")
	@ApiIgnore
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult allotAllTask(){
		// step.1 查询出所有的未分配闲置土地记录
		LambdaQueryWrapper<DataClgtLand> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataClgtLand :: getIsAllot,"0");
		List<DataClgtLand> idleLandList = iDataClgtLandService.list(queryWrapper);
		LoginUser loginUser = SecurityUtils.getLoginUser();
		List<IdleAllotTaskUserVo> userList = new ArrayList<>();
//		IdleAllotTaskUserVo user1 = new IdleAllotTaskUserVo();
//		user1.setUserId("102");
//		user1.setUserName("杨社锋");
//		user1.setUserPhone("18088138716");
//		user1.setIsLeader("0");
//		user1.setBusType("1");
//		IdleAllotTaskUserVo user2 = new IdleAllotTaskUserVo();
//		user2.setUserId("8dfaae0e444db0b861da749f962615ef");
//		user2.setUserName("敖朝刚");
//		user2.setUserPhone("15012382334");
//		user2.setIsLeader("0");
//		user2.setBusType("1");
//		IdleAllotTaskUserVo user3 = new IdleAllotTaskUserVo();
//		user3.setUserId("b134ae9aafd6b8f22e78a1fc19d2b9de");
//		user3.setUserName("姜宝鑫");
//		user3.setUserPhone("18082759810");
//		user3.setIsLeader("1");
//		user3.setBusType("1");
//		IdleAllotTaskUserVo user4 = new IdleAllotTaskUserVo();
//		user4.setUserId("ee46da99f56232b42a9ea8fe22cff626");
//		user4.setUserName("陈柯洁");
//		user4.setUserPhone("13708795276");
//		user4.setIsLeader("0");
//		user4.setBusType("1");
//		IdleAllotTaskUserVo user5 = new IdleAllotTaskUserVo();
//		user5.setUserId("fd703f751fcbe442a2b9bb5a06f3edac");
//		user5.setUserName("和晓庆");
//		user5.setUserPhone("18760898958");
//		user5.setIsLeader("0");
//		user5.setBusType("1");
//		userList.add(user1);
//		userList.add(user2);
//		userList.add(user3);
//		userList.add(user4);
//		userList.add(user5);

		//查询内业人员
		LambdaQueryWrapper<SysUser> taskQueryWrapper = new LambdaQueryWrapper<SysUser>();
		taskQueryWrapper.eq(SysUser::getRegionCode, "5301");
		taskQueryWrapper.eq(SysUser::getUserType, "1");
		taskQueryWrapper.eq(SysUser::getStatus, "0");
		List<String> nameList = new ArrayList<>();
		nameList.add("admin");
		taskQueryWrapper.notIn(SysUser::getUserName, nameList);
		List<SysUser> userList1 = userService.list(taskQueryWrapper);
		for (SysUser user:userList1) {
			IdleAllotTaskUserVo idleAllotTaskUserVo = new IdleAllotTaskUserVo();
			idleAllotTaskUserVo.setUserId(user.getUserId());
			idleAllotTaskUserVo.setUserName(user.getUserName());
			idleAllotTaskUserVo.setUserPhone(user.getPhonenumber());
			idleAllotTaskUserVo.setIsLeader("0");
			idleAllotTaskUserVo.setBusType("1");
			userList.add(idleAllotTaskUserVo);
		}
		if (idleLandList.size() == 0) return AjaxResult.success("您的所有外业调查项目均已分配，无需再次分配",null);
		//执行异步任务
		CompletableFuture<Void> future = CompletableFuture.runAsync(()->{
//			DataClgtLand land = idleLandList.get(0);
//			HashMap<String, Object> map = new HashMap<>();
//			map.put("任务分配","任务分配中");
//			redisCache.setCacheObject(land.getTaskNo(), map, 5, TimeUnit.MINUTES);
			for (DataClgtLand idleLand : idleLandList){
				// 修改闲置土地的分配字段
				idleLand.setIsAllot("1");
				iDataClgtLandService.updateById(idleLand);
				// 在巡查任务中新增记录
				DataPatrolTask patrolTask = new DataPatrolTask();
//			BeanUtils.copyProperties(taskVo, patrolTask);
				patrolTask.setIdleLandId(idleLand.getId());
				patrolTask.setProjectName(idleLand.getProjectName());
				patrolTask.setRegionCode(idleLand.getCountyCode());
				patrolTask.setRegionName(idleLand.getCountyName());
				patrolTask.setQuarter(idleLand.getQuarter());
				if(idleLand.getYear()!=null){
					patrolTask.setYear(idleLand.getYear().trim());
				}
				String now = DateUtil.now();
				Date date = DateUtil.parse(now);
				Date newDate = DateUtil.offsetDay(date, 10);
				patrolTask.setMeetingTime(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
				patrolTask.setMeetingEndTime(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss"));
				// 根据巡查组编号获取巡查组名称(不能直接分配到巡查组)
				// patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById(Long.valueOf(taskVo.getPatrolGroup())));
				patrolTask.setPublishStatus("0");
				patrolTask.setProjectType("0");
//			patrolTask.setPatrolType(getPatrolType(idleLand.getInsideRemark()));
				patrolTask.setPatrolType("0");
				patrolTask.setIsPatrol("0");
				patrolTask.setDeleteFlag("0");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					patrolTask.setCreateBy(loginUser.getUsername());
				}

				iDataPatrolTaskService.save(patrolTask);


				// 保存配置的巡查组成员（一般是后台的核实人员）

				for (IdleAllotTaskUserVo user : userList) {
					DataTaskUser taskUser = new DataTaskUser();
					BeanUtils.copyProperties(user, taskUser);
					taskUser.setTaskId(patrolTask.getId());
					if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("1");
					taskUser.setRemark("内业核实人员");
					if (loginUser != null) {
						//设置创建的用户和创建时间
						taskUser.setCreateBy(loginUser.getUsername());
					}
					iDataTaskUserService.save(taskUser);
				}
			}
		});
		return AjaxResult.success("成功分配闲置巡查任务");
	}

	/**
	 * 分配闲置巡查任务
	 */
	@ApiOperation("分配闲置巡查任务")
//	@PreAuthorize("@ss.hasPermi('idle:land:list')")
	@PostMapping("/allotTask")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult allotTask(@RequestBody IdleAllotTaskVo taskVo) throws ParseException {
		if (null == taskVo) return AjaxResult.error("提交的表单内容不能为空");
		DataClgtLand idleLand = iDataClgtLandService.queryById(taskVo.getIdleLandId());
		if (null == idleLand) return AjaxResult.success("没有找到对应的闲置土地数据");
		if (idleLand.getIsAllot().equals("1")) return AjaxResult.success("您选择的闲置土地记录已经分配巡查任务，不能多次分配");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginUser loginUser = SecurityUtils.getLoginUser();
		// 修改闲置土地的分配字段
		idleLand.setIsAllot("1");
		iDataClgtLandService.updateById(idleLand);
		// 在巡查任务中新增记录
		DataPatrolTask patrolTask = new DataPatrolTask();
		BeanUtils.copyProperties(taskVo, patrolTask);

		patrolTask.setProjectName(idleLand.getProjectName());
		patrolTask.setRegionCode(idleLand.getCountyCode());
		patrolTask.setRegionName(idleLand.getCountyName());
		patrolTask.setQuarter(idleLand.getQuarter());



		if (patrolTask.getMeetingTime() != null) {
			Date meetingTime = DateUtil.parse(taskVo.getMeetingTime());
			patrolTask.setMeetingTime(DateUtil.format(meetingTime, "yyyy-MM-dd HH:mm:ss"));
			Date endTime = DateUtil.parse(taskVo.getMeetingEndTime());
			patrolTask.setMeetingEndTime(DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));
		}else{
			String now = DateUtil.now();
			Date date = DateUtil.parse(now);
			Date newDate = DateUtil.offsetDay(date, 10);
			patrolTask.setMeetingTime(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
			patrolTask.setMeetingEndTime(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss"));
		}
		// 根据巡查组编号获取巡查组名称(不能直接分配到巡查组)
		// patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById(Long.valueOf(taskVo.getPatrolGroup())));
		patrolTask.setPublishStatus("0");
		patrolTask.setProjectType("0");
//		patrolTask.setPatrolType(getPatrolType(idleLand.getInsideRemark()));
		patrolTask.setPatrolType("0");
		patrolTask.setIsPatrol("0");
		patrolTask.setDeleteFlag("0");
		if (loginUser != null) {
			//设置创建的用户和创建时间
			patrolTask.setCreateBy(loginUser.getUsername());
		}
		if(idleLand.getYear()!=null){
			patrolTask.setYear(idleLand.getYear().trim());
		}
		iDataPatrolTaskService.save(patrolTask);

		// 保存配置的巡查组成员（一般是后台的核实人员）
		List<IdleAllotTaskUserVo> userList = taskVo.getUserList();
		if (null != userList && userList.size() != 0) {
			for (IdleAllotTaskUserVo user : userList) {
				DataTaskUser taskUser = new DataTaskUser();
				BeanUtils.copyProperties(user, taskUser);
				taskUser.setTaskId(patrolTask.getId());
				if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("1");
				taskUser.setRemark("内业核实人员");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					taskUser.setCreateBy(loginUser.getUsername());
				}
				iDataTaskUserService.save(taskUser);
			}
		}

		return AjaxResult.success("成功分配闲置巡查任务");
	}

	private String getPatrolType(String insideRemark) {
		String patrolType = "1";
		switch (insideRemark) {
			case "自行举证":
				patrolType = "0";
				break;
			case "连线核查":
				patrolType = "1";
				break;
			case "内业核查":
				patrolType = "2";
				break;
			default:
				break;
		}
		return patrolType;
	}

	/**
	 * 撤销已分配的闲置巡查任务
	 */
	@ApiOperation("撤销已分配的闲置巡查任务")
//	@PreAuthorize("@ss.hasPermi('idle:land:list')")
	@PostMapping("/revokeAllotTask/{id}")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult revokeAllotTask(@PathVariable(value = "id") String id) {
		// step.1 根据闲置土地记录编号去巡查任务表中查找符合条件的记录
		List<DataPatrolTask> taskList = iDataPatrolTaskService.list(new LambdaQueryWrapper<DataPatrolTask>()
			.eq(DataPatrolTask::getIdleLandId, id));
		// step.2 循环查询出来的巡查任务记录
		if (taskList.size() != 0) {
			for (DataPatrolTask task : taskList) {
				// step.2.1 删除和任务相关的巡查人员记录
				iDataTaskUserService.remove(new LambdaQueryWrapper<DataTaskUser>().eq(DataTaskUser::getTaskId, task.getId()));
				// 防止逻辑删除的时候不更新时间字段
				task.setRemark("任务撤销");
				iDataPatrolTaskService.updateById(task);
				iDataPatrolTaskService.removeById(task);

				// step.2.2 删除和任务相关的审核记录（假删除）
				auditRecordService.remove(new LambdaQueryWrapper<DataAuditRecord>().eq(DataAuditRecord::getTaskId, task.getId()));
				// step.2.3 删除和任务相关的核查结果记录（假删除）

				// step.2.4 删除和任务相关的巡查附件（假删除）
				iDataAttachmentService.remove(new LambdaQueryWrapper<DataAttachment>().eq(DataAttachment::getPatrolTaskId, task.getId()));
				// step.2.5 删除和任务相关的核查记录（假删除）
				verifyRecordService.remove(new LambdaQueryWrapper<DataVerifyRecord>()
				.eq(DataVerifyRecord :: getTaskId,task.getId()));
			}
		}
		// step.3 修改闲置土地的分配状态字段
		DataClgtLand idleLand = iDataClgtLandService.getById(id);
		if (idleLand != null) {
			idleLand.setIsAllot("0");
			iDataClgtLandService.updateById(idleLand);
		}
		return AjaxResult.success("闲置巡查任务撤销成功");
	}

	/**
	 * 新增闲置土地
	 */
	@ApiOperation("新增闲置土地")
//    @PreAuthorize("@ss.hasPermi('idle:land:add')")
	@Log(title = "闲置土地", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataIdleLand bo) {
		boolean success = false;
		LambdaQueryWrapper<DataIdleLand> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataIdleLand::getSupervisionNo, bo.getSupervisionNo());
		queryWrapper.eq(DataIdleLand::getYear, bo.getYear());
		queryWrapper.eq(DataIdleLand::getQuarter, bo.getQuarter());
		DataIdleLand dataIdleLand = iDataIdleLandService.getOne(queryWrapper);
		if (dataIdleLand != null) {
			// 更新
			String[] ignoreFiled = new String[]{"id", "deleteFlag","createBy","create_time","updateBy","updateTime","isAllot"};
			BeanUtils.copyProperties(bo, dataIdleLand,ignoreFiled);
			success = iDataIdleLandService.updateById(dataIdleLand);
		} else {
			bo.setCreateBy(SecurityUtils.getUsername());
			bo.setDeleteFlag("0");
			success = iDataIdleLandService.insert(bo);
		}
		if (success) {
			return AjaxResult.success("导入成功");
		} else {
			return AjaxResult.error("导入失败");
		}
	}

	/**
	 * 批量新增闲置土地
	 */
	@ApiOperation("批量新增闲置土地")
	@Log(title = "闲置土地", businessType = BusinessType.INSERT)
	@PostMapping("/batchAdd")
	public AjaxResult<Void> batchAdd(@Validated @RequestBody List<DataIdleLand> boList) {
		if (null == boList || boList.size() == 0) return AjaxResult.error("新增的闲置土地数据不能为空");
		int successCount = 0;
		StringBuilder failMsg = new StringBuilder();
		for (DataIdleLand idleLand : boList) {
			// 先根据电子监管号判断是否已经存在该项目
			LambdaQueryWrapper<DataIdleLand> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataIdleLand::getSupervisionNo, idleLand.getSupervisionNo());
			queryWrapper.eq(DataIdleLand::getYear, idleLand.getYear());
			queryWrapper.eq(DataIdleLand::getQuarter, idleLand.getQuarter());
			DataIdleLand dataIdleLand = iDataIdleLandService.getOne(queryWrapper,false);
			if (dataIdleLand != null) {
				DataIdleLand temp = new DataIdleLand();
				// 修改对应的字段
				String[] ignoreFiled = new String[]{"id", "deleteFlag","createBy","create_time","updateBy","updateTime","isAllot"};
				BeanUtils.copyProperties(idleLand, temp,ignoreFiled);
				temp.setId(dataIdleLand.getId());
				successCount += iDataIdleLandService.updateById(temp) ? 1 : 0;
				failMsg.append(idleLand.getSupervisionNo()).append(",");
			} else {
				String contractNo = idleLand.getContractNo();
				if (StringUtils.isNotBlank(contractNo)){
					if (contractNo.contains("CR") || contractNo.contains("cr") || contractNo.contains("Cr") || contractNo.contains("cR")){
						idleLand.setSupplyType("CR");
					}else if (contractNo.contains("HB") || contractNo.contains("hb") || contractNo.contains("Hb") || contractNo.contains("hB")){
						idleLand.setSupplyType("HB");
					}
				}
				idleLand.setCreateBy(SecurityUtils.getUsername());
				idleLand.setDeleteFlag("0");
				if (idleLand.getIsAllot() == null) idleLand.setIsAllot("0");
				successCount += iDataIdleLandService.insert(idleLand) ? 1 : 0;
			}
		}
		if (successCount == 0) {
			return AjaxResult.error("导入失败");
		} else {
			return AjaxResult.success("导入成功，其中" + failMsg.toString() + "重复，已经更新");
		}
	}

	/**
	 * 修改闲置土地
	 */
	@ApiOperation("修改闲置土地")
//    @PreAuthorize("@ss.hasPermi('idle:land:edit')")
	@Log(title = "闲置土地", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataIdleLand bo) {
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iDataIdleLandService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除闲置土地
	 */
	@ApiOperation("删除闲置土地")
//    @PreAuthorize("@ss.hasPermi('idle:land:remove')")
	@Log(title = "闲置土地", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String [] ids) {
		return toAjax(iDataIdleLandService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	@Log(title = "闲置土地", businessType = BusinessType.IMPORT)
	@PreAuthorize("@ss.hasPermi('idle:land:import')")
	@PostMapping("/importData")
	public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
		return AjaxResult.success("暂不支持导入");
//		ExcelUtil<DataIdleLand> util = new ExcelUtil<>(DataIdleLand.class);
//		List<DataIdleLand> idleLandList = util.importExcel(file.getInputStream());
//		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//		long operaId = loginUser.getUser().getUserId();
//		String message = iDataIdleLandService.importIdleLand(idleLandList, updateSupport, SecurityUtils.getUsername());
//		return AjaxResult.success(message);
	}

	@GetMapping("/importTemplate")
	public AjaxResult importTemplate() {
		ExcelUtil<DataIdleLand> util = new ExcelUtil<>(DataIdleLand.class);
		return util.importTemplateExcel("闲置土地数据");
	}

	@GetMapping("/getTemplate")
	@ApiOperation("下载闲置土地上传模板")
	public void getTemplate(HttpServletResponse response, HttpServletRequest request) {
		try {
			ClassPathResource classPathResource = new ClassPathResource("template.zip");
			File file = classPathResource.getFile();
			InputStream inputStream = classPathResource.getInputStream();
			//输出文件
			InputStream fis = new BufferedInputStream(inputStream);
			byte[] buffer = new byte[fis.available()];
			fis.read(buffer);
			fis.close();
			response.reset();

			//获取文件的名字再浏览器下载页面
			String name = file.getName();
			response.addHeader("Content-Disposition", "attachment;filename=" + new String(name.getBytes(), "iso-8859-1"));
			response.addHeader("Content-Length", "" + file.length());
			OutputStream out = new BufferedOutputStream(response.getOutputStream());
			response.setContentType("application/octet-stream");
			out.write(buffer);
			out.flush();
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@ApiOperation("统计闲置土地数据")
	@GetMapping("/idleStat")
	public AjaxResult idleStat(String regionCode){
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		List<StatisticsVo> voList = new ArrayList<>();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				// 只能看自己县区的
				voList = iDataIdleLandService.idleStat(sysUser.getRegionCode());
			}else{
				// 能查看所有的
				if (StringUtils.isNotBlank(regionCode)){
					// 根据指定的行政区过滤
					voList = iDataIdleLandService.idleStat(regionCode);
				}else{
					// 统计所有行政区
					voList = iDataIdleLandService.idleStat("");
				}
			}
		}
		return AjaxResult.success("成功获取统计数据",voList);
	}
}
