package com.ruoyi.idle.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.idle.domain.DataClgtLand;
import com.ruoyi.idle.domain.DataHouseCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡查任务 视图对象 data_patrol_task
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ApiModel("巡查任务 视图对象")
public class DataPatrolTaskVo {
	private static final long serialVersionUID = 1L;

	/** 主键编号 */
	@ApiModelProperty("主键编号")
	private String id;

	/** 闲置项目编号 */
	@Excel(name = "闲置项目编号")
	@ApiModelProperty("闲置项目编号")
	private String idleLandId;

	/** 项目名称 */
	@Excel(name = "项目名称")
	@ApiModelProperty("项目名称")
	private String projectName;

	/** 任务季度 */
	@Excel(name = "任务季度")
	@ApiModelProperty("任务季度")
	private String quarter;

	/** 巡查类型（0自行举证 1连线核查 2内业核查） */
	@Excel(name = "巡查类型", readConverterExp = "0=自行举证,1=连线核查,2=内业核查")
	@ApiModelProperty("巡查类型（0自行举证 1连线核查 2内业核查）")
	private String patrolType;

	/** 在线核查开始时间 */
	@Excel(name = "在线核查开始时间")
	@ApiModelProperty("在线核查开始时间")
	private String meetingTime;

	/** 项目类型（0闲置土地 1批而未供） */
	@Excel(name = "项目类型", readConverterExp = "0=闲置土地,1=批而未供")
	@ApiModelProperty("项目类型（0闲置土地 1批而未供）")
	private String projectType;

	/** 巡查组 */
	@Excel(name = "巡查组")
	@ApiModelProperty("巡查组")
	private String patrolGroup;

	/** 行政区代码 */
	@Excel(name = "行政区代码")
	@ApiModelProperty("行政区代码")
	private String regionCode;

	/** 行政区名称 */
	@Excel(name = "行政区名称")
	@ApiModelProperty("行政区名称")
	private String regionName;

	/** 发布状态 */
	@Excel(name = "发布状态")
	@ApiModelProperty("发布状态")
	private String publishStatus;

	/** 是否已巡查 */
	@Excel(name = "是否已巡查")
	@ApiModelProperty("是否已巡查")
	private String isPatrol;

	/** 备注 */
	@Excel(name = "备注")
	@ApiModelProperty("备注")
	private String remark;

	/** 在线核查预计结束时间 */
	@Excel(name = "在线核查预计结束时间")
	@ApiModelProperty("在线核查预计结束时间")
	private String meetingEndTime;

	/** 删除标志 */
	@Excel(name = "删除标志")
	@ApiModelProperty("删除标志")
	private String deleteFlag;

	/** 状态 */
	@Excel(name = "状态")
	@ApiModelProperty("状态")
	private String status;

	/** 巡查组名称 */
	@Excel(name = "巡查组名称")
	@ApiModelProperty("巡查组名称")
	private String patrolGroupName;

	/** 外业巡查意见 */
	@Excel(name = "外业巡查意见")
	@ApiModelProperty("外业巡查意见")
	private String patrolOpinion;

	/** 巡查结果说明 */
	@Excel(name = "巡查结果说明")
	@ApiModelProperty("巡查结果说明")
	private String patrolResult;

	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	private String contractNo;

	/**
	 * 电子监管号
	 */
	@ApiModelProperty(value = "电子监管号")
	private String supervisionNo;

	/** 房屋调查 */
	@Excel(name = "房屋调查")
	@ApiModelProperty("房屋调查")
	private DataHouseCheck dataHouseCheck;

	/** 国土空间表 */
	@Excel(name = "国土空间表")
	@ApiModelProperty("国土空间表")
	private DataClgtLand dataClgtLand;


	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	@TableField(exist = false)
	private Integer bh;
}
