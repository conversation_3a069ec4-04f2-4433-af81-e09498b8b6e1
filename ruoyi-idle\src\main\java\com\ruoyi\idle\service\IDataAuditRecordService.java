package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataAuditRecord;
import com.ruoyi.idle.domain.bo.DataAuditRecordQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 审核记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
public interface IDataAuditRecordService extends IServicePlus<DataAuditRecord> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataAuditRecord queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataAuditRecord> queryPageList(DataAuditRecordQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataAuditRecord> queryList(DataAuditRecordQueryBo bo);

	/**
	 * 根据新增业务对象插入审核记录
	 *
	 * @param bo 审核记录 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataAuditRecord bo);

	/**
	 * 根据编辑业务对象修改审核记录
	 *
	 * @param bo 审核记录 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataAuditRecord bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
