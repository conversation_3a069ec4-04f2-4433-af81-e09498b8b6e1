package com.ruoyi.idle.domain.bo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.idle.domain.vo.IdleAllotTaskUserVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: PatrolTaskEditVo
 * @Description: com.ruoyi.idle.domain.bo
 * @Author: HongDeng
 * @Date: 2021/7/14 11:12
 * @Version: 1.0.0
 **/
@Data
public class PatrolTaskEditVo {
	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;

	/**
	 * 巡查类型（0自行举证 1连线核查 2内业核查）
	 */
	@ApiModelProperty(value = "巡查类型")
	private String patrolType;

	/**
	 * 在线核查开始时间
	 */
	@ApiModelProperty(value = "在线核查开始时间")
	private String meetingTime;

	/**
	 * 在线核查预计结束时间
	 */
	@ApiModelProperty(value = "在线核查预计结束时间")
	private String meetingEndTime;

	/**
	 * 巡查组
	 */
	@ApiModelProperty(value = "巡查组")
	private String patrolGroup;

	/**
	 * 内页核实人员
	 */
	@ApiModelProperty(value = "内页核实人员")
	private List<IdleAllotTaskUserVo> userList;
}
