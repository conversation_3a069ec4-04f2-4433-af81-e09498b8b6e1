<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataHouseCheckMapper">

    <resultMap type="com.ruoyi.idle.domain.DataHouseCheck" id="DataHouseCheckResult">
                    <result property="id" column="id"/>
                    <result property="jzbh" column="jzbh"/>
                    <result property="jzybh" column="jzybh"/>
                    <result property="jzbm" column="jzbm"/>
                    <result property="mc" column="mc"/>
                    <result property="jdmj" column="jdmj"/>
                    <result property="dscs" column="dscs"/>
                    <result property="dsjzmj" column="dsjzmj"/>
                    <result property="jzgd" column="jzgd"/>
                    <result property="yxjz" column="yxjz"/>
                    <result property="fwts" column="fwts"/>
                    <result property="xxdz" column="xxdz"/>
                    <result property="jznd" column="jznd"/>
                    <result property="dcsj" column="dcsj"/>
                    <result property="jzzt" column="jzzt"/>
                    <result property="jglx" column="jglx"/>
                    <result property="sjyt" column="sjyt"/>
                    <result property="ghyt" column="ghyt"/>
                    <result property="jgyt" column="jgyt"/>
                    <result property="jzfcyt" column="jzfcyt"/>
                    <result property="fytlcd" column="fytlcd"/>
                    <result property="fytjzmj" column="fytjzmj"/>
                    <result property="dxcs" column="dxcs"/>
                    <result property="dxjzmj" column="dxjzmj"/>
                    <result property="fwxz" column="fwxz"/>
                    <result property="xzsj" column="xzsj"/>
                    <result property="ydsyq" column="ydsyq"/>
                    <result property="txsm" column="txsm"/>
                    <result property="deleteFlag" column="delete_flag"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="taskId" column="task_id"/>
                    <result property="clgtId" column="clgt_id"/>
                    <result property="mjly" column="mjly"/>
                    <result property="sjly" column="sjly"/>
                    <result property="xzfw" column="xzfw"/>
                    <result property="lybz" column="lybz"/>
                    <result property="bz" column="bz"/>
            </resultMap>

    <select id="selectByClgtId" resultType="com.ruoyi.idle.domain.DataHouseCheck">
        select  *  from data_house_check where  delete_flag = '0' and clgt_id = #{clgtId}
    </select>

    <select id="selectByTaskId" resultType="com.ruoyi.idle.domain.DataHouseCheck">
        select  *  from data_house_check where  delete_flag = '0' and task_id = #{taskId}
    </select>

    <select id="queryHouseList" resultType="com.ruoyi.idle.domain.vo.DataHouseCheckExportVo">
        select  c.*,t.region_code,t.region_name  from data_patrol_task t inner join data_house_check c on t.id = c.task_id where c.delete_flag = '0'
         and t.delete_flag='0'
         and t.is_patrol = '1'
<!--        <if test="bo.regionCode!=null and bo.regionCode!=''">-->
<!--            and t.region_code = #{bo.regionCode}-->
<!--        </if>-->
        <if test="bo.regionCodes!=null">
            and t.region_code in
            <foreach collection="bo.regionCodes" item="regionCode" index="index" separator="," open="(" close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="bo.jzbh!=null and bo.jzbh!=''">
            and c.jzbh = #{bo.jzbh}
        </if>
        <if test="bo.jzybh!=null and bo.jzybh!=''">
            and c.jzybh = #{bo.jzybh}
        </if>
    </select>
</mapper>
