package com.ruoyi.idle.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.PipedReader;

/**
 * @Created with IntelliJ IDEA.
 * @Title: ChangeTaskStatusVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/11/22 10:29
 * @Version: 1.0.0
 **/
@Data
public class ChangeTaskStatusVo {
	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;
	/**
	 * 任务状态
	 */
	@ApiModelProperty(value = "任务状态")
	private String status;
	/**
	 * 审核结果
	 */
	@ApiModelProperty(value = "审核结果")
	private String result;
}
