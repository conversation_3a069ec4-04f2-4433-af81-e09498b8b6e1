package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 成果导出记录 对象 data_export_record
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_export_record")
public class DataExportRecord implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id")
	private String id;

	/**
	 * 开始时间
	 */
	private Date startTime;

	/**
	 * 结束时间
	 */
	private Date endTime;

	/**
	 * 打包耗时
	 */
	private String takeUpTime;

	/**
	 * 成果名称
	 */
	private String zipName;

	/**
	 * 成果大小
	 */
	private Long zipSize;

	/**
	 * 下载地址
	 */
	private String zipUrl;

	/**
	 * 导出类型 0单个项目1按行政区2多个项目
	 */
	private String zipType;

	/**
	 * 删除标志
	 */
	private String deleteFlag;

	/**
	 * 状态 0正在打包1打包成功
	 */
	private String status;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
