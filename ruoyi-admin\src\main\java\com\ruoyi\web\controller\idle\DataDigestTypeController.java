package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.idle.domain.DataDigestType;
import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.bo.DataDigestTypeQueryBo;
import com.ruoyi.idle.domain.vo.DataDigestTypeVo;
import com.ruoyi.idle.service.IDataDigestTypeService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 消化类型对应关系 Controller
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Api(value = "消化类型对应关系 控制器", tags = {"消化类型对应关系 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/digest/type")
public class DataDigestTypeController extends BaseController {

	private final IDataDigestTypeService iDataDigestTypeService;

	/**
	 * 查询消化类型对应关系 列表
	 */
	@ApiOperation("查询消化类型对应关系 列表")
	@GetMapping("/list")
	public TableDataInfo<DataDigestTypeVo> list(@Validated DataDigestTypeQueryBo bo) {
		return iDataDigestTypeService.queryPageList(bo);
	}

	/**
	 * 导出消化类型对应关系 列表
	 */
	@ApiOperation("导出消化类型对应关系 列表")
	@Log(title = "消化类型对应关系 ", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataDigestTypeVo> export(@Validated DataDigestTypeQueryBo bo) {
		List<DataDigestTypeVo> list = iDataDigestTypeService.queryList(bo);
		ExcelUtil<DataDigestTypeVo> util = new ExcelUtil<DataDigestTypeVo>(DataDigestTypeVo.class);
		return util.exportExcel(list, "消化类型对应关系 ");
	}

	/**
	 * 获取消化类型对应关系 详细信息
	 */
	@ApiOperation("获取消化类型对应关系 详细信息")
	@GetMapping("/{id}")
	public AjaxResult<DataDigestTypeVo> getInfo(@NotNull(message = "主键不能为空")
												@PathVariable("id") String id) {
		return AjaxResult.success(iDataDigestTypeService.queryById(id));
	}

	/**
	 * 新增消化类型对应关系
	 */
	@ApiOperation("新增消化类型对应关系 ")
	@Log(title = "消化类型对应关系 ", businessType = BusinessType.INSERT)
	@RepeatSubmit
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataDigestType bo) {
		return toAjax(iDataDigestTypeService.insertByAddBo(bo) ? 1 : 0);
	}

	/**
	 * 修改消化类型对应关系
	 */
	@ApiOperation("修改消化类型对应关系 ")
	@Log(title = "消化类型对应关系 ", businessType = BusinessType.UPDATE)
	@RepeatSubmit
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataDigestType bo) {
		return toAjax(iDataDigestTypeService.updateByEditBo(bo) ? 1 : 0);
	}

	/**
	 * 删除消化类型对应关系
	 */
	@ApiOperation("删除消化类型对应关系 ")
	@Log(title = "消化类型对应关系 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataDigestTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	/**
	 * 消化类型树型数据
	 */
	@ApiOperation("消化类型树型数据")
	@GetMapping("/treeData")
	public AjaxResult treeNodeList() {
		List<DataDigestType> catalogList = iDataDigestTypeService.list();
		//配置
		TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
		// 自定义属性名 都要默认值的
		// treeNodeConfig.setWeightKey("orderNum");
		treeNodeConfig.setIdKey("id");
		// 最大递归深度
		treeNodeConfig.setDeep(4);

		//转换器
		List<Tree<String>> treeNodes = TreeUtil.build(catalogList, "0", treeNodeConfig,
			(treeNode, tree) -> {
				tree.setId(treeNode.getId().toString());
				tree.setParentId(treeNode.getParentId().toString());
				tree.setName(treeNode.getTypeName());
				// 扩展属性 ...
				tree.putExtra("typeCode", treeNode.getTypeCode());
				tree.putExtra("status", treeNode.getStatus());
			});

		return AjaxResult.success("消化类型树型数据生成树节点成功", treeNodes);
	}
}
