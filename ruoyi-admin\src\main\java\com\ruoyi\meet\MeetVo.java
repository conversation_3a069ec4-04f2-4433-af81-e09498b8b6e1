package com.ruoyi.meet;

import java.util.List;

public class MeetVo {

	private String creatorName;
	private String creatorXzq;
	private String projectXzq;
	private String mtNo;
	private String createPhone;
	private String meetLongId;

	// 添加一个唯一的标识字段
	private String meetingUuid;

	private String projectName;
	private double longitude;
	private double latitude;
	private String landid;
	private String patrolTaskId;
	private Long createTime;//

	private List<MeetuserVo> pnoneUsers;

	public String getMeetLongId() {
		return meetLongId;
	}

	public void setMeetLongId(String meetLongId) {
		this.meetLongId = meetLongId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getMtNo() {
		return mtNo;
	}

	public void setMtNo(String mtNo) {
		this.mtNo = mtNo;
	}

	public String getCreatePhone() {
		return createPhone;
	}

	public String getMeetingUuid() {
		return meetingUuid;
	}

	public void setMeetingUuid(String meetingUuid) {
		this.meetingUuid = meetingUuid;
	}

	public void setCreatePhone(String createPhone) {
		this.createPhone = createPhone;
	}

	public List<MeetuserVo> getPnoneUsers() {
		return pnoneUsers;
	}

	public void setPnoneUsers(List<MeetuserVo> pnoneUsers) {
		this.pnoneUsers = pnoneUsers;
	}

	public String getCreatorXzq() {
		return creatorXzq;
	}

	public void setCreatorXzq(String creatorXzq) {
		this.creatorXzq = creatorXzq;
	}

	public String getProjectXzq() {
		return projectXzq;
	}

	public void setProjectXzq(String projectXzq) {
		this.projectXzq = projectXzq;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public double getLongitude() {
		return longitude;
	}

	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}

	public double getLatitude() {
		return latitude;
	}

	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	public String getLandid() {
		return landid;
	}

	public void setLandid(String landid) {
		this.landid = landid;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public String getPatrolTaskId() {
		return patrolTaskId;
	}

	public void setPatrolTaskId(String patrolTaskId_) {
		patrolTaskId=patrolTaskId_;
		//patrolTaskId.equals(patrolTaskId_);
	}

	@Override
	public String toString() {
		return "MeetVo{" +
			"creatorName='" + creatorName + '\'' +
			", creatorXzq='" + creatorXzq + '\'' +
			", projectXzq='" + projectXzq + '\'' +
			", mtNo='" + mtNo + '\'' +
			", createPhone='" + createPhone + '\'' +
			", meetLongId='" + meetLongId + '\'' +
			", projectName='" + projectName + '\'' +
			", longitude=" + longitude +
			", latitude=" + latitude +
			", landid=" + landid +
			", PatrolTaskId=" + getPatrolTaskId() +
			", createTime=" + createTime +
			", pnoneUsers=" + pnoneUsers +
			'}';
	}
}
