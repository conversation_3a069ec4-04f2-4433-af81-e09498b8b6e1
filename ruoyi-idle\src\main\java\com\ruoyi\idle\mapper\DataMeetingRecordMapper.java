package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataMeetingRecord;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 会议记录 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
public interface DataMeetingRecordMapper extends BaseMapperPlus<DataMeetingRecord> {

}
