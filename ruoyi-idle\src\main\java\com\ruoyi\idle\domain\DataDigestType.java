package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 消化类型对应关系 对象 data_digest_type
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_digest_type")
public class DataDigestType implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id")
	private String id;

	/**
	 * 父节点编号
	 */
	private String parentId;

	/**
	 * 消化类型名称
	 */
	private String typeName;

	/**
	 * 消化类型代码
	 */
	private String typeCode;

	/**
	 * 删除标志
	 */
	private String deleteFlag;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
