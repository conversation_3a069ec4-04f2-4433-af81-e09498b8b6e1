package org.geo.shape;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.FileDataStore;
import org.geotools.data.FileDataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.opengis.feature.Property;
import org.opengis.feature.simple.SimpleFeature;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
public class ShapeUtil {

    //上传路径
    private static final String FILE_UPLOAD_PATH = "upload" + File.separator;
    // 根路径
    private static final String ROOT_PATH = "E:" + File.separator;
    //下划线
    private static final String UNDER_LINE = "_";
    // 日期路径
    private static final String DATE_PATH = DateUtil.getNowStr() + File.separator;

    public static List<String>getFileNames(String Path,String filterField){
    List<String>fileNames=new ArrayList<>();
    File files = new File(String.valueOf(Path));
    if(!files.exists()){
        log.info("目录不存在");
        return null;
    }
    File fileList[]=files.listFiles();
    for (File file:fileList) {
        String fileName=file.getName();
        String[] split = fileName.split("\\.");
        if (split.length > 0) {
            String suffix = split[split.length - 1];
//            if(suffix.equals("shp")){
            if(suffix.equals(filterField)){
                fileNames.add(file.getName());
            }
        }
    }
    return fileNames;
}
	//解析shp 上传三个文件 .shp .shx .dbf
	public static JSONObject readShp(List<String>fileNames) throws IOException {
		JSONArray features=new JSONArray();
		JSONObject geoRes=GenerateGeojson.structureSHPGeojson(features);
		try {
			//到上传路径中寻找文件并解析

			for (String filename:fileNames) {

				File file = new File(filename);
				// 读取到数据存储中
				FileDataStore dataStore = FileDataStoreFinder.getDataStore(file);

				// 获取特征资源
				SimpleFeatureSource simpleFeatureSource = dataStore.getFeatureSource();
				// 要素集合
				SimpleFeatureCollection simpleFeatureCollection = simpleFeatureSource.getFeatures();

				// 要素数量
				int featureSize = simpleFeatureCollection.size();
				// 获取要素迭代器
				SimpleFeatureIterator featureIterator = simpleFeatureCollection.features();
				while (featureIterator.hasNext()){

					JSONObject proJson=new JSONObject();
					JSONObject geoJso=new JSONObject();
					// 要素对象
					SimpleFeature feature = featureIterator.next();
					// 要素属性信息，名称，值，类型
					Collection<Property> propertyList = (Collection<Property>) feature.getValue();
					for(Property property : propertyList){
						if(property.getName().toString().equals("the_geom")){
							geoJso= WKTUtil.wktToJson(property.getValue().toString());
						}else {
							proJson.put(property.getName().toString(),property.getValue().toString());

						}
					}
					JSONObject readFeature=GenerateGeojson.generatePolygonFeature(geoJso,proJson);
					features.add(readFeature);

				}
				featureIterator.close();
				dataStore.dispose();
			}


		}catch (Exception e){
			e.printStackTrace();
		}finally {
			return geoRes;
		}
	}

//解析shp 上传三个文件 .shp .shx .dbf
public static JSONObject readShp() throws IOException {
    JSONArray features=new JSONArray();
    JSONObject geoRes=GenerateGeojson.structureSHPGeojson(features);
    try {
        //到上传路径中寻找文件并解析
        String dirPath= ROOT_PATH + FILE_UPLOAD_PATH + DATE_PATH;
        List<String>fileNames=getFileNames(dirPath,"shp");
        for (String filename:fileNames) {

            String filePath=dirPath+ filename;
            File file = new File(filePath);
            // 读取到数据存储中
            FileDataStore dataStore = FileDataStoreFinder.getDataStore(file);

            // 获取特征资源
            SimpleFeatureSource simpleFeatureSource = dataStore.getFeatureSource();
            // 要素集合
            SimpleFeatureCollection simpleFeatureCollection = simpleFeatureSource.getFeatures();

            // 要素数量
            int featureSize = simpleFeatureCollection.size();
            // 获取要素迭代器
            SimpleFeatureIterator featureIterator = simpleFeatureCollection.features();
            while (featureIterator.hasNext()){

                  JSONObject proJson=new JSONObject();
                  JSONObject geoJso=new JSONObject();
                // 要素对象
                SimpleFeature feature = featureIterator.next();
                // 要素属性信息，名称，值，类型
                Collection<Property> propertyList = (Collection<Property>) feature.getValue();
                for(Property property : propertyList){
                    if(property.getName().toString().equals("the_geom")){
                      geoJso= WKTUtil.wktToJson(property.getValue().toString());
                    }else {
                        proJson.put(property.getName().toString(),property.getValue().toString());

                    }
                }
                JSONObject readFeature=GenerateGeojson.generatePolygonFeature(geoJso,proJson);
                features.add(readFeature);

            }
            featureIterator.close();
            dataStore.dispose();
        }


    }catch (Exception e){
        e.printStackTrace();
    }finally {
       return geoRes;
    }
    }
}
