package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataDigestType;
import com.ruoyi.idle.domain.bo.DataDigestTypeQueryBo;
import com.ruoyi.idle.domain.vo.DataDigestTypeVo;
import com.ruoyi.idle.mapper.DataDigestTypeMapper;
import com.ruoyi.idle.service.IDataDigestTypeService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 消化类型对应关系 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Service
public class DataDigestTypeServiceImpl extends ServicePlusImpl<DataDigestTypeMapper, DataDigestType> implements IDataDigestTypeService {

	@Override
	public DataDigestTypeVo queryById(String id) {
		return getVoById(id, DataDigestTypeVo.class);
	}

	@Override
	public TableDataInfo<DataDigestTypeVo> queryPageList(DataDigestTypeQueryBo bo) {
		PagePlus<DataDigestType, DataDigestTypeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataDigestTypeVo.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataDigestTypeVo> queryList(DataDigestTypeQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataDigestTypeVo.class);
	}

	private LambdaQueryWrapper<DataDigestType> buildQueryWrapper(DataDigestTypeQueryBo bo) {
		LambdaQueryWrapper<DataDigestType> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getParentId()), DataDigestType::getParentId, bo.getParentId());
		lqw.like(StrUtil.isNotBlank(bo.getTypeName()), DataDigestType::getTypeName, bo.getTypeName());
		lqw.eq(StrUtil.isNotBlank(bo.getTypeCode()), DataDigestType::getTypeCode, bo.getTypeCode());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataDigestType::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataDigestType::getStatus, bo.getStatus());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataDigestType bo) {
		DataDigestType add = BeanUtil.toBean(bo, DataDigestType.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataDigestType bo) {
		DataDigestType update = BeanUtil.toBean(bo, DataDigestType.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataDigestType entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
