package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 与会人员 对象 data_meeting_user
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_meeting_user")
public class DataMeetingUser implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 会议记录编号
	 */
	@ApiModelProperty(value = "会议记录编号")
	private String meetingId;

	/**
	 * 会议记录唯一编号
	 */
	@ApiModelProperty(value = "会议记录唯一编号")
	private String meetingUuid;

	/**
	 * 与会人员编号
	 */
	@ApiModelProperty(value = "与会人员编号")
	private String userId;

	/**
	 * 与会人员姓名
	 */
	@ApiModelProperty(value = "与会人员姓名")
	private String userName;

	/**
	 * 与会人员电话
	 */
	@ApiModelProperty(value = "与会人员电话")
	private String userPhone;

	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private Double longitude;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private Double latitude;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
