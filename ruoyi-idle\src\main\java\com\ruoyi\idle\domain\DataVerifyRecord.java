package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 核查记录 对象 data_verify_record
 *
 * <AUTHOR>
 * @date 2021-11-17
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_verify_record")
public class DataVerifyRecord implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id")
	private String id;

	/**
	 * 巡查任务编号
	 */
	private String taskId;

	/**
	 * 闲置土地或批而未供数据编号
	 */
	private String dataId;

	/**
	 * 项目类型
	 */
	private String projectType;

	/**
	 * 目录编号
	 */
	private String catalogId;

	/**
	 * 目录名称
	 */
	private String catalogName;

	/**
	 * 是否审核通过
	 */
	private String isProcess;

	/**
	 * 视频连线情况是否属实
	 */
	private String videoIsReal;

	/**
	 * 实地核查情况是否属实
	 */
	private String siteIsReal;

	/**
	 * 处置是否合规
	 */
	private String handleIsCorrect;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/** 核查情况  */
	private String verificationSituation;

	/** 核查选项  */
	private String verificationSelect;
}
