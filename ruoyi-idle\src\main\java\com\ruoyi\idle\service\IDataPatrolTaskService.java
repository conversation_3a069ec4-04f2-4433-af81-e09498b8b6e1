package com.ruoyi.idle.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.bo.MyTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 巡查任务 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataPatrolTaskService extends IServicePlus<DataPatrolTask> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataPatrolTask queryById(String id);

	/**
	 * 村级任务总数，未提交，已提交，未巡查任务数
	 * @param pId
	 * @return
	 */
	List<Map<String,Object>> countByPatrol(String pId);

	Map<String, String> appPulLTaskStatusist(  String regionCode,   String userId);
	List<DataPatrolTask> appPulList(  String regionCode,   String userId);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataPatrolTask> queryPageList(DataPatrolTaskQueryBo bo);

	TableDataInfo<DataPatrolTask> queryPageList1(DataPatrolTaskQueryBo bo);

	TableDataInfo<DataPatrolTaskVo> submitTaskList(DataPatrolTaskQueryBo bo);

	void submitTaskListImages(DataPatrolTaskQueryBo bo) throws FileNotFoundException;
	List<Map<String,Object>> getImages(DataPatrolTaskQueryBo bo);
	/**
	 *下载zip
	 */
	void dowloadToZip(DataPatrolTaskQueryBo bo, HttpServletResponse response) throws Exception;

	/**
	 *获取待办列表
	 * type 2为内业 0为外业巡查
	 */
	TableDataInfo<DataPatrolTask> queryPageToDoList(MyTaskQueryBo bo, SysUser sysUser, String type);

	/**
	 * 查询列表
	 */
	List<DataPatrolTask> queryList(DataPatrolTaskQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查任务
	 *
	 * @param bo 巡查任务 新增业务对象
	 * @return
	 */
	Boolean insert(DataPatrolTask bo);

	/**
	 * 根据编辑业务对象修改巡查任务
	 *
	 * @param bo 巡查任务 编辑业务对象
	 * @return
	 */
	Boolean update(DataPatrolTask bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<DataPatrolTask> getAllPatrolTaskList(String startTime);

	List<DataPatrolTaskVo> appCheckList(String regionCode);
}
