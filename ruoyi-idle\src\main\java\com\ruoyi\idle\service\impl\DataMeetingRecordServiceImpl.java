package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataMeetingRecord;
import com.ruoyi.idle.domain.bo.DataMeetingRecordQueryBo;
import com.ruoyi.idle.mapper.DataMeetingRecordMapper;
import com.ruoyi.idle.service.IDataMeetingRecordService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 会议记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
@Service
public class DataMeetingRecordServiceImpl extends ServicePlusImpl<DataMeetingRecordMapper, DataMeetingRecord> implements IDataMeetingRecordService {

	@Override
	public DataMeetingRecord queryById(String id) {
		return getVoById(id, DataMeetingRecord.class);
	}

	@Override
	public TableDataInfo<DataMeetingRecord> queryPageList(DataMeetingRecordQueryBo bo) {
		PagePlus<DataMeetingRecord, DataMeetingRecord> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataMeetingRecord.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataMeetingRecord> queryList(DataMeetingRecordQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataMeetingRecord.class);
	}

	private LambdaQueryWrapper<DataMeetingRecord> buildQueryWrapper(DataMeetingRecord bo) {
		LambdaQueryWrapper<DataMeetingRecord> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getTaskId() != null, DataMeetingRecord::getTaskId, bo.getTaskId());
		lqw.like(StrUtil.isNotBlank(bo.getTaskName()), DataMeetingRecord::getTaskName, bo.getTaskName());
		lqw.eq(bo.getCreateUserId() != null, DataMeetingRecord::getCreateUserId, bo.getCreateUserId());
		lqw.like(StrUtil.isNotBlank(bo.getCreateUserName()), DataMeetingRecord::getCreateUserName, bo.getCreateUserName());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateUserPhone()), DataMeetingRecord::getCreateUserPhone, bo.getCreateUserPhone());
		lqw.eq(StrUtil.isNotBlank(bo.getMeetingNum()), DataMeetingRecord::getMeetingNum, bo.getMeetingNum());
		lqw.eq(StrUtil.isNotBlank(bo.getMeetingLongId()), DataMeetingRecord::getMeetingLongId, bo.getMeetingLongId());
		lqw.eq(bo.getLongitude() != null, DataMeetingRecord::getLongitude, bo.getLongitude());
		lqw.eq(bo.getLatitude() != null, DataMeetingRecord::getLatitude, bo.getLatitude());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataMeetingRecord::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataMeetingRecord::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataMeetingRecord::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataMeetingRecord::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataMeetingRecord::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataMeetingRecord::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataMeetingRecord bo) {
		DataMeetingRecord add = BeanUtil.toBean(bo, DataMeetingRecord.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataMeetingRecord bo) {
		DataMeetingRecord update = BeanUtil.toBean(bo, DataMeetingRecord.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataMeetingRecord entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
