package com.ruoyi.idle.service.impl;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.idle.service.ISendSmsService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Created with IntelliJ IDEA.
 * @Title: SendSmsServiceImpl
 * @Description: com.ruoyi.idle.service.impl
 * @Author: HongDeng
 * @Date: 2021/7/16 9:28
 * @Version: 1.0.0
 **/
@Service
public class SendSmsServiceImpl implements ISendSmsService {

	// 2025-5-27
//	AccessKey ID
//	LTAI5tBoCbQTy84o8qWUd1Lm
//
//	AccessKey Secret
//	******************************
	// 这里采用 注入的方式传递参数
//	@Value("${aliyun.accessKeyID}")
	private String accessKeyID = "LTAI5tBoCbQTy84o8qWUd1Lm";
	//	@Value("${aliyun.accessKeySecret}")
	private String accessKeySecret = "******************************";

	@Override
	public boolean sendSms(String phoneNum, String code) {
		DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyID, accessKeySecret);
		IAcsClient client = new DefaultAcsClient(profile);

		CommonRequest request = new CommonRequest();
		request.setMethod(MethodType.POST);
		request.setDomain("dysmsapi.aliyuncs.com");
		request.setVersion("2017-05-25");
		request.setAction("SendSms");
		request.putQueryParameter("RegionId", "cn-hangzhou");
		request.putQueryParameter("SignName", "昆明市土地开发整理中心");
		request.putQueryParameter("PhoneNumbers", phoneNum);
		request.putQueryParameter("TemplateCode", "SMS_487230278");

		Map<String, Object> params = new HashMap<>();
		params.put("code", code);

		request.putQueryParameter("TemplateParam", JsonUtils.toJsonString(params));

		try {
			CommonResponse response = client.getCommonResponse(request);
			System.out.println(response.getData());
			return response.getHttpResponse().isSuccess();

		} catch (ServerException e) {
			e.printStackTrace();
		} catch (ClientException e) {
			e.printStackTrace();
		}
		return false;
	}
}
