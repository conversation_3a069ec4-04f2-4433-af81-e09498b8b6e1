package com.ruoyi.web.controller.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.SseEmitterUtil;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.uuid.uuid.IdUtils;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.idle.domain.DataClgtLand;
import com.ruoyi.idle.domain.DataHouseCheck;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.service.IDataClgtLandService;
import com.ruoyi.idle.service.IDataHouseCheckService;
import com.ruoyi.idle.service.IDataIdleLandService;
import com.ruoyi.idle.service.ISysRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.geo.shape.*;
import org.geotools.data.FileDataStore;
import org.geotools.data.FileDataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.opengis.feature.Property;
import org.opengis.feature.simple.SimpleFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@Api(value = "图斑图形导入控制器", tags = {"核查图斑管理"})
@RestController
public class ShapefileController {

	@Autowired
	private ServerConfig serverConfig;
	@Autowired
	private IDataIdleLandService iDataIdleLandService;

	@Autowired
	private IDataClgtLandService iDataClgtLandService;

	@Autowired
	private IDataHouseCheckService iDataHouseCheckService;

	@Autowired
	private ISysRegionService iSysRegionService;


	private final ExecutorService executor = Executors.newSingleThreadExecutor();
	/**
	 * 用于创建连接
	 */
	@GetMapping("/conn_sse/{userId}")
	public SseEmitter connect(@PathVariable String userId) {
		return SseEmitterUtil.connect(userId);
	}

	@ApiOperation("readShapeFile")
	@PostMapping("/tb_shp/readShapeFile")
	public static void readShapeFile(String path) throws Exception {
		ShpUtils.ParseInfo parseInfo = ShpUtils.readGeometriesFromShpFile(new File("D:\\data\\data\\shp_4326.zip"));

		Map<String, String> landMap = new HashMap<>();
		landMap.put("xxx", DataClgtLand.Fields.projectName);

		Map<String, String> houseMap = new HashMap<>();
		houseMap.put(DataHouseCheck.Fields.dxcs, "xxx");

		List<List<ShpUtils.RecordMetaData>> parseRecords = parseInfo.getParseRecords();
		for (List<ShpUtils.RecordMetaData> parseRecord : parseRecords) {

			DataClgtLand dataClgtLand = new DataClgtLand();
			DataHouseCheck dataHouseCheck = new DataHouseCheck();

			parseRecord.forEach(item -> {
				String objectKey1 = landMap.get(item.getName());
				if (StrUtil.isNotBlank(objectKey1)) {
					BeanUtil.setFieldValue(dataClgtLand, objectKey1, item.getValue());
				}

				String objectKey2 = houseMap.get(item.getName());
				if (StrUtil.isNotBlank(objectKey2)) {
					BeanUtil.setFieldValue(dataHouseCheck, objectKey2, item.getValue());
				}
			});
		}

		System.out.println(parseInfo);
	}
	// 文件上传
	@ApiOperation("核查图斑shape数据批量导入")
	@PostMapping("/tb_shp/upload")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "file", value = "上传的文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true)})
//	@Transactional
	public AjaxResult uploadFile(@RequestParam("file") MultipartFile file,String yeaer ,String quarter,String remark) {
		// 上传文件路径
		String filePath = RuoYiConfig.getUploadPath();

		try {
			// 上传并返回新文件名称
			String fileName = FileUploadUtils.uploadShape(filePath, file);
			File fileParent = new File(fileName);
			String strParentDirectory = fileParent.getParent();

			strParentDirectory=strParentDirectory.replace('\\','/');

			if(!strParentDirectory.endsWith("/")){
				strParentDirectory=strParentDirectory+"/";
			}
			String str=fileParent.getName().substring(0,fileParent.getName().lastIndexOf("."));
			strParentDirectory=strParentDirectory+str;
			File newfileDir = new File(strParentDirectory);
			if(!newfileDir.exists()) {
				newfileDir.mkdirs();
			}
			FileUtil.unPackZip(fileName,strParentDirectory);

			ArrayList<String> shapeFiles= FileUtil.getShapefileFromDir(strParentDirectory);
//			List<DataClgtLand> boList = readShp(shapeFiles,yeaer,quarter);
			//异步执行并获取结果
			CompletableFuture<Void> future = CompletableFuture.runAsync(()->{

				Map<String, List> map  = null;
				try {
					map = readShp(shapeFiles, yeaer, quarter,remark);
				} catch (IOException e) {
					e.printStackTrace();
				}
				List<DataClgtLand> boList = map.get("boList");
			  List<DataHouseCheck> houseCheckList = map.get("houseCheckList");
			  AjaxResult<Void> rr = batchAdd(boList,houseCheckList);

			});
//			AjaxResult<Void> result = future.join();
//			AjaxResult<Void> result1 = future.thenApply(result -> {
//				return result; // 如果需要进一步处理结果，可以返回新值或原值
//			}).join();// 确保主线程等待异步操作完成，尽管这不是必须的，因为这里只是为了示例确保主线程结束前异步操作已完成。
			return AjaxResult.success("正在导入数据");
//			String url = serverConfig.getUrl() + fileName;
//			Map<String, Object> ajax = new HashMap<>();
//			ajax.put("fileName", fileName);
//			ajax.put("url", url);
//
//			return AjaxResult.success(ajax);
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	// 测试读取shape 数据
	public static void main(String[] args){
		String pathSHP = "E:/data/data";
		ArrayList<String> shapeFiles= FileUtil.getShapefileFromDir(pathSHP);
		java.util.Calendar calendar = java.util.Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		int quarter = calendar.get(Calendar.MONTH)/3+1;
		String yearStr = Integer.toString(year);
		String quarterStr = Integer.toString(quarter);

		ShapefileController shapefileController=new ShapefileController();
        try {
            List<DataIdleLand> boList = shapefileController.readClgtHouseShp(shapeFiles,yearStr,quarterStr);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
	public List<DataIdleLand> readClgtHouseShp(List<String> fileNames,String year1,String quarter) throws IOException {
		//JSONArray features=new JSONArray();
		//JSONObject geoRes= GenerateGeojson.structureSHPGeojson(features);
		List<DataIdleLand> boList =new ArrayList<>();
		try {
			//到上传路径中寻找文件并解析

			for (String filename:fileNames) {

				File file = new File(filename);
				// 读取到数据存储中
				FileDataStore dataStore = FileDataStoreFinder.getDataStore(file);

				// 获取特征资源
				SimpleFeatureSource simpleFeatureSource = dataStore.getFeatureSource();
				// 要素集合
				SimpleFeatureCollection simpleFeatureCollection = simpleFeatureSource.getFeatures();

				// 要素数量
				int featureSize = simpleFeatureCollection.size();
				// 获取要素迭代器
				int count=0;
				SimpleFeatureIterator featureIterator = simpleFeatureCollection.features();
				while (featureIterator.hasNext()){

					JSONObject proJson=new JSONObject();
					String geoJso="";
					// 要素对象
					SimpleFeature feature = featureIterator.next();
					// 要素属性信息，名称，值，类型
					Collection<Property> propertyList = (Collection<Property>) feature.getValue();

					DataIdleLand land = new DataIdleLand();
					Geometry geometry = (Geometry)feature.getDefaultGeometry();

					String id = feature.getIdentifier().getID();
					Point directPosition= geometry.getCentroid();
					//double[] xy= directPosition.getCoordinate();
					land.setLongitude(Double.toString(directPosition.getX()));
					land.setLatitude(Double.toString(directPosition.getY()));
					land.setWarrantNum(id);
					for(Property property : propertyList){
						String propName = property.getName().toString();
						if(propName.equals("the_geom")){
							geoJso= WKTUtil.wktToJsonStr(property.getValue().toString());
						}else {
							proJson.put(property.getName().toString(),property.getValue().toString());
						}

						if(propName.toString().equals("XMBH")){
							land.setContractNo(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("XQMC")){
							land.setCountyName(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("XHLX")){
							land.setDigestionType(StringUtils3.trim(property.getValue().toString()) );
							//land.setSpotNumber(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("HCFS")){
							land.setInsideRemark(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("TBMJ")){
							land.setLandArea(StringtoBigDecimal(StringUtils3.trim(property.getValue().toString()))  );
						}else if(propName.equals("XMMC")){
							land.setProjectName(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("TBBH")){
							land.setSpotNumber(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("DZBABH")){
							land.setSupervisionNo(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("年份")){
							String vii = StringUtils3.trim(property.getValue().toString());
							if(vii.length()>4){
								vii=vii.substring(0,4);
							}
							int yeart = StringUtils3.StrToInt(vii);
							if(yeart>1997) {
								land.setYear(Integer.toString(yeart));
							}
						}else if(propName.equals("TDZL")){
							land.setAddress(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("TDYT")){
							land.setLandUse(StringUtils3.trim(property.getValue().toString()) );
							//String dd = StringUtils3.trim(property.getValue().toString());

							//land.setSignDate(StringUtils3.parseDateZf(dd) );
						}else if(propName.equals("SJDGSJ")){
							if (!StringUtils3.trim(property.getValue().toString()).equals("")){
								land.setActualStartTime(StringToDate(StringUtils3.trim(property.getValue().toString())));
							}
						}else if(propName.equals("YDDGSJ")){
							if (!StringUtils3.trim(property.getValue().toString()).equals("")){
								land.setAgreedStartTime(StringToDate(StringUtils3.trim(property.getValue().toString())));
							}
						}else if(propName.equals("SJJGSJ")){
							if (!StringUtils3.trim(property.getValue().toString()).equals("")){
								land.setActualEndTime(StringToDate(StringUtils3.trim(property.getValue().toString())));
							}
						}

					}
					land.setQuarter(quarter);
					land.setYear(year1);

					land.setCity("昆明市");
//					land.setIsAllot("0");
					if(geoJso!=null && !geoJso.equals("")){
						land.setGeoData(geoJso);
					}
					land.setId(IdUtils.simpleUUID());
					//JSONObject readFeature=GenerateGeojson.generatePolygonFeature(geoJso,proJson);
					//features.add(readFeature);
					boList.add(land);
					count++;
				}
				featureIterator.close();
				dataStore.dispose();
			}

		}catch (Exception e){
			e.printStackTrace();

			String x = "456";
			for (DataIdleLand bo : boList){
				String geo = bo.getGeoData();
				JSONObject jsonObject = JSONObject.parseObject(geo);
				String xx = "456";
			}
			return boList;
		}finally {
			String x = "456";
			for (DataIdleLand bo : boList){
				String geo = bo.getGeoData();
				JSONObject jsonObject = JSONObject.parseObject(geo);
				Object geox = jsonObject.get("coordinates");
				ArrayList test2 = new ArrayList();
				ArrayList test = new ArrayList();
				String[] mix = {"102.68079733472928", "24.680844246364927"};
				String[] mixx = {"102.68079733472928", "24.680844246364927"};
				test.add(mix);
				test.add(mixx);
				test2.add(test);
				jsonObject.put("coordinates",test2);
				String xx = "456";
			}
			return boList;
		}
	}
	public AjaxResult<Void> batchAdd( List<DataClgtLand> boList,List<DataHouseCheck> houseCheckList) {
		if (null == boList || boList.size() == 0) return AjaxResult.error("新增的闲置土地数据不能为空");
		int successCount = 0;
		StringBuilder failMsg = new StringBuilder();
		for (DataClgtLand idleLand : boList) {
			// 先根据电子监管号判断是否已经存在该项目
			{
				String contractNo = idleLand.getContractNo();
				if (StringUtils.isNotBlank(contractNo)){
					if (contractNo.contains("CR") || contractNo.contains("cr") || contractNo.contains("Cr") || contractNo.contains("cR")){
						idleLand.setSupplyType("CR");
					}else if (contractNo.contains("HB") || contractNo.contains("hb") || contractNo.contains("Hb") || contractNo.contains("hB")){
						idleLand.setSupplyType("HB");
					}
				}
				idleLand.setCreateBy(SecurityUtils.getUsername());
				idleLand.setDeleteFlag("0");
				if (idleLand.getIsAllot() == null) idleLand.setIsAllot("0");
//				successCount += iDataClgtLandService.insertByAddBo(idleLand) ? 1 : 0;
			}
		}
		boolean b = iDataHouseCheckService.saveBatch(houseCheckList);
		successCount += iDataClgtLandService.saveBatch(boList) ? 1 : 0;
		if (successCount == 0 || !b) {
			return AjaxResult.error("导入失败");
		} else {
			return AjaxResult.success("导入成功，其中" + failMsg.toString() + "重复，已经更新");
		}
	}

	public Map<String,List> readShp(List<String> fileNames,String year1,String quarter,String remark) throws IOException {
		//JSONArray features=new JSONArray();
		//JSONObject geoRes= GenerateGeojson.structureSHPGeojson(features);
		Map<String, List> map = new HashMap<>();
		List<DataClgtLand> boList =new ArrayList<>();
		List<DataHouseCheck> houseCheckList =new ArrayList<>();
		//获取任务编号
		String taskNo = iDataClgtLandService.taskNo();
		try {
			//到上传路径中寻找文件并解析

			for (String filename:fileNames) {

				File file = new File(filename);
				// 读取到数据存储中
				FileDataStore dataStore = FileDataStoreFinder.getDataStore(file);

				// 获取特征资源
				SimpleFeatureSource simpleFeatureSource = dataStore.getFeatureSource();
				// 要素集合
				SimpleFeatureCollection simpleFeatureCollection = simpleFeatureSource.getFeatures();

				// 要素数量
				int featureSize = simpleFeatureCollection.size();
				// 获取要素迭代器
				int count=0;
				SimpleFeatureIterator featureIterator = simpleFeatureCollection.features();
				while (featureIterator.hasNext()){

					JSONObject proJson=new JSONObject();
					String geoJso="";
					// 要素对象
					SimpleFeature feature = featureIterator.next();
					// 要素属性信息，名称，值，类型
					Collection<Property> propertyList = (Collection<Property>) feature.getValue();

					DataClgtLand land = new DataClgtLand();
					DataHouseCheck houseCheck = new DataHouseCheck();
					Geometry geometry = (Geometry)feature.getDefaultGeometry();

					String id = feature.getIdentifier().getID();
					Point directPosition= geometry.getCentroid();
					//double[] xy= directPosition.getCoordinate();
					land.setLongitude(Double.toString(directPosition.getX()));
					land.setLatitude(Double.toString(directPosition.getY()));
					land.setWarrantNum(id);
					for(Property property : propertyList){
						String propName = property.getName().toString();
						if(propName.equals("the_geom")){
							geoJso= WKTUtil.wktToJsonStr(property.getValue().toString());
						}else {
							proJson.put(property.getName().toString(),property.getValue().toString());
						}
						if(propName.toString().equals("JZBH")){
							houseCheck.setJzbh(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.toString().equals("JZYBH")){
							houseCheck.setJzybh(StringUtils3.trim(property.getValue().toString()) );
						} else if(propName.equals("CJXZQDM")){
							String cjxzqdm = StringUtils3.trim(property.getValue().toString());
							land.setCountyCode(cjxzqdm);
							if(StringUtils.isNotBlank(cjxzqdm)){
								String name = iSysRegionService.getNameByCode(cjxzqdm);
								if (StringUtils.isNotBlank(name)) {
									land.setCountyName(name);
								}
							}
						}else if(propName.equals("JZBM")){
							houseCheck.setJzbm(StringUtils3.trim(property.getValue().toString()) );
							//land.setSpotNumber(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("JDMJ")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if (StringUtils.isNotBlank(trim)) {
								houseCheck.setJdmj(Double.parseDouble(trim));
							}else{
								houseCheck.setJdmj(0.0);
							}
						}else if(propName.equals("DSCS")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if (StringUtils.isNotBlank(trim)) {
								houseCheck.setDscs(trim);
							}else{
								houseCheck.setDscs("0");
							}
						}else if(propName.equals("JZGD")){
							if (StringUtils.isNotBlank(StringUtils3.trim(property.getValue().toString()))) {
								houseCheck.setJzgd(Double.parseDouble(StringUtils3.trim(property.getValue().toString())));
							}
						}else if(propName.equals("DSJZMJ")){
							if (StringUtils.isNotBlank(StringUtils3.trim(property.getValue().toString()))) {
								houseCheck.setDsjzmj(Double.parseDouble(StringUtils3.trim(property.getValue().toString())));
							}else{
								houseCheck.setDsjzmj(0.0);
							}
						}else if(propName.equals("SJLY")){
							houseCheck.setSjly(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("MJLY")){
							houseCheck.setMjly(StringUtils3.trim(property.getValue().toString()) );
						}else if(propName.equals("YXJZ")){
							houseCheck.setYxjz(StringUtils3.trim(property.getValue().toString()) );
							//String dd = StringUtils3.trim(property.getValue().toString());
							//land.setSignDate(StringUtils3.parseDateZf(dd) );
						}else if(propName.equals("DZ")){
							if (!StringUtils3.trim(property.getValue().toString()).equals("")){
								houseCheck.setXxdz(StringUtils3.trim(property.getValue().toString()));
							}
						}else if(propName.equals("JZND")){
							if (StringUtils.isNotBlank(StringUtils3.trim(property.getValue().toString()))){
//								houseCheck.setJznd(StringToDate(StringUtils3.trim(property.getValue().toString())));
							}
						}else if(propName.equals("SJYT")){
							if (!StringUtils3.trim(property.getValue().toString()).equals("")){
								houseCheck.setSjyt(StringUtils3.trim(property.getValue().toString()));
							}
						}else if(propName.equals("DXCS")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if(StringUtils.isNotBlank(trim)){
								houseCheck.setDscs(trim);
							} else {
								houseCheck.setDscs("0");
							}
						}else if(propName.equals("DXJZMJ")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if(StringUtils.isNotBlank(trim)){
								houseCheck.setDxjzmj(Double.parseDouble(trim));
							} else {
								houseCheck.setDxjzmj(0.0);
							}
						}else if(propName.equals("FWTS")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if(StringUtils.isNotBlank(trim)){
								houseCheck.setFwts(Long.parseLong(trim));
							} else {
								houseCheck.setFwts(0l);
							}
						}else if(propName.equals("JGLX")){
							houseCheck.setJglx(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("JZZT")){
							houseCheck.setJzzt(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("GHYT")){
							houseCheck.setGhyt(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("JGYT")){
							houseCheck.setJgyt(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("FYTLC")){
							houseCheck.setFytlcd(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("FCYT")){
							houseCheck.setFytlcd(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("FYTMJ")){
							String trim = StringUtils3.trim(property.getValue().toString());
							if(StringUtils.isNotBlank(trim)){
								houseCheck.setFytjzmj(Double.parseDouble(trim));
							}else {
								houseCheck.setFytjzmj(0.0);
							}
						}else if(propName.equals("TDSYQ")){
							houseCheck.setYdsyq(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("FWXZ")){
							houseCheck.setFwxz(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("XZFW")){
//							houseCheck.setXzfw(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("LYBJ")){
							houseCheck.setLybz(StringUtils3.trim(property.getValue().toString()));
						}else if(propName.equals("BZ")){
							houseCheck.setBz(StringUtils3.trim(property.getValue().toString()));
						}
					}
					land.setQuarter(quarter);
					land.setYear(year1);
					land.setRemark(remark);
					land.setTaskNo(taskNo);
					land.setCity("昆明市");
//					land.setIsAllot("0");
					if(geoJso!=null && !geoJso.equals("")){
						land.setGeoData(geoJso);
					}
					land.setId(IdUtils.simpleUUID());
					houseCheck.setClgtId(land.getId());
					houseCheck.setId(IdUtils.simpleUUID());
					//JSONObject readFeature=GenerateGeojson.generatePolygonFeature(geoJso,proJson);
					//features.add(readFeature);
					boList.add(land);
					houseCheckList.add(houseCheck);
					map.put("boList",boList);
					map.put("houseCheckList",houseCheckList);
					count++;
				}
				featureIterator.close();
				dataStore.dispose();
			}

		}catch (Exception e){
			e.printStackTrace();

			String x = "456";
			for (DataClgtLand bo : boList){
				String geo = bo.getGeoData();
				JSONObject jsonObject = JSONObject.parseObject(geo);
				String xx = "456";
			}
			return map;
		}finally {
			String x = "456";
			for (DataClgtLand bo : boList){
				String geo = bo.getGeoData();
				JSONObject jsonObject = JSONObject.parseObject(geo);
				Object geox = jsonObject.get("coordinates");
				ArrayList test2 = new ArrayList();
				ArrayList test = new ArrayList();
				String[] mix = {"102.68079733472928", "24.680844246364927"};
				String[] mixx = {"102.68079733472928", "24.680844246364927"};
				test.add(mix);
				test.add(mixx);
				test2.add(test);
				jsonObject.put("coordinates",test2);
				String xx = "456";
			}
			return map;
		}
	}

	public BigDecimal StringtoBigDecimal ( String str) {
		BigDecimal bd = new BigDecimal(str);
		return bd;
	}
	public Date StringToDate ( String str) throws ParseException {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = simpleDateFormat.parse(str);
		return date;
	}
}
