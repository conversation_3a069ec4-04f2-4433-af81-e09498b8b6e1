package com.ruoyi.meet.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.meet.MeetVo;
import com.ruoyi.meet.MeetuserVo;
import com.ruoyi.meet.utils.Des;
import com.ruoyi.socket.StrConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import com.ruoyi.socket.MeetStatusManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(value = "实时视频会议", tags = {"实时视频会议"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/phone/meet")
public class MeetingController {
	/**
	 * Token的服务
	 */
	private final TokenService tokenService;

	/**
	 * 查询巡查任务用户关系 列表
	 */
	@ApiOperation("查询运行的会议参会人员列表")
	@GetMapping("/meetusers")
	public AjaxResult listMeetUsers(String meetNo) {
		return AjaxResult.success(MeetStatusManager.getMeetUsers(meetNo));
	}

	@ApiOperation("提交用户位置-仅在内存中")
	@PostMapping("/postuserPoc")
	public AjaxResult answerMeet(
		@RequestParam("phone") String phone,
		@RequestParam("n") String n,
		@RequestParam("x") String x,
		@RequestParam("y") String y,
		 String angle){

		MeetRealTime.addUserXy(phone ,new RealTimeXy(x,y,n,angle));

		return AjaxResult.success();
	}
	@ApiOperation("获取所有用户位置-仅在内存中")
	@GetMapping("/getusersPoc")
	public AjaxResult getusersPoc( ){


		return AjaxResult.success(MeetRealTime.getAll( ));
	}

	/**
	 * 查询巡查任务用户关系 列表
	 */
	@ApiOperation("查询运行的会议列表")
	@GetMapping("/allmeet")
	public AjaxResult listMeeting() {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 一般用户和县区人员只能看到z自己行政区的会议
			if (org.apache.commons.lang.StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				// 根据登录用户的行政区过滤本行政区的会议记录
				List<MeetVo> list = MeetStatusManager.getAllMeetRunnig();
				if (list.size() == 0) return AjaxResult.success("没有匹配到符合条件的会议列表");
				List<MeetVo> meetingList = list.stream().filter(a -> a.getProjectXzq().equals(sysUser.getRegionCode())).collect(Collectors.toList());
				return AjaxResult.success(meetingList);
			} else {
				return AjaxResult.success(MeetStatusManager.getAllMeetRunnig());
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	@PostMapping("/answer")
	public AjaxResult answerMeet(
		@RequestParam("mtNo") String mtNo,
		@RequestParam("NowPhone") String NowPhone,
		@RequestParam("user") String user,
		@RequestParam("code") int code) {
		if (StringUtils.isNotBlank(mtNo)
			&& StringUtils.isNotBlank(NowPhone)
			&& StringUtils.isNotBlank(user)) {
			try {
				String mtNo33 = Des.decrypt(mtNo, "f1f3h6h9");
				String NowPhone3 = Des.decrypt(NowPhone, "f1f3h9h9");

				JSONObject object1 = new JSONObject();

				//1.在JSONObject对象中放入键值对
				object1.put("code", code);

				object1.put("mtNo", mtNo33);
				object1.put("user", user);
				object1.put("phone", NowPhone3);

				MeetStatusManager.DealClientMsg(object1);

				AjaxResult ajax = AjaxResult.success("提交成功");
				return ajax;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		AjaxResult ajax = AjaxResult.error("提交失败");
		return ajax;
	}

	@PostMapping("/create")
	public AjaxResult createMeet(@RequestParam("creatorName") String creatorName,
								 @RequestParam("creatorXzq") String creatorXzq,
								 @RequestParam("projectXzq") String projectXzq,
								 @RequestParam("latitude") String latitude,
								 @RequestParam("longitude") String longitude,
								 @RequestParam("projectName") String projectName,
								 @RequestParam("dataPatrolTaskId") String dataPatrolTaskId,
								 @RequestParam("landid") String landid,
								 @RequestParam("mtNo") String mtNo,
								 @RequestParam("meetLongId") String meetLongId,
								 @RequestParam("createPhone") String createPhone,
								 @RequestParam("pnones") String pnones) {
		if (StringUtils.isNotBlank(creatorName)
			&& StringUtils.isNotBlank(createPhone)
			&& StringUtils.isNotBlank(mtNo)) {
			try {
				String createPhone33 = Des.decrypt(createPhone, "fl23h6h9");
				String mtNo33 = Des.decrypt(mtNo, "f1f3h6h9");
				String meetLongId33 = Des.decrypt(meetLongId, "f1f3h6h9");
				String pnones33 = "";
				if (StringUtils.isNotBlank(pnones)) {
					pnones33 = Des.decrypt(pnones, "fl23hdh9");
					List<String> list = JSON.parseObject(pnones33, new TypeReference<List<String>>() {
					});

					MeetVo meetVo = new MeetVo();
					meetVo.setCreatorName(creatorName);
					meetVo.setCreatorXzq(creatorXzq);
					meetVo.setProjectXzq(projectXzq);
					meetVo.setLatitude(StrConvert.toDouble(latitude));
					meetVo.setLongitude(StrConvert.toDouble(longitude));
					meetVo.setProjectName(StrConvert.toStrTrim(projectName));
					meetVo.setPatrolTaskId(dataPatrolTaskId);
					meetVo.setLandid( landid );
					meetVo.setMtNo(mtNo33);
					meetVo.setMeetLongId(meetLongId33);
					meetVo.setCreatePhone(createPhone33);
					meetVo.setMeetingUuid(IdUtil.simpleUUID());

					meetVo.setCreateTime(System.currentTimeMillis());

					List<MeetuserVo> meetuserVoList = new ArrayList<>();
					for (String urPhone :
						list) {
						MeetuserVo meetuserVo = new MeetuserVo();
						meetuserVo.setPhone(urPhone);
						meetuserVo.setStatus("wait");

						meetuserVoList.add(meetuserVo);
					}
					meetVo.setPnoneUsers(meetuserVoList);
					MeetStatusManager.addMeet(meetVo);
					AjaxResult ajax = AjaxResult.success("提交成功");
					return ajax;
				} else {
					List<MeetuserVo> list = new ArrayList<>();
					MeetVo meetVo = new MeetVo();
					meetVo.setCreatorName(creatorName);
					meetVo.setCreatorXzq(creatorXzq);
					meetVo.setProjectXzq(projectXzq);
					meetVo.setLatitude(StrConvert.toDouble(latitude));
					meetVo.setLongitude(StrConvert.toDouble(longitude));
					meetVo.setProjectName(StrConvert.toStrTrim(projectName));
					meetVo.setPatrolTaskId(dataPatrolTaskId);
					meetVo.setLandid( landid );
					meetVo.setMtNo(mtNo33);
					meetVo.setMeetLongId(meetLongId33);
					meetVo.setCreatePhone(createPhone33);
					meetVo.setMeetingUuid(IdUtil.simpleUUID());

					meetVo.setCreateTime(System.currentTimeMillis());
					meetVo.setPnoneUsers(list);
					MeetStatusManager.addMeet(meetVo);
					AjaxResult ajax = AjaxResult.success("提交成功");
					return ajax;
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		AjaxResult ajax = AjaxResult.error("提交失败");
		return ajax;
	}
}
