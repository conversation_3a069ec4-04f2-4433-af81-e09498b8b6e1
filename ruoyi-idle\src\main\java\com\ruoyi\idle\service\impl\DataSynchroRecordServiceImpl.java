package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.idle.domain.DataSynchroRecord;
import com.ruoyi.idle.mapper.DataSynchroRecordMapper;
import com.ruoyi.idle.service.IDataSynchroRecordService;
import org.springframework.stereotype.Service;

/**
 * @Created with IntelliJ IDEA.
 * @Title: DataSynchroRecordServiceImpl
 * @Description: com.ruoyi.idle.service.impl
 * @Author: HongDeng
 * @Date: 2021/9/13 9:26
 * @Version: 1.0.0
 **/
@Service
public class DataSynchroRecordServiceImpl extends ServicePlusImpl<DataSynchroRecordMapper, DataSynchroRecord> implements IDataSynchroRecordService {

	@Override
	public TableDataInfo<DataSynchroRecord> queryPageList(DataSynchroRecord bo) {
		PagePlus<DataSynchroRecord, DataSynchroRecord> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataSynchroRecord.class);
		return PageUtils.buildDataInfo(result);
	}

	private LambdaQueryWrapper<DataSynchroRecord> buildQueryWrapper(DataSynchroRecord bo) {
		LambdaQueryWrapper<DataSynchroRecord> lqw = Wrappers.lambdaQuery();
		lqw.like(StrUtil.isNotBlank(bo.getFileName()), DataSynchroRecord::getFileName, bo.getFileName());
		lqw.like(StrUtil.isNotBlank(bo.getSynchroType()), DataSynchroRecord::getSynchroType, bo.getSynchroType());
		lqw.eq(StrUtil.isNotBlank(bo.getErrorMsg()), DataSynchroRecord::getErrorMsg, bo.getErrorMsg());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataSynchroRecord::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataSynchroRecord::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataSynchroRecord::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataSynchroRecord::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataSynchroRecord::getUpdateTime, bo.getUpdateTime());
		lqw.orderByDesc(DataSynchroRecord :: getCreateTime);
		return lqw;
	}
}
