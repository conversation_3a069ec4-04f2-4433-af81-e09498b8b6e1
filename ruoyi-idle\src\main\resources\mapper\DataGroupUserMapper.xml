<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataGroupUserMapper">

    <resultMap type="com.ruoyi.idle.domain.DataGroupUser" id="DataGroupUserResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getUserIdsByGroupId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT user_id FROM `data_group_user` where group_id = #{groupId}
    </select>


</mapper>
