package com.ruoyi.idle.domain.bo;

import com.ruoyi.idle.domain.DataMeetingRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会议记录 分页查询对象 data_meeting_record
 *
 * <AUTHOR>
 * @date 2021-07-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("会议记录 分页查询对象")
public class DataMeetingRecordQueryBo extends DataMeetingRecord {

	/**
	 * 分页大小
	 */
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	/**
	 * 当前页数
	 */
	@ApiModelProperty("当前页数")
	private Integer pageNum;
	/**
	 * 排序列
	 */
	@ApiModelProperty("排序列")
	private String orderByColumn;
	/**
	 * 排序的方向desc或者asc
	 */
	@ApiModelProperty(value = "排序的方向", example = "asc,desc")
	private String isAsc;

}
