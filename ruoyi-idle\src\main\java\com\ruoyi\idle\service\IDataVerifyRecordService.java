package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
        import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataVerifyRecord;
import com.ruoyi.idle.domain.bo.DataVerifyRecordQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 核查记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-11-17
 */
public interface IDataVerifyRecordService extends IServicePlus<DataVerifyRecord> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataVerifyRecord queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataVerifyRecord> queryPageList(DataVerifyRecordQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataVerifyRecord> queryList(DataVerifyRecordQueryBo bo);

	/**
	 * 根据新增业务对象插入核查记录
	 *
	 * @param bo 核查记录 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataVerifyRecord bo);

	/**
	 * 根据编辑业务对象修改核查记录
	 *
	 * @param bo 核查记录 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataVerifyRecord bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

//	/**
//	 * 根据新增业务对象插入核查记录
//	 * @param bo 核查记录 新增业务对象
//	 * @return
//	 */
//	Boolean insertByAddBo(DataVerifyRecordAddBo bo);
//
//	/**
//	 * 根据编辑业务对象修改核查记录
//	 * @param bo 核查记录 编辑业务对象
//	 * @return
//	 */
//	Boolean updateByEditBo(DataVerifyRecordEditBo bo);
}
