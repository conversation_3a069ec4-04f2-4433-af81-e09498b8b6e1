package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataAuditRecord;
import com.ruoyi.idle.domain.bo.DataAuditRecordQueryBo;
import com.ruoyi.idle.mapper.DataAuditRecordMapper;
import com.ruoyi.idle.service.IDataAuditRecordService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 审核记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
@Service
public class DataAuditRecordServiceImpl extends ServicePlusImpl<DataAuditRecordMapper, DataAuditRecord> implements IDataAuditRecordService {

	@Override
	public DataAuditRecord queryById(String id) {
		return getVoById(id, DataAuditRecord.class);
	}

	@Override
	public TableDataInfo<DataAuditRecord> queryPageList(DataAuditRecordQueryBo bo) {
		PagePlus<DataAuditRecord, DataAuditRecord> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataAuditRecord.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataAuditRecord> queryList(DataAuditRecordQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataAuditRecord.class);
	}

	private LambdaQueryWrapper<DataAuditRecord> buildQueryWrapper(DataAuditRecordQueryBo bo) {
		LambdaQueryWrapper<DataAuditRecord> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getTaskId() != null, DataAuditRecord::getTaskId, bo.getTaskId());
		lqw.eq(StrUtil.isNotBlank(bo.getIsPass()), DataAuditRecord::getIsPass, bo.getIsPass());
		lqw.eq(StrUtil.isNotBlank(bo.getFailReason()), DataAuditRecord::getFailReason, bo.getFailReason());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataAuditRecord::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataAuditRecord::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataAuditRecord::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataAuditRecord::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataAuditRecord::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataAuditRecord::getUpdateTime, bo.getUpdateTime());
		lqw.orderByAsc(DataAuditRecord :: getCreateTime);
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataAuditRecord bo) {
		return save(bo);
	}

	@Override
	public Boolean updateByEditBo(DataAuditRecord bo) {
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataAuditRecord entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
