package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 闲置土地核查记录 对象 data_idle_verify
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_idle_verify")
public class DataIdleVerify implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;

	/**
	 * 闲置土地编号
	 */
	@ApiModelProperty(value = "闲置土地编号")
	private String idleId;

	/**
	 * 县区代码
	 */
	@ApiModelProperty(value = "县区代码")
	@Excel(name = "县区代码")
	private String countyCode;

	/**
	 * 县区名称
	 */
	@ApiModelProperty(value = "县区名称")
	@Excel(name = "县区名称")
	private String countyName;

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	@Excel(name = "项目名称")
	private String projectName;

	/**
	 * 项目年度
	 */
	@ApiModelProperty(value = "项目年度")
	@Excel(name = "项目年度")
	private String year;

	/**
	 * 项目季度
	 */
	@ApiModelProperty(value = "项目季度")
	@Excel(name = "项目季度")
	private String quarter;

	/**
	 * 土地坐落
	 */
	@ApiModelProperty(value = "土地坐落")
	@Excel(name = "土地坐落")
	private String address;

	/**
	 * 供应方式
	 */
	@ApiModelProperty(value = "供应方式")
	@Excel(name = "供应方式")
	private String supplyType;

	/**
	 * 电子监管号
	 */
	@ApiModelProperty(value = "电子监管号")
	@Excel(name = "电子监管号")
	private String supervisionNo;

	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	@Excel(name = "合同编号")
	private String contractNo;

	/**
	 * 宗地号
	 */
	@ApiModelProperty(value = "宗地号")
	@Excel(name = "宗地号")
	private String parcelNum;

	/**
	 * 供应面积
	 */
	@ApiModelProperty(value = "供应面积")
	@Excel(name = "供应面积")
	private BigDecimal landArea;

	/**
	 * 土地用途
	 */
	@ApiModelProperty(value = "土地用途")
	@Excel(name = "土地用途")
	private String landUse;

	/**
	 * 不动产权证号
	 */
	@ApiModelProperty(value = "不动产权证号")
	@Excel(name = "不动产权证号")
	private String warrantNum;

	/**
	 * 处置方式（0收回 1置换 2 动工开发）
	 */
	@ApiModelProperty(value = "处置方式")
	private String handleType;

	/**
	 * 是否有收回土地协议
	 */
	@ApiModelProperty(value = "是否有收回土地协议")
	private String hasReturnPact;

	/**
	 * 是否有置换土地协议
	 */
	@ApiModelProperty(value = "是否有置换土地协议")
	private String hasReplacePact;

	/**
	 * 是否有注销土地登记资料
	 */
	@ApiModelProperty(value = "是否有注销土地登记资料")
	private String hasRevokeData;

	/**
	 * 是否有置换土地登记资料
	 */
	@ApiModelProperty(value = "是否有置换土地登记资料")
	private String hasReplaceData;

	/**
	 * 是否有收回补偿费支付凭证
	 */
	@ApiModelProperty(value = "是否有收回补偿费支付凭证")
	private String hasPayProof;

	/**
	 * 是否有是否有置换新供地资料
	 */
	@ApiModelProperty(value = "是否有是否有置换新供地资料")
	private String hasNewSupplyData;

	/**
	 * 是否有施工许可证或动工证明材料
	 */
	@ApiModelProperty(value = "是否有施工许可证或动工证明材料")
	private String hasStartData;

	/**
	 * 是否有动工开发现场照片
	 */
	@ApiModelProperty(value = "是否有动工开发现场照片")
	private String hasStartPhoto;

	/**
	 * 视频连线情况是否属实
	 */
	@ApiModelProperty(value = "视频连线情况是否属实")
	private String videoIsReal;

	/**
	 * 实地核查情况是否属实
	 */
	@ApiModelProperty(value = "实地核查情况是否属实")
	private String siteIsReal;

	/**
	 * 处置是否合规
	 */
	@ApiModelProperty(value = "处置是否合规")
	private String handleIsCorrect;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
