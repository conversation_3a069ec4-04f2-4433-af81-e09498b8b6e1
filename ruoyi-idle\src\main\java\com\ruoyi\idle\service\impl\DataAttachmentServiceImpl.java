package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataAttachment;
import com.ruoyi.idle.domain.bo.DataAttachmentQueryBo;
import com.ruoyi.idle.mapper.DataAttachmentMapper;
import com.ruoyi.idle.service.IDataAttachmentService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Collection;

/**
 * 巡查附件 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataAttachmentServiceImpl extends ServicePlusImpl<DataAttachmentMapper, DataAttachment> implements IDataAttachmentService {

	@Override
	public DataAttachment queryById(String id) {
		return getVoById(id, DataAttachment.class);
	}

	@Override
	public TableDataInfo<DataAttachment> queryPageList(DataAttachmentQueryBo bo) {
		PagePlus<DataAttachment, DataAttachment> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataAttachment.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataAttachment> queryList(DataAttachmentQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataAttachment.class);
	}

	private LambdaQueryWrapper<DataAttachment> buildQueryWrapper(DataAttachmentQueryBo bo) {
		LambdaQueryWrapper<DataAttachment> lqw = Wrappers.lambdaQuery();
		lqw.like(StrUtil.isNotBlank(bo.getName()), DataAttachment::getName, bo.getName());
		lqw.eq(StrUtil.isNotBlank(bo.getSize()), DataAttachment::getSize, bo.getSize());
		lqw.eq(StrUtil.isNotBlank(bo.getType()), DataAttachment::getType, bo.getType());
		lqw.eq(StrUtil.isNotBlank(bo.getUrl()), DataAttachment::getUrl, bo.getUrl());
		lqw.eq(StrUtil.isNotBlank(bo.getLocation()), DataAttachment::getLocation, bo.getLocation());
		lqw.eq(bo.getPatrolTaskId() != null, DataAttachment::getPatrolTaskId, bo.getPatrolTaskId());
		lqw.eq(bo.getCatalogId() != null, DataAttachment::getCatalogId, bo.getCatalogId());
		lqw.eq(StrUtil.isNotBlank(bo.getIsSiteData()), DataAttachment::getIsSiteData, bo.getIsSiteData());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataAttachment::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataAttachment::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataAttachment::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataAttachment::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataAttachment::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataAttachment::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insert(DataAttachment bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataAttachment bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataAttachment entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public int realDeleteRecord(String fileName, String taskId) {
		return baseMapper.realDeleteRecord(fileName, taskId);
	}
}
