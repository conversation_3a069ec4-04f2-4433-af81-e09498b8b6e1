package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataUnprovidedVerify;
import com.ruoyi.idle.domain.bo.DataUnprovidedVerifyQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;
import com.ruoyi.idle.mapper.DataUnprovidedVerifyMapper;
import com.ruoyi.idle.service.IDataUnprovidedVerifyService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 批而未供核查记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Service
public class DataUnprovidedVerifyServiceImpl extends ServicePlusImpl<DataUnprovidedVerifyMapper, DataUnprovidedVerify> implements IDataUnprovidedVerifyService {

	@Override
	public DataUnprovidedVerify queryById(String id) {
		return getVoById(id, DataUnprovidedVerify.class);
	}

	@Override
	public TableDataInfo<DataUnprovidedVerify> queryPageList(DataUnprovidedVerifyQueryBo bo) {
		PagePlus<DataUnprovidedVerify, DataUnprovidedVerify> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataUnprovidedVerify.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataUnprovidedVerify> queryList(DataUnprovidedVerifyQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataUnprovidedVerify.class);
	}

	private LambdaQueryWrapper<DataUnprovidedVerify> buildQueryWrapper(DataUnprovidedVerifyQueryBo bo) {
		LambdaQueryWrapper<DataUnprovidedVerify> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getTaskId() != null, DataUnprovidedVerify::getTaskId, bo.getTaskId());
		lqw.eq(bo.getUnprovidedId() != null, DataUnprovidedVerify::getUnprovidedId, bo.getUnprovidedId());
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataUnprovidedVerify::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataUnprovidedVerify::getCountyName, bo.getCountyName());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataUnprovidedVerify::getProjectName, bo.getProjectName());
		lqw.eq(StrUtil.isNotBlank(bo.getYear()), DataUnprovidedVerify::getYear, bo.getYear());
		lqw.eq(StrUtil.isNotBlank(bo.getQuarter()), DataUnprovidedVerify::getQuarter, bo.getQuarter());
		lqw.eq(StrUtil.isNotBlank(bo.getAddress()), DataUnprovidedVerify::getAddress, bo.getAddress());
		lqw.eq(StrUtil.isNotBlank(bo.getSupplyType()), DataUnprovidedVerify::getSupplyType, bo.getSupplyType());
		lqw.eq(StrUtil.isNotBlank(bo.getSupervisionNo()), DataUnprovidedVerify::getSupervisionNo, bo.getSupervisionNo());
		lqw.eq(StrUtil.isNotBlank(bo.getContractNo()), DataUnprovidedVerify::getContractNo, bo.getContractNo());
		lqw.like(StrUtil.isNotBlank(bo.getContractName()), DataUnprovidedVerify::getContractName, bo.getContractName());
		lqw.eq(StrUtil.isNotBlank(bo.getParcelNum()), DataUnprovidedVerify::getParcelNum, bo.getParcelNum());
		lqw.eq(bo.getLandArea() != null, DataUnprovidedVerify::getLandArea, bo.getLandArea());
		lqw.eq(StrUtil.isNotBlank(bo.getLandUse()), DataUnprovidedVerify::getLandUse, bo.getLandUse());
		lqw.eq(StrUtil.isNotBlank(bo.getHasProjectDoc()), DataUnprovidedVerify::getHasProjectDoc, bo.getHasProjectDoc());
		lqw.eq(StrUtil.isNotBlank(bo.getHasApprovalPlan()), DataUnprovidedVerify::getHasApprovalPlan, bo.getHasApprovalPlan());
		lqw.eq(StrUtil.isNotBlank(bo.getHasPlanCondition()), DataUnprovidedVerify::getHasPlanCondition, bo.getHasPlanCondition());
		lqw.eq(StrUtil.isNotBlank(bo.getHasSupplyPost()), DataUnprovidedVerify::getHasSupplyPost, bo.getHasSupplyPost());
		lqw.eq(StrUtil.isNotBlank(bo.getHasResultPublicity()), DataUnprovidedVerify::getHasResultPublicity, bo.getHasResultPublicity());
		lqw.eq(StrUtil.isNotBlank(bo.getHasDealBook()), DataUnprovidedVerify::getHasDealBook, bo.getHasDealBook());
		lqw.eq(StrUtil.isNotBlank(bo.getHandleIsCorrect()), DataUnprovidedVerify::getHandleIsCorrect, bo.getHandleIsCorrect());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataUnprovidedVerify::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataUnprovidedVerify::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataUnprovidedVerify::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataUnprovidedVerify::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataUnprovidedVerify::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataUnprovidedVerify::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataUnprovidedVerify bo) {
		return save(bo);
	}

	@Override
	public Boolean updateByEditBo(DataUnprovidedVerify bo) {
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataUnprovidedVerify entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
