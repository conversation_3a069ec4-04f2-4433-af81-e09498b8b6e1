package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataUnprovidedVerify;
import com.ruoyi.idle.domain.bo.DataUnprovidedVerifyQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;

import java.util.Collection;
import java.util.List;

/**
 * 批而未供核查记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface IDataUnprovidedVerifyService extends IServicePlus<DataUnprovidedVerify> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataUnprovidedVerify queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataUnprovidedVerify> queryPageList(DataUnprovidedVerifyQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataUnprovidedVerify> queryList(DataUnprovidedVerifyQueryBo bo);

	/**
	 * 根据新增业务对象插入批而未供核查记录
	 *
	 * @param bo 批而未供核查记录 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataUnprovidedVerify bo);

	/**
	 * 根据编辑业务对象修改批而未供核查记录
	 *
	 * @param bo 批而未供核查记录 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataUnprovidedVerify bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);


}
