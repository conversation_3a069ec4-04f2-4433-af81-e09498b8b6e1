package com.ruoyi.idle.domain.bo;

import com.ruoyi.idle.domain.DataDigestType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 消化类型对应关系 分页查询对象 data_digest_type
 *
 * <AUTHOR>
 * @date 2021-11-16
 */

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("消化类型对应关系 分页查询对象")
    public class DataDigestTypeQueryBo extends DataDigestType {

	/**
	 * 分页大小
	 */
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	/**
	 * 当前页数
	 */
	@ApiModelProperty("当前页数")
	private Integer pageNum;
	/**
	 * 排序列
	 */
	@ApiModelProperty("排序列")
	private String orderByColumn;
	/**
	 * 排序的方向desc或者asc
	 */
	@ApiModelProperty(value = "排序的方向", example = "asc,desc")
	private String isAsc;


}
