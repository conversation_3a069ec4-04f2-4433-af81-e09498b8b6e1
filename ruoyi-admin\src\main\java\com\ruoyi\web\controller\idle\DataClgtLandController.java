package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.ruoyi.idle.domain.DataClgtLand;
import com.ruoyi.idle.domain.bo.DataClgtLandQueryBo;
import com.ruoyi.idle.domain.vo.DataClgtLandVo;
import com.ruoyi.idle.service.IDataClgtLandService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

/**
 * 闲置土地 Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Api(value = "闲置土地 控制器", tags = {"闲置土地 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/clgt")
public class DataClgtLandController extends BaseController {

	private final IDataClgtLandService iDataClgtLandService;

	/**
	 * 查询闲置土地 列表
	 */
	@ApiOperation("查询闲置土地 列表")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:list')")
	@GetMapping("/list")
	public TableDataInfo<DataClgtLandVo> list(@Validated DataClgtLandQueryBo bo) {
		return iDataClgtLandService.queryPageList(bo);
	}

	/**
	 * 导出闲置土地 列表
	 */
	@ApiOperation("导出闲置土地 列表")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:export')")
	@Log(title = "闲置土地 ", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataClgtLandVo> export(@Validated DataClgtLandQueryBo bo) {
		List<DataClgtLandVo> list = iDataClgtLandService.queryList(bo);
		ExcelUtil<DataClgtLandVo> util = new ExcelUtil<DataClgtLandVo>(DataClgtLandVo.class);
		return util.exportExcel(list, "闲置土地 ");
	}

	/**
	 * 获取闲置土地 详细信息
	 */
	@ApiOperation("获取闲置土地 详细信息1")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataClgtLand> getInfo(@NotNull(message = "主键不能为空")
											  @PathVariable("id") String id) {
		return AjaxResult.success(iDataClgtLandService.queryById(id));
	}

	/**
	 * 新增闲置土地
	 */
	@ApiOperation("新增闲置土地 ")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:add')")
	@Log(title = "闲置土地 ", businessType = BusinessType.INSERT)
	@RepeatSubmit
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataClgtLand bo) {
		return toAjax(iDataClgtLandService.insertByAddBo(bo) ? 1 : 0);
	}

	/**
	 * 修改闲置土地
	 */
	@ApiOperation("修改闲置土地 ")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:edit')")
	@Log(title = "闲置土地 ", businessType = BusinessType.UPDATE)
	@RepeatSubmit
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataClgtLand bo) {
		return toAjax(iDataClgtLandService.updateByEditBo(bo) ? 1 : 0);
	}

	/**
	 * 删除闲置土地
	 */
	@ApiOperation("删除闲置土地 ")
//	@PreAuthorize("@ss.hasPermi('idle:clgt:remove')")
	@Log(title = "闲置土地 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataClgtLandService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}


	/**
	 * 导入数据
	 */
	@PostMapping("/import")
	@ApiOperation("导入国土空间数据 ")
	@Transactional
	@ApiImplicitParams({
		@ApiImplicitParam(name = "file", value = "上传的文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true),
	})
	public AjaxResult<Void> importData(@RequestPart(value = "file") MultipartFile file) throws Exception {
		return toAjax(iDataClgtLandService.importData(file));
	}
}
