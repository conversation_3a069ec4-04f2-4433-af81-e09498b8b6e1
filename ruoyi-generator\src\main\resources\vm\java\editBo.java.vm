package ${packageName}.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import javax.validation.constraints.*;

#foreach ($import in $importList)
import ${import};
#end

/**
 * ${functionName}编辑对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@ApiModel("${functionName}编辑对象")
public class ${ClassName}EditBo {

    #foreach ($column in $columns)
        #if($column.isEdit || $column.isPk==1)

            /** $column.columnComment */
            @ApiModelProperty("$column.columnComment")
                #if($column.isRequired==1)
                    #if($column.javaType == 'String')
                    @NotBlank(message = "$column.columnComment不能为空")
                    #else
                    @NotNull(message = "$column.columnComment不能为空")
                    #end
                #end
            private $column.javaType $column.javaField;
        #end
    #end
    #if($table.sub)

        /** $table.subTable.functionName信息 */
        @ApiModelProperty("$table.subTable.functionName")
        private List<${subClassName}> ${subclassName}List;
    #end
}
