package com.ruoyi.idle.domain.vo;

import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.DataPatrolTask;
import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: ProjectPackageVerifyVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/12/2 14:33
 * @Version: 1.0.0
 **/
@Data
public class ProjectPackageVerifyVo {
	/**
	 * 是否验证成功
	 */
	Boolean isSuccess = false;
	/**
	 * 验证错误信息
	 */
	String errorMsg;
	/**
	 * 巡查任务
	 */
	DataPatrolTask patrolTask;
	/**
	 * 文件目录列表
	 */
	List<DataFileCatalog> catalogList;
	/**
	 * 电子监管号
	 */
	String supervisionNo;

}
