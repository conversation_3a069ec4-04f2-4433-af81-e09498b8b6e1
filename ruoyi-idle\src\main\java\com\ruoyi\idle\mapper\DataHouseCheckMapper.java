package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataHouseCheck;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataHouseCheckExportVo;
import com.ruoyi.idle.domain.vo.DataHouseCheckVo;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 房屋建筑信息外业调查 Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
//@CacheNamespace(implementation = MybatisPlusRedisCache.class, eviction = MybatisPlusRedisCache.class)
public interface DataHouseCheckMapper extends BaseMapperPlus<DataHouseCheck> {
	DataHouseCheck selectByClgtId(@Param("clgtId") String clgtId);

	DataHouseCheck selectByTaskId(@Param("taskId") String taskId);
//	@Select("SELECT h.* from data_house_check h \n" +
//		"INNER JOIN data_patrol_task t on t.id =h.task_id \n" +
//		"INNER JOIN data_task_user u on u.task_id=t.id \n" +
//		"where u.user_id=#{userId} \n" +
//		"and t.publish_status = '1' and t.is_patrol = '0' and t.region_code=#{regionCode}")

	@Select("SELECT h.* from data_house_check h \n" +
		"INNER JOIN data_patrol_task t on t.id =h.task_id \n" +
		"where t.publish_status = '1' and t.is_patrol = '0' and t.region_code=#{regionCode}")
	List<DataHouseCheck> appPulList(@Param("regionCode") String regionCode, @Param("userId") String userId);


	List<DataHouseCheckExportVo> queryHouseList(@Param("bo") DataPatrolTaskQueryBo bo);
}
