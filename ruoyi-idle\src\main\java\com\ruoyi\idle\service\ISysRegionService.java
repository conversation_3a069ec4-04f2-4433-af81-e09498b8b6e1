package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.SysRegionQueryBo;
import com.ruoyi.idle.domain.vo.RegionTreeVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 中国行政区Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface ISysRegionService extends IServicePlus<SysRegion> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	SysRegion queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<SysRegion> queryPageList(SysRegionQueryBo bo);

	/**
	 * 查询列表
	 */
	List<SysRegion> queryList(SysRegionQueryBo bo);

	/**
	 * 获取子对象列表
	 */
	List<String> queryChildList(String regionCode);

	/**
	 * 根据新增业务对象插入中国行政区
	 *
	 * @param bo 中国行政区新增业务对象
	 * @return
	 */
	Boolean insert(SysRegion bo);

	/**
	 * 根据编辑业务对象修改中国行政区
	 *
	 * @param bo 中国行政区编辑业务对象
	 * @return
	 */
	Boolean update(SysRegion bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	String getNameByCode(String code);

	List<RegionTreeVo> getRegionTreeAsync(String pId);

	Boolean exportJsonFile(HttpServletResponse response);
}
