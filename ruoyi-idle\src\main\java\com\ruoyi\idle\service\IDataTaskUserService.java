package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataTaskUser;
import com.ruoyi.idle.domain.bo.DataTaskUserQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 巡查任务用户关系 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataTaskUserService extends IServicePlus<DataTaskUser> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataTaskUser queryById(String id);

	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataTaskUser queryByTaskIdAndUserId(String taskId, String userId);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataTaskUser> queryPageList(DataTaskUserQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataTaskUser> queryList(DataTaskUserQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查任务用户关系
	 *
	 * @param bo 巡查任务用户关系 新增业务对象
	 * @return
	 */
	Boolean insert(DataTaskUser bo);

	/**
	 * 根据编辑业务对象修改巡查任务用户关系
	 *
	 * @param bo 巡查任务用户关系 编辑业务对象
	 * @return
	 */
	Boolean update(DataTaskUser bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
