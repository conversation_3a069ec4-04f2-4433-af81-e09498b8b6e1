package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataPatrolGroup;
import com.ruoyi.idle.domain.DataPatrolTask;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查组 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface DataPatrolGroupMapper extends BaseMapperPlus<DataPatrolGroup> {

	List<DataPatrolGroup> selectAllPatrolGroupList(@Param("startTime") String startTime);

}
