package com.ruoyi.idle.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.DataPatrolTask;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.DataIdleLandQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;
import com.ruoyi.idle.mapper.DataIdleLandMapper;
import com.ruoyi.idle.mapper.DataLandNotProvidedMapper;
import com.ruoyi.idle.mapper.DataPatrolTaskMapper;
import com.ruoyi.idle.mapper.SysRegionMapper;
import com.ruoyi.idle.service.IDataIdleLandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.token.TokenService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.*;

/**
 * 闲置土地 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DataIdleLandServiceImpl extends ServicePlusImpl<DataIdleLandMapper, DataIdleLand> implements IDataIdleLandService {

	private final DataIdleLandMapper idleLandMapper;

	private final DataLandNotProvidedMapper notProvidedMapper;

	private final DataPatrolTaskMapper taskMapper;

	private final SysRegionMapper regionMapper;

	@Override
	public List<DataIdleLand> appPulList(  String regionCode,   String userId){
		return idleLandMapper.appPulList(regionCode,userId);
	}

	@Override
	public DataIdleLand queryById(String id) {
		return getVoById(id, DataIdleLand.class);
	}

	@Override
	public TableDataInfo<DataIdleLand> queryPageList(DataIdleLandQueryBo bo) {
		PagePlus<DataIdleLand, DataIdleLand> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataIdleLand.class);
		List<DataIdleLand> dataIdleLands = result.getRecordsVo();
		if (dataIdleLands.size() != 0){
			for (DataIdleLand idleLand : dataIdleLands){
				// 查询对应的任务状态
				DataPatrolTask task = taskMapper.selectOne(new LambdaQueryWrapper<DataPatrolTask>()
				.eq(DataPatrolTask ::getIdleLandId,idleLand.getId()));
				if (task != null){
					idleLand.setIsPatrol(task.getIsPatrol());
				}
			}
		}
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataIdleLand> queryList(DataIdleLandQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataIdleLand.class);
	}

	private LambdaQueryWrapper<DataIdleLand> buildQueryWrapper(DataIdleLandQueryBo bo) {
		LambdaQueryWrapper<DataIdleLand> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getCity()), DataIdleLand::getCity, bo.getCity());
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataIdleLand::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataIdleLand::getCountyName, bo.getCountyName());
		lqw.like(StrUtil.isNotBlank(bo.getContractNo()), DataIdleLand::getContractNo, bo.getContractNo());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataIdleLand::getProjectName, bo.getProjectName());
		lqw.eq(StrUtil.isNotBlank(bo.getYear()), DataIdleLand::getYear, bo.getYear());
		lqw.eq(StrUtil.isNotBlank(bo.getQuarter()), DataIdleLand::getQuarter, bo.getQuarter());
		lqw.like(StrUtil.isNotBlank(bo.getSupervisionNo()), DataIdleLand::getSupervisionNo, bo.getSupervisionNo());
		lqw.like(StrUtil.isNotBlank(bo.getLandUse()), DataIdleLand::getLandUse, bo.getLandUse());
		lqw.eq(bo.getLandArea() != null, DataIdleLand::getLandArea, bo.getLandArea());
		lqw.eq(bo.getSignDate() != null, DataIdleLand::getSignDate, bo.getSignDate());
		lqw.eq(bo.getAgreedStartTime() != null, DataIdleLand::getAgreedStartTime, bo.getAgreedStartTime());
		lqw.eq(bo.getActualStartTime() != null, DataIdleLand::getActualStartTime, bo.getActualStartTime());
		lqw.eq(bo.getActualEndTime() != null, DataIdleLand::getActualEndTime, bo.getActualEndTime());
		lqw.eq(StrUtil.isNotBlank(bo.getIdleStatus()), DataIdleLand::getIdleStatus, bo.getIdleStatus());
		lqw.like(StrUtil.isNotBlank(bo.getSpotNumber()), DataIdleLand::getSpotNumber, bo.getSpotNumber());
		lqw.eq(StrUtil.isNotBlank(bo.getInsideRemark()), DataIdleLand::getInsideRemark, bo.getInsideRemark());
		lqw.eq(StrUtil.isNotBlank(bo.getDigestionType()), DataIdleLand::getDigestionType, bo.getDigestionType());
		lqw.like(StrUtil.isNotBlank(bo.getAddress()), DataIdleLand::getAddress, bo.getAddress());
		lqw.eq(StrUtil.isNotBlank(bo.getGeoData()), DataIdleLand::getGeoData, bo.getGeoData());
		lqw.eq(StrUtil.isNotBlank(bo.getLongitude()), DataIdleLand::getLongitude, bo.getLongitude());
		lqw.eq(StrUtil.isNotBlank(bo.getLatitude()), DataIdleLand::getLatitude, bo.getLatitude());
		lqw.eq(StrUtil.isNotBlank(bo.getIsAllot()), DataIdleLand::getIsAllot, bo.getIsAllot());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataIdleLand::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataIdleLand::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataIdleLand::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataIdleLand::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataIdleLand::getUpdateTime, bo.getUpdateTime());
		lqw.eq(bo.getSupplyType() != null, DataIdleLand::getSupplyType, bo.getSupplyType());
		lqw.eq(bo.getParcelNum() != null, DataIdleLand::getParcelNum, bo.getParcelNum());
		lqw.eq(bo.getWarrantNum() != null, DataIdleLand::getWarrantNum, bo.getWarrantNum());
		// 根据是否已分配排序
		lqw.orderByAsc(DataIdleLand::getIsAllot);
		// 再根据创建时间排序
		lqw.orderByDesc(DataIdleLand::getCreateTime);
		return lqw;
	}

	@Override
	public Boolean insert(DataIdleLand bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataIdleLand bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataIdleLand entity) {
		//TODO 做一些数据校验,如唯一约束
		Date date = DateUtil.date();
		// 赋值默认的年份
		if (StringUtils.isBlank(entity.getYear())) entity.setYear(String.valueOf(DateUtil.year(date)));
		// 赋值默认的季度
		if (StringUtils.isBlank(entity.getQuarter())) entity.setQuarter(String.valueOf(DateUtil.quarter(date)));
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public String importIdleLand(List<DataIdleLand> dataList, boolean updateSupport, String operaName) {
		if (Validator.isNull(dataList) || dataList.size() == 0) {
			throw new CustomException("导入闲置土地数据不能为空！");
		}
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		for (DataIdleLand idleLand : dataList) {
			try {
				// 根据电子监管号验证是否存在这个闲置土地
				DataIdleLand selectIdle = idleLandMapper.selectDataBySupervisionNo(idleLand.getSupervisionNo());
				if (selectIdle == null) {
					// 设置该条记录的一些默认字段值
					idleLand.setIsAllot("0");
					idleLand.setDeleteFlag("0");
					idleLand.setCreateBy(operaName);
					// TODO 去给的矢量数据中获取该记录对应的空间数据并赋值
					idleLandMapper.insert(idleLand);

					// TODO 判断一下 看看是否需要直接将项目添加到巡查任务中

					successNum++;
					successMsg.append("<br/>" + successNum + "、项目 " + idleLand.getProjectName() + " 导入成功");

				} else if (updateSupport) {
					BeanUtils.copyProperties(idleLand, selectIdle);
					selectIdle.setUpdateBy(operaName);
					idleLandMapper.updateById(selectIdle);
					successNum++;
					successMsg.append("<br/>" + successNum + "、项目 " + idleLand.getProjectName() + " 更新成功");

				} else {
					failureNum++;
					failureMsg.append("<br/>" + failureNum + "、项目 " + idleLand.getProjectName() + " 已存在");
				}
			} catch (Exception e) {
				failureNum++;
				String msg = "<br/>" + failureNum + "、项目 " + idleLand.getProjectName() + " 导入失败：";
				failureMsg.append(msg + e.getMessage());
				log.error(msg, e);
			}
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new CustomException(failureMsg.toString());
		} else {
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}

	@Override
	public String getDigestionTypeByTaskId(String taskId) {
		return idleLandMapper.selectDigestionTypeByTaskId(taskId);
	}

	@Override
	public String selectSupervisionNoByTaskId(String taskId,String projectType) {
		if (projectType.equals("0")){
			return idleLandMapper.selectSupervisionNoByTaskId(taskId);
		}else{
			return notProvidedMapper.selectSupervisionNoByTaskId(taskId);
		}
	}

	@Override
	public List<StatisticsVo> idleStat(String regionCode) {
		List<StatisticsVo> voList = new ArrayList<>();
		if (StringUtils.isNotBlank(regionCode)){
			// 按照指定行政区过滤
			StatisticsVo vo = getStatisticsVo(regionCode);
			voList.add(vo);
		}else {
			// 所有行政区
			// 获取所有行政区列表并循环获取
			LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(SysRegion :: getLevel,"2");
			List<SysRegion> regionList = regionMapper.selectList(queryWrapper);
			for (SysRegion region : regionList){
				StatisticsVo vo = getStatisticsVo(region.getAreaCode());
				voList.add(vo);
			}
		}
		return voList;
	}

	@Override
	public List<DataIdleLand> getAllIdleLandList(String startTime) {
		return idleLandMapper.selectAllIdleLandList(startTime);
	}

	private Integer getTaskCount(String regionCode,String type){
		int count = 0;
		LambdaQueryWrapper<DataPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataPatrolTask :: getRegionCode,regionCode)
			.eq(DataPatrolTask :: getProjectType,"0")
			.eq(DataPatrolTask :: getIsPatrol,type);
		count = taskMapper.selectCount(queryWrapper);
		return count;
	}
	private Integer getIdleCount(String regionCode,String type){
		int count = 0;
		LambdaQueryWrapper<DataIdleLand> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataIdleLand :: getCountyCode,regionCode)
			.eq(StringUtils.isNotBlank(type),DataIdleLand :: getIsAllot,type);
		count = idleLandMapper.selectCount(queryWrapper);
		return count;
	}

	private StatisticsVo getStatisticsVo(String regionCode){
		StatisticsVo vo = new StatisticsVo();
		vo.setRegionCode(regionCode);
		LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysRegion :: getAreaCode,regionCode);
		SysRegion region = regionMapper.selectOne(queryWrapper);
		if (region != null)vo.setRegionName(region.getName());
		vo.setStatType("闲置土地");
		vo.setTotalCount(getIdleCount(regionCode,""));
		vo.setAllocatedCount(getIdleCount(regionCode,"1"));
		vo.setUnAllocatedCount(getIdleCount(regionCode,"0"));
		vo.setUnPatrolledCount(getTaskCount(regionCode,"0"));
		vo.setPatrolledCount(getTaskCount(regionCode,"1"));
		vo.setAuditCount(getTaskCount(regionCode,"2"));
		return vo;
	}

}
