package ${packageName}.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import ${packageName}.vo.${ClassName}Vo;
import ${packageName}.bo.${ClassName}QueryBo;
import ${packageName}.bo.${ClassName}AddBo;
import ${packageName}.bo.${ClassName}EditBo;
import ${packageName}.service.I${ClassName}Service;
import com.ruoyi.common.utils.poi.ExcelUtil;
    #if($table.crud || $table.sub)
    import com.ruoyi.common.core.page.TableDataInfo;
    #elseif($table.tree)
    #end
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(value = "${functionName}控制器", tags = {"${functionName}管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {

    private final I${ClassName}Service i${ClassName}Service;

/**
 * 查询${functionName}列表
 */
@ApiOperation("查询${functionName}列表")
@PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
@GetMapping("/list")
    #if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}Vo> list(@Validated ${ClassName}QueryBo bo) {
        return i${ClassName}Service.queryPageList(bo);
    }
    #elseif($table.tree)
        public AjaxResult<List<${ClassName}Vo>> list(@Validated ${ClassName}QueryBo bo) {
            List<${ClassName}Vo> list = i${ClassName}Service.queryList(bo);
            return AjaxResult.success(list);
        }
    #end

    /**
     * 导出${functionName}列表
     */
    @ApiOperation("导出${functionName}列表")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<${ClassName}Vo> export(@Validated ${ClassName}QueryBo bo) {
        List<${ClassName}Vo> list = i${ClassName}Service.queryList(bo);
        ExcelUtil<${ClassName}Vo> util = new ExcelUtil<${ClassName}Vo>(${ClassName}Vo.class);
        return util.exportExcel(list, "${functionName}");
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation("获取${functionName}详细信息")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping("/{${pkColumn.javaField}}")
    public AjaxResult<${ClassName}Vo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return AjaxResult.success(i${ClassName}Service.queryById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation("新增${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping()
    public AjaxResult<Void> add(@Validated @RequestBody ${ClassName}AddBo bo) {
        return toAjax(i${ClassName}Service.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation("修改${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping()
    public AjaxResult<Void> edit(@Validated @RequestBody ${ClassName}EditBo bo) {
        return toAjax(i${ClassName}Service.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation("删除${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
                                   @PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(i${ClassName}Service.deleteWithValidByIds(Arrays.asList(${pkColumn.javaField}s), true) ? 1 : 0);
    }
}
