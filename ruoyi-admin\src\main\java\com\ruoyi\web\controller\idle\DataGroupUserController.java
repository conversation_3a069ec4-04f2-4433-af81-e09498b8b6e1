package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.idle.domain.DataGroupUser;
import com.ruoyi.idle.domain.bo.DataGroupUserQueryBo;
import com.ruoyi.idle.service.IDataGroupUserService;
import com.ruoyi.system.domain.SysUserRole;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 巡查组用户关系 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查组用户关系控制器", tags = {"巡查组用户关系管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/group/user")
public class DataGroupUserController extends BaseController {

	private final IDataGroupUserService iDataGroupUserService;

	/**
	 * 查询巡查组用户关系 列表
	 */
	@ApiOperation("查询巡查组用户关系列表")
	@GetMapping("/list")
	public TableDataInfo<DataGroupUser> list(@Validated DataGroupUserQueryBo bo) {
		return iDataGroupUserService.queryPageList(bo);
	}


	/**
	 * 获取巡查组用户关系 详细信息
	 */
	@ApiOperation("获取巡查组用户关系详细信息")
	@GetMapping("/{id}")
	public AjaxResult<DataGroupUser> getInfo(@NotNull(message = "主键不能为空")
											 @PathVariable("id") String id) {
		return AjaxResult.success(iDataGroupUserService.queryById(id));
	}

	/**
	 * 新增巡查组用户关系
	 */
	@ApiOperation("新增巡查组用户关系")
	@Log(title = "巡查组用户关系", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult<Void> add(String groupId, String userIds) {
		if (StringUtils.isBlank(groupId)) return AjaxResult.error("巡查组编号不能为空");
		if (StringUtils.isBlank(userIds)) return AjaxResult.error("用户编号不能为空");
		if (userIds.contains(",")) {
			List<String> userIdList = Arrays.asList(userIds.split(","));
			if (userIdList.size() != 0) {
				// 循环添加关系
				for (String userId : userIdList) {
					DataGroupUser groupUser = new DataGroupUser();
					groupUser.setGroupId(groupId);
					groupUser.setUserId(userId);
					groupUser.setCreateBy(SecurityUtils.getUsername());
					iDataGroupUserService.save(groupUser);
				}
			}
		} else {
			DataGroupUser groupUser = new DataGroupUser();
			groupUser.setGroupId(groupId);
			groupUser.setUserId(userIds);
			groupUser.setCreateBy(SecurityUtils.getUsername());
			iDataGroupUserService.save(groupUser);
		}
		return AjaxResult.success("成功添加巡查组用户关系");
	}

	/**
	 * 修改巡查组用户关系
	 */
	@ApiOperation("修改巡查组用户关系")
	@Log(title = "巡查组用户关系", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataGroupUser bo) {
		bo.setCreateBy(SecurityUtils.getUsername());
		return toAjax(iDataGroupUserService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除巡查组用户关系
	 */
	@ApiOperation("删除巡查组用户关系")
	@Log(title = "巡查组用户关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/deleteGroupUser")
	public AjaxResult<Void> deleteGroupUser(String groupId, String userId) {
		LambdaQueryWrapper<DataGroupUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataGroupUser::getGroupId, groupId);
		queryWrapper.eq(DataGroupUser::getUserId, userId);
		return toAjax(iDataGroupUserService.remove(queryWrapper) ? 1 : 0);
	}
}
