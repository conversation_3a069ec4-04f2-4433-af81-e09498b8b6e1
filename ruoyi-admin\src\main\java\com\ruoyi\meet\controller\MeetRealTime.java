package com.ruoyi.meet.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.socket.WebSocket;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class MeetRealTime {

	private static int OnlineCount = 0;
	private static ConcurrentHashMap<String, RealTimeXy> rocketMap = new ConcurrentHashMap<>();

	public static void addUserXy(String phone, RealTimeXy xy){
		if (rocketMap.containsKey(phone)) {
			rocketMap.remove(phone);
			rocketMap.put(phone,xy);
		}else{
			rocketMap.put(phone,xy);
		}
	}

	public static List<JSONObject> getAll(){
		List<JSONObject> list=new ArrayList<>();

		for(String key : rocketMap.keySet()) {
			JSONObject jo =new JSONObject();
			System.out.println("key = " + key);
			RealTimeXy xy = rocketMap.get(key);

			jo.put("user",key);
			jo.put("name",xy.getName());
			jo.put("x",xy.getX());
			jo.put("y",xy.getY());
			jo.put("angle",xy.getAngle());
			list.add(jo);
		}
		return list;

	}

}
