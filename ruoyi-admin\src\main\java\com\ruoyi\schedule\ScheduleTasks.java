package com.ruoyi.schedule;

import com.ruoyi.socket.MeetStatusManager;
import com.ruoyi.socket.WebSocket;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@EnableScheduling
public class ScheduleTasks {

	//@Scheduled(fixedRate = 1000 * 15L)
	public void runIntervalTask() {
		WebSocket.sendAllMessage("six second");
	}

	@Scheduled(fixedRate = 1000 * 300L)
	public void runIntervalPingTask() {
		WebSocket.sendAllMessage("ping");
	}

	@Scheduled(fixedRate = 1000 * 15L)
	public void runingMeetingLongIdTask() {
		MeetStatusManager.sendAllUsersMeetRunnig();
	}
}
