package com.ruoyi.idle.domain.bo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 闲置土地 分页查询对象 data_clgt_land
 *
 * <AUTHOR>
 * @date 2025-04-17
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("闲置土地 分页查询对象")
public class DataClgtLandQueryBo extends BaseEntity {

	/**
	 * 分页大小
	 */
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	/**
	 * 当前页数
	 */
	@ApiModelProperty("当前页数")
	private Integer pageNum;
	/**
	 * 排序列
	 */
	@ApiModelProperty("排序列")
	private String orderByColumn;
	/**
	 * 排序的方向desc或者asc
	 */
	@ApiModelProperty(value = "排序的方向", example = "asc,desc")
	private String isAsc;


	/**
	 * 闲置土地所在市
	 */
	@ApiModelProperty("闲置土地所在市")
	private String city;
	/**
	 * 闲置土地所在县代码
	 */
	@ApiModelProperty("闲置土地所在县代码")
	private String countyCode;
	/**
	 * 闲置土地所在县名称
	 */
	@ApiModelProperty("闲置土地所在县名称")
	private String countyName;
	/**
	 * 合同编号
	 */
	@ApiModelProperty("合同编号")
	private String contractNo;
	/**
	 * 供应方式
	 */
	@ApiModelProperty("供应方式")
	private String supplyType;
	/**
	 * 项目名称
	 */
	@ApiModelProperty("项目名称")
	private String projectName;
	/**
	 * 数据年度
	 */
	@ApiModelProperty("数据年度")
	private String year;
	/**
	 * 数据季度
	 */
	@ApiModelProperty("数据季度")
	private String quarter;
	/**
	 * 电子监管号
	 */
	@ApiModelProperty("电子监管号")
	private String supervisionNo;
	/**
	 * 土地用途
	 */
	@ApiModelProperty("土地用途")
	private String landUse;
	/**
	 * 土地面积（公顷）
	 */
	@ApiModelProperty("土地面积（公顷）")
	private BigDecimal landArea;
	/**
	 * 签订日期
	 */
	@ApiModelProperty("签订日期")
	private Date signDate;
	/**
	 * 约定动工时间
	 */
	@ApiModelProperty("约定动工时间")
	private Date agreedStartTime;
	/**
	 * 实际动工时间
	 */
	@ApiModelProperty("实际动工时间")
	private Date actualStartTime;
	/**
	 * 实际竣工时间
	 */
	@ApiModelProperty("实际竣工时间")
	private Date actualEndTime;
	/**
	 * 闲置状态
	 */
	@ApiModelProperty("闲置状态")
	private String idleStatus;
	/**
	 * 图斑编号
	 */
	@ApiModelProperty("图斑编号")
	private String spotNumber;
	/**
	 * 内业备注
	 */
	@ApiModelProperty("内业备注")
	private String insideRemark;
	/**
	 * 消化类型
	 */
	@ApiModelProperty("消化类型")
	private String digestionType;
	/**
	 * 项目详细位置
	 */
	@ApiModelProperty("项目详细位置")
	private String address;
	/**
	 * 项目空间数据
	 */
	@ApiModelProperty("项目空间数据")
	private String geoData;
	/**
	 * 宗地号
	 */
	@ApiModelProperty("宗地号")
	private String parcelNum;
	/**
	 * 项目中心点经度
	 */
	@ApiModelProperty("项目中心点经度")
	private String longitude;
	/**
	 * 项目中心点纬度
	 */
	@ApiModelProperty("项目中心点纬度")
	private String latitude;
	/**
	 * 是否已分配巡查任务
	 */
	@ApiModelProperty("是否已分配巡查任务")
	private String isAllot;

	/**
	 * 任务编号
	 */
	@ApiModelProperty("任务编号")
	private String taskNo;

	/**
	 * 不动产权证号
	 */
	@ApiModelProperty("不动产权证号")
	private String warrantNum;
	/**
	 * 删除标记
	 */
	@ApiModelProperty("删除标记")
	private String deleteFlag;

}
