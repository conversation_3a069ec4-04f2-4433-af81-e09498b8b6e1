package com.ruoyi.web.controller.idle;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.bo.*;
import com.ruoyi.idle.domain.vo.DataPatrolTaskVo;
import com.ruoyi.idle.domain.vo.*;
import com.ruoyi.idle.service.*;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.message.ReusableMessage;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.unit.DataUnit;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 巡查任务 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查任务控制器", tags = {"巡查任务管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/patrol/task")
public class DataPatrolTaskController extends BaseController {

	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final IDataTaskUserService iDataTaskUserService;

	private final IDataIdleLandService iDataIdleLandService;

	private final IDataClgtLandService iDataClgtLandService;

	private final IDataPatrolGroupService iDataPatrolGroupService;

	private final IDataAttachmentService iDataAttachmentService;

	private final IDataAuditRecordService auditRecordService;

	private final IDataLandNotProvidedService notProvidedService;

	private final IDataGroupUserService groupUserService;

	private final ISysUserService userService;

	private final TokenService tokenService;

	private final IDataFileCatalogService iDataFileCatalogService;

	private final IDataDigestTypeService digestTypeService;

	private final IDataVerifyRecordService verifyRecordService;

	private final IDataHouseCheckService iDataHouseCheckService;

	private final ISysRegionService iSysRegionService;

	/**
	 * 查询巡查任务 列表
	 */
	@ApiOperation("查询巡查任务列表")
//    @PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/list")
	public TableDataInfo<DataPatrolTask> list(@Validated DataPatrolTaskQueryBo bo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				bo.setRegionCode(sysUser.getRegionCode());
				// 一般用户和县区人员只能看到已发布的任务
				bo.setPublishStatus("1");
			}
		}
//		return iDataPatrolTaskService.queryPageList(bo);
		return iDataPatrolTaskService.queryPageList1(bo);
	}

	/**
	 * 下载巡查照片
	 */
	@ApiOperation("下载巡查照片")
//    @PreAuthorize("@ss.hasPermi('idle:attachment:list')")
	@PostMapping("/exportImages")

	public void exportImages(@Validated DataPatrolTaskQueryBo bo, HttpServletResponse response) throws Exception {
		if(bo.getRegionCode()==null || bo.getRegionCode()==""){throw new Exception("请选择行政区!");}
		List<String> codes = iSysRegionService.queryChildList(bo.getRegionCode());
		if(codes!=null && codes.size()!=0){
			bo.setRegionCodes(codes);
		}
		List<Map<String, Object>> images = iDataPatrolTaskService.getImages(bo);
		response.setContentType("application/zip");
		response.setHeader("Content-Disposition", "attachment; filename=flat_files.zip");
		String profilePath = RuoYiConfig.getProfile();
		try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
			for (Map<String, Object> image : images) {
				String location = profilePath + image.get("location").toString();
				Object jzybh = image.get("jzybh");
				String s = "";
				if(ObjectUtil.isNotNull(jzybh)&&ObjectUtil.isNotEmpty(jzybh)){
					s = jzybh.toString();
				}else {
					s= image.get("id").toString()+image.get("regionName").toString();
				}
				File realFile = new File(location);
				if (!realFile.exists()) continue;
				String fileName = s+"\\"+image.get("name").toString()+"."+image.get("type").toString();
				// 写入 zip 根目录
				zipOut.putNextEntry(new ZipEntry(fileName));
				try (InputStream fis = new FileInputStream(realFile)) {
					StreamUtils.copy(fis, zipOut);
				}
				zipOut.closeEntry();
			}
		}
	}

	@PostMapping ("/downloads")
	public void downloadFlat(@RequestParam DataPatrolTaskQueryBo bo, HttpServletResponse response) throws IOException {
		List<Map<String, Object>> images = iDataPatrolTaskService.getImages(bo);
		response.setContentType("application/zip");
		response.setHeader("Content-Disposition", "attachment; filename=flat_files.zip");
		String profilePath = RuoYiConfig.getProfile();
		try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
			for (Map<String, Object> image : images) {
				 String location = profilePath + image.get("location").toString();
				Object jzybh = image.get("jzybh");
				String s = "";
				if(ObjectUtil.isNotNull(jzybh)&&ObjectUtil.isNotEmpty(jzybh)){
					s = jzybh.toString();
				}else {
					s= image.get("id").toString()+image.get("regionName").toString();
				}
				File realFile = new File(location);
				if (!realFile.exists()) continue;
				String fileName = s+"\\"+image.get("name").toString()+"."+image.get("type").toString();
				// 写入 zip 根目录
				zipOut.putNextEntry(new ZipEntry(fileName));
				try (InputStream fis = new FileInputStream(realFile)) {
					StreamUtils.copy(fis, zipOut);
				}
				zipOut.closeEntry();
			}
		}
	}

	/**
	 * 查询已提交巡查任务
	 */
	@ApiOperation("查询已提交巡查任务")
//    @PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/submitTask")
	public TableDataInfo<DataPatrolTaskVo> submitTask(@Validated DataPatrolTaskQueryBo bo) {
		List<String> codes = iSysRegionService.queryChildList(bo.getRegionCode());
		if(ObjectUtil.isNotEmpty(codes)){
			bo.setRegionCodes(codes);
		}
		return iDataPatrolTaskService.submitTaskList(bo);
	}

	/**
	 * 获取我的待审核任务
	 */
	@ApiOperation("内业-获取我的待审核任务")
	@Log(title = "巡查任务")
	@GetMapping("/getMyAuditList")
	public TableDataInfo<DataPatrolTask> getMyAuditList(MyTaskQueryBo queryBo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
			// 获取正在审核的任务列表
		return iDataPatrolTaskService.queryPageToDoList(queryBo,sysUser,"2");
	}

	@ApiOperation("保存或修改房屋核实结果记录")
	@Log(title = "巡查任务")
	@PostMapping("/saveHouseCheck")
	@Transactional(rollbackFor = Exception.class)
//	public AjaxResult<Void> saveHouseCheck(@RequestBody DataHouseCheckVo vo) {
	public AjaxResult<Void> saveHouseCheck(@RequestParam Map<String,Object> map) {
		boolean isSuccess = false;
		if (ObjectUtil.isNull(map)||map.size()==0)return AjaxResult.error("提交的房屋核实记录为空");
		//先将这两个时间字段转化为yyyy-mm-dd格式
//		Object jznd = map.get("jznd");
//		Object dcsj = map.get("dcsj");
//		if (ObjectUtil.isNotNull(jznd)) {
//			map.put("jznd",jznd);
//		}
//		if (ObjectUtil.isNotNull(dcsj)) {
//			try {
//				map.put("dcsj",DateFormate(dcsj.toString()));
//			} catch (Exception e) {
//				map.put("dcsj",new Date());
//			}
//		}else {
//			map.put("dcsj",new Date());
//		}
		map.put("dcsj",new Date());
		DataHouseCheck check = new DataHouseCheck();
		//map转vo
		ObjectMapper mapper = new ObjectMapper();
		DataHouseCheckVo vo = mapper.convertValue(map, DataHouseCheckVo.class);
		BeanUtils.copyProperties(vo,check);
		isSuccess = iDataHouseCheckService.updateById(check);
		// 保存意见信息(怪怪的)
		DataPatrolTask tempTask = iDataPatrolTaskService.queryById(vo.getTaskId());
		if (tempTask != null){
			tempTask.setPatrolResult(vo.getPatrolResult());
			tempTask.setPatrolOpinion(vo.getPatrolOpinion());
			tempTask.setIsPatrol("1");
			iDataPatrolTaskService.updateById(tempTask);
		}
		return isSuccess ? AjaxResult.success("成功保存核查记录", null) : AjaxResult.error("保存失败");
	}

	public String DateFormate(String input){
		// 定义输入的日期时间格式
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss 'GMT'XXX yyyy", Locale.ENGLISH);
		// 解析输入的日期时间字符串为ZonedDateTime对象
		ZonedDateTime zonedDateTime = ZonedDateTime.parse(input, inputFormatter);
		// 定义输出的日期时间格式
		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		// 格式化ZonedDateTime对象为所需的格式
		return zonedDateTime.format(outputFormatter);
	}

	/**
	 * 移动端查询巡查任务列表gtkj
	 */
	@ApiOperation("移动端拉取任务列表状态")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/appPulLTaskStatusist")
	public AjaxResult appPulLTaskStatusist(String regionCode) {
		Map<String, String> data=iDataPatrolTaskService.appPulLTaskStatusist( regionCode,  null);
		return AjaxResult.success("成功获取巡查任务列表状态", data);
	}
	/**
	 * 移动端查询巡查任务列表gtkj
	 */
	@ApiOperation("移动端拉取数据到手机列表")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/appPulList")
	public AjaxResult appPulList(String regionCode) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			String userid = sysUser.getUserId();

			List<DataPatrolTask> list1 = iDataPatrolTaskService.appPulList(regionCode,userid);
			List<DataClgtLand> list2 = iDataClgtLandService.appPulList(regionCode,userid);
			List<DataHouseCheck> list3 = iDataHouseCheckService.appPulList(regionCode,userid);
			if(list1==null || list2==null||list3==null){
				return AjaxResult.error("无任务或下载失败,请重试");
			}
			if(list1.size()<1 || list2.size()<1||list3.size()<1){
				return AjaxResult.error("无任务或下载失败,请重试");
			}
			HashMap<String,Object> data=new HashMap<>();
			data.put("list1Task",list1);
			data.put("list2ClgtLand",list2);
			data.put("list3HouseCheck",list3);

			return AjaxResult.success("成功获取巡查任务列表", data);
		}
		return AjaxResult.error("您的令牌已过期,请您重新登录");
	}

	/**
	 * 移动端查询巡查任务列表gtkj
	 */
	@ApiOperation("移动端查询巡查任务列表")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/appCheckList")
	public AjaxResult appCheckList(String regionCode) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 根据用户编号去巡查任务用户关系表中查询业务类型为外业（0）的巡查任务记录编号列表
//			LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
//			queryWrapper.eq(DataTaskUser::getUserId, sysUser.getUserId());
//			List<DataTaskUser> taskUserList = iDataTaskUserService.list(queryWrapper);
//			List<String> taskIdList = new ArrayList<>();
//			if (ObjectUtils.isEmpty(taskUserList)) return AjaxResult.success("没有查询到和您相关的任务");
//			for (DataTaskUser taskUser : taskUserList) {
//				taskIdList.add(taskUser.getTaskId());
//			}

//			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<>();
//			taskQueryWrapper.in(DataPatrolTask::getId, taskIdList);
//			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
//				taskQueryWrapper.eq(DataPatrolTask::getRegionCode, sysUser.getRegionCode());
//			} else {
//				taskQueryWrapper.eq(DataPatrolTask::getRegionCode, regionCode);
//			}

//			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<>();
//			taskQueryWrapper.eq(DataPatrolTask::getRegionCode, regionCode);
//			// 只能获取已经发布到巡查任务
//			taskQueryWrapper.eq(DataPatrolTask::getPublishStatus, "1");
//			// 只能获取自行举证和连线核查的任务
//			taskQueryWrapper.in(DataPatrolTask::getPatrolType, Arrays.asList("0", "1"));
//			// 只能获取还没有巡查或者正在审核的任务
//			taskQueryWrapper.in(DataPatrolTask::getIsPatrol, Arrays.asList("0", "2","3"));
//			// 按照时间排序
//			taskQueryWrapper.orderByDesc(DataPatrolTask::getCreateTime);
//			List<DataPatrolTask> taskList = iDataPatrolTaskService.list(taskQueryWrapper);
//			List<DataPatrolTaskVo> list = new ArrayList<>();
//
//			int bh = 1;
//			for (DataPatrolTask task : taskList){
//				DataPatrolTaskVo taskVo = new DataPatrolTaskVo();
//				DataClgtLand idleLand = iDataClgtLandService.getById(task.getIdleLandId());
//				if (idleLand != null){
//					task.setContractNo(idleLand.getContractNo());
//					task.setSupervisionNo(idleLand.getSupervisionNo());
//				}
//				BeanUtils.copyProperties(task,taskVo);
//				String clgtId = task.getIdleLandId();
//				String taskId = task.getId();
//				taskVo.setDataClgtLand(ObjectUtil.isNotNull(idleLand)?idleLand : new DataClgtLand());
//				LambdaQueryWrapper<DataHouseCheck> checkQueryWrapper = new LambdaQueryWrapper<>();
//				checkQueryWrapper.eq(DataHouseCheck::getTaskId,taskId);
//				checkQueryWrapper.eq(DataHouseCheck::getClgtId,clgtId);
//				DataHouseCheck voOne = iDataHouseCheckService.getVoOne(checkQueryWrapper, DataHouseCheck.class);
//				taskVo.setDataHouseCheck(ObjectUtil.isNotNull(voOne)?voOne : new DataHouseCheck());
//				//临时编号
//				taskVo.setBh(bh++);
//				list.add(taskVo);
//			}
			int bh = 1;
			List<DataPatrolTaskVo> dataPatrolTaskVos = iDataPatrolTaskService.appCheckList(regionCode);
			for (DataPatrolTaskVo v: dataPatrolTaskVos){
				//临时编号
				v.setBh(bh++);
			}
//			if (taskList.size() != 0) {
			if (dataPatrolTaskVos.size() != 0) {
				return AjaxResult.success("成功获取巡查任务列表", dataPatrolTaskVos);
			} else {
				return AjaxResult.success("没有查询到和您相关的任务");
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}
	/**
	 * 移动端查询巡查任务列表
	 */
	@ApiOperation("移动端查询巡查任务列表")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/appList")
	public AjaxResult appList(String regionCode, String quarter) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 根据用户编号去巡查任务用户关系表中查询业务类型为外业（0）的巡查任务记录编号列表
			LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataTaskUser::getUserId, sysUser.getUserId());
//			queryWrapper.eq(DataTaskUser :: getBusType,"0");
			List<DataTaskUser> taskUserList = iDataTaskUserService.list(queryWrapper);
			List<String> taskIdList = new ArrayList<>();
			if (null == taskUserList && taskUserList.size() == 0) return AjaxResult.success("没有查询到和您相关的任务");
			for (DataTaskUser taskUser : taskUserList) {
				taskIdList.add(taskUser.getTaskId());
			}

			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<DataPatrolTask>();
			taskQueryWrapper.in(DataPatrolTask::getId, taskIdList);
			if (StringUtils.isNotBlank(quarter)) taskQueryWrapper.eq(DataPatrolTask::getQuarter, quarter);
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				taskQueryWrapper.eq(DataPatrolTask::getRegionCode, sysUser.getRegionCode());
			} else {
				taskQueryWrapper.eq(DataPatrolTask::getRegionCode, regionCode);
			}
			// 只能获取已经发布到巡查任务
			taskQueryWrapper.eq(DataPatrolTask::getPublishStatus, "1");
			// 只能获取自行举证和连线核查的任务
			taskQueryWrapper.in(DataPatrolTask::getPatrolType, new ArrayList<>(Arrays.asList("0", "1")));
			// 只能获取还没有巡查或者正在审核的任务
			taskQueryWrapper.in(DataPatrolTask::getIsPatrol, Arrays.asList("0", "2"));
			// 按照时间排序
			taskQueryWrapper.orderByDesc(DataPatrolTask::getCreateTime);
			List<DataPatrolTask> taskList = iDataPatrolTaskService.list(taskQueryWrapper);
			for (DataPatrolTask task : taskList){
				DataIdleLand idleLand = iDataIdleLandService.getById(task.getIdleLandId());
				if (idleLand != null){
					task.setContractNo(idleLand.getContractNo());
					task.setSupervisionNo(idleLand.getSupervisionNo());
				}
			}
			if (taskList.size() != 0) {
				return AjaxResult.success("成功获取巡查任务列表", taskList);
			} else {
				return AjaxResult.success("没有查询到和您相关的任务");
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	/**
	 * 移动端分页查询巡查任务列表
	 */
	@ApiOperation("移动端分页查询巡查任务列表")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@ApiIgnore
	@GetMapping("/appPageList")
	public AjaxResult appPageList(@Validated DataPatrolTaskQueryBo bo) {
		if (bo.getPageNum() == null) bo.setPageNum(1);
		if (bo.getPageSize() == null) bo.setPageSize(10);
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 根据用户编号去巡查任务用户关系表中查询业务类型为外业（0）的巡查任务记录编号列表
			LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataTaskUser::getUserId, sysUser.getUserId());
			queryWrapper.eq(DataTaskUser::getBusType, "0");
			List<DataTaskUser> taskUserList = iDataTaskUserService.list(queryWrapper);
			List<String> taskIdList = new ArrayList<>();
			if (null == taskUserList && taskUserList.size() == 0) return AjaxResult.success("没有查询到和您相关的任务");
			for (DataTaskUser taskUser : taskUserList) {
				taskIdList.add(taskUser.getTaskId());
			}

			AjaxResult<IPage<DataPatrolTask>> returnPage = new AjaxResult<IPage<DataPatrolTask>>();
			Page<DataPatrolTask> page = new Page<DataPatrolTask>(bo.getPageNum(), bo.getPageSize());
			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<DataPatrolTask>();
			taskQueryWrapper.in(DataPatrolTask::getId, taskIdList);
			if (StringUtils.isNotBlank(bo.getMeetingTime()))
				taskQueryWrapper.like(DataPatrolTask::getMeetingTime, bo.getMeetingTime());
			taskQueryWrapper.eq(DataPatrolTask::getRegionCode, bo.getRegionCode());
			// 只能获取已经发布到巡查任务
			taskQueryWrapper.eq(DataPatrolTask::getPublishStatus, "1");
			// 按照时间排序
			taskQueryWrapper.orderByDesc(DataPatrolTask::getCreateTime);
			//分页数据
			IPage<DataPatrolTask> pageData = iDataPatrolTaskService.page(page, taskQueryWrapper);
			returnPage.setData(pageData);
			return returnPage;
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	/**
	 * 根据巡查任务编号获取内业核查人员
	 */
	@ApiOperation("根据巡查任务编号获取内业核查人员")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/getVerifyUserList")
	public AjaxResult getVerifyUserList(String taskId) {
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (null == patrolTask) return AjaxResult.success("没有匹配到对应的巡查任务");
		List<DataTaskUser> verifyUserList = iDataTaskUserService.list(new LambdaQueryWrapper<DataTaskUser>()
			.eq(DataTaskUser::getTaskId, patrolTask.getId())
			.eq(DataTaskUser::getBusType, "1"));
		if (null == verifyUserList || verifyUserList.size() == 0) return AjaxResult.error("没有匹配到对应的内业核实人员");
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("patrolTask", patrolTask);
		resultMap.put("verifyUser", verifyUserList);
		return AjaxResult.success("成功获取内业核查人员", resultMap);
	}

	/**
	 * 根据巡查编号获取详细信息
	 */
	@ApiOperation("根据巡查编号获取详细信息")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@GetMapping("/getDetailInfo")
	public AjaxResult getDetailInfo(String taskId) {
		if (taskId == null) return AjaxResult.error("巡查项目编号不能为空");
		PatrolTaskDetailInfo detailInfo = new PatrolTaskDetailInfo();
		// 首先根据巡查编号获取巡查任务信息
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (null == patrolTask) return AjaxResult.success("根据编号没有匹配到对应的巡查项目");
		detailInfo.setPatrolTask(patrolTask);
		// 再根据闲置土地编号获取对应的闲置土地记录信息
//		if (patrolTask.getProjectType().equals("1")) {
//			DataLandNotProvided unprovided = notProvidedService.getById(patrolTask.getIdleLandId());
//			if (null != unprovided) detailInfo.setUnprovidedLand(unprovided);
//		} else {
//			DataIdleLand idleLand = iDataIdleLandService.queryById(patrolTask.getIdleLandId());
//			if (null != idleLand) detailInfo.setIdleLand(idleLand);
//		}
		DataClgtLand idleLand = iDataClgtLandService.queryById(patrolTask.getIdleLandId());
		if (null != idleLand) detailInfo.setIdleLand(idleLand);

		DataHouseCheck dataHouseCheck = iDataHouseCheckService.selectByTaskId(taskId);
		if (null != dataHouseCheck) detailInfo.setDataHouseCheck(dataHouseCheck);

		// 再根据巡查编号获取内页核实人员
		List<DataTaskUser> verifyUserList = iDataTaskUserService.list(new LambdaQueryWrapper<DataTaskUser>()
			.eq(DataTaskUser::getTaskId, patrolTask.getId())
			.eq(DataTaskUser::getBusType, "1"));
		if (null != verifyUserList && verifyUserList.size() != 0) detailInfo.setVerifyUserList(verifyUserList);
		// 再根据巡查编号获取外业巡查人员
		List<DataTaskUser> patrolUserList = iDataTaskUserService.list(new LambdaQueryWrapper<DataTaskUser>()
			.eq(DataTaskUser::getTaskId, patrolTask.getId())
			.eq(DataTaskUser::getBusType, "0"));
		if (null != patrolUserList && patrolUserList.size() != 0) detailInfo.setPatrolUserList(patrolUserList);
//		if (patrolTask.getIsPatrol().equals("1")) {
//			// 已巡查的任务需要获取巡查附件
//			List<DataAttachment> patrolFileList = iDataAttachmentService.list(new LambdaQueryWrapper<DataAttachment>()
//				.eq(DataAttachment::getPatrolTaskId, patrolTask.getId()));
//			if (null != patrolFileList && patrolFileList.size() != 0) detailInfo.setPatrolFileList(patrolFileList);
//		}

		// 根据任务编号获取对应的审核记录
		List<DataAuditRecord> auditRecordList = auditRecordService.list(new LambdaQueryWrapper<DataAuditRecord>()
			.eq(DataAuditRecord::getTaskId, patrolTask.getId())
			.orderByAsc(DataAuditRecord :: getCreateTime));
		if (null != auditRecordList && auditRecordList.size() != 0) detailInfo.setAuditRecordList(auditRecordList);

		// 获取项目对应的文件目录列表
		// 先通过任务编号获取对应闲置土地的消化类型
//		LambdaQueryWrapper<DataFileCatalog> queryWrapper = new LambdaQueryWrapper<>();
//		if (patrolTask.getProjectType().equals("1")){
//			queryWrapper.eq(DataFileCatalog::getCatalogType, "批而未供");
//			queryWrapper.orderByAsc(DataFileCatalog :: getOrderNum);
//			List<DataFileCatalog> fileCatalogList = iDataFileCatalogService.list(queryWrapper);
//			detailInfo.setFileCatalogList(fileCatalogList);
//		}else{
//
//			String catalogType = iDataIdleLandService.getDigestionTypeByTaskId(taskId);
//			// 通过项目的消化类型找到对应的固定的父节点
//			String parentCatalog = getParentCatalog(catalogType);
//			catalogType = StringUtils.isNotBlank(parentCatalog) ? parentCatalog : catalogType;
//			// 先按照文件目录类型catalogType查询目录数据
//			queryWrapper.eq(DataFileCatalog::getCatalogType, catalogType);
//			queryWrapper.orderByAsc(DataFileCatalog :: getOrderNum);
//			List<DataFileCatalog> fileCatalogList = iDataFileCatalogService.list(queryWrapper);
//			detailInfo.setFileCatalogList(fileCatalogList);
//		}

		// 获取对应的核查记录信息
		List<DataVerifyRecord> verifyRecords = verifyRecordService.list(new LambdaQueryWrapper<DataVerifyRecord>()
		.eq(DataVerifyRecord :: getTaskId,taskId));
		detailInfo.setVerifyRecordList(verifyRecords);

		return AjaxResult.success(detailInfo);
	}

	private String getParentCatalog(String tempCatalog){
		if (StringUtils.isBlank(tempCatalog)) return "";
		DataDigestType digestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType :: getTypeName,tempCatalog),false);
		if (digestType == null) return "";
		DataDigestType parentDigestType = digestTypeService.getOne(new LambdaQueryWrapper<DataDigestType>().eq(DataDigestType :: getId,digestType.getParentId()),false);
		if (parentDigestType == null) return "";
		return parentDigestType.getTypeName();
	}



	/**
	 * 编辑巡查任务
	 */
	@ApiOperation("编辑巡查任务")
//	@PreAuthorize("@ss.hasPermi('idle:task:list')")
	@PostMapping("/editTask")
	@Transactional
	public AjaxResult editTask(@RequestBody PatrolTaskEditVo taskEditVo) {
		if (taskEditVo == null) return AjaxResult.error("巡查任务数据不能为空");
		// 先根据任务编号查询对应的记录
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskEditVo.getTaskId());
		if (null == patrolTask) return AjaxResult.success("根据编号没有匹配到对应的巡查项目");
		patrolTask.setPatrolType(taskEditVo.getPatrolType());
		patrolTask.setMeetingEndTime(taskEditVo.getMeetingTime());
		patrolTask.setMeetingEndTime(taskEditVo.getMeetingEndTime());
		//patrolTask.setPatrolGroup(taskEditVo.getPatrolGroup());
		//patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById(Long.valueOf(taskEditVo.getPatrolGroup())));
		LoginUser loginUser = SecurityUtils.getLoginUser();
		if (loginUser != null) {
			//设置创建的用户和创建时间
			patrolTask.setUpdateBy(loginUser.getUsername());
		}
		iDataPatrolTaskService.updateById(patrolTask);

		// 接下来保存内业核查人员变更情况
		LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataTaskUser::getTaskId, taskEditVo.getTaskId());
		queryWrapper.eq(DataTaskUser::getBusType, "1");
		// 先删除原先的人员
		iDataTaskUserService.remove(queryWrapper);
		List<IdleAllotTaskUserVo> userList = taskEditVo.getUserList();
		if (null != userList && userList.size() != 0) {
			for (IdleAllotTaskUserVo user : userList) {
				DataTaskUser taskUser = new DataTaskUser();
				BeanUtils.copyProperties(user, taskUser);
				taskUser.setTaskId(taskEditVo.getTaskId());
				if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("1");
				taskUser.setRemark("后台核实人员");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					taskUser.setCreateBy(loginUser.getUsername());
					taskUser.setUpdateBy(loginUser.getUsername());
				}
				iDataTaskUserService.save(taskUser);
			}
		}
		return AjaxResult.success("成功修改巡查任务");
	}

	/**
	 * 一键发布所有未发布的任务
	 */
	@ApiOperation("一键发布所有未发布的任务")
	@Log(title = "巡查任务")
	@PostMapping("/publishAllTask")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult publishAllTask(){
		try{
			// step.1 先查询所有的未发布的任务
			LambdaQueryWrapper<DataPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
//			queryWrapper.eq(DataPatrolTask :: getPublishStatus,"0");
			List<DataPatrolTask> taskList = iDataPatrolTaskService.list(queryWrapper);
			if (taskList.size() == 0) return AjaxResult.success("您的所有项目均已发布，无需再次发布",null);
			// step.2 循环未发布的任务开始发布
			// 成功发布的数量
			int successCount = 0;
			// 发布失败的原因
			StringBuilder stringBuilder = new StringBuilder();
			for (DataPatrolTask task : taskList){
				CompletableFuture<Void> future = CompletableFuture.runAsync(()->{
					 publishTask(task);
					task.setPublishStatus("1");
					iDataPatrolTaskService.updateById(task);
				});
//				AjaxResult result = future.join();
//				AjaxResult result = publishTask(task);
//				if (result.getCode() != 200){
//					stringBuilder.append(task.getId()+"，失败原因："+result.getMsg());
//				}else{
//					successCount += 1;
//
//				}
				successCount += 1;
			}
			int failCount = taskList.size() - successCount;
			if (StringUtils.isNotBlank(stringBuilder.toString())){
				return AjaxResult.success("一键发布成功,本次发布"+taskList.size() +"个任务，失败"+failCount+"个,失败记录"+stringBuilder.toString());
			}else{
				return AjaxResult.success("一键发布成功,本次发布"+taskList.size() +"个任务，成功"+successCount+"个");
			}

		}catch (Exception ex){
			return AjaxResult.error("一键发布失败，失败原因："+ex.getMessage());
		}
	}

	/**
	 * 修改巡查任务发布状态
	 */
	@ApiOperation("修改巡查任务发布状态")
	@Log(title = "巡查任务")
	@PostMapping("/changePublishStatus")
	//@Transactional(rollbackFor = Exception.class)
	public AjaxResult<Void> changePublishStatus(String taskId) {
		if (taskId == null) return AjaxResult.error("巡查任务编号不能为空");
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (null == patrolTask) return AjaxResult.error("没有匹配到对应的巡查任务");
		if (patrolTask.getPublishStatus().equals("1")) {
			patrolTask.setPublishStatus("0");
			patrolTask.setUpdateBy(SecurityUtils.getUsername());
			iDataPatrolTaskService.updateById(patrolTask);
			return AjaxResult.success("成功修改任务状态");
		} else {
			patrolTask.setPublishStatus("1");
			return publishTask(patrolTask);
		}
	}

	private AjaxResult publishTask(DataPatrolTask patrolTask){
		boolean isSuccess = false;
		// 先根据任务编号和业务类型判断是否已经存在外业巡查人员
		List<DataTaskUser> taskUsers = iDataTaskUserService.list(new LambdaQueryWrapper<DataTaskUser>()
			.eq(DataTaskUser :: getTaskId,patrolTask.getId())
			.eq(DataTaskUser :: getBusType,"0"));

		//获取该任务已经关联的所有人员
		List<String> publishedTaskUserIds = new ArrayList();
		if (ObjectUtil.isNotNull(taskUsers)) {
			for (DataTaskUser t:taskUsers ) {
				publishedTaskUserIds.add(t.getUserId());
			}
		}
//		if (taskUsers.size() == 0){
			// 不存在
			// 获取巡查任务的行政区代码
			String regionCode = patrolTask.getRegionCode();
			if (StringUtils.isBlank(regionCode))return AjaxResult.success("发布成功，该巡查任务记录行政区代码属性为空，请检查任记录");
			//获取市级行政区
			String s = regionCode.substring(0, 6);
//			 根据行政区代码获取巡查组
			DataPatrolGroup patrolGroup = iDataPatrolGroupService.getOne(new LambdaQueryWrapper<DataPatrolGroup>()
				.eq(DataPatrolGroup :: getCountyCode,s),false);
			if (patrolGroup == null)return AjaxResult.success("发布成功，根据行政区代码没有匹配到对应的巡查组");
//			DataPatrolGroup patrolGroup = iDataPatrolGroupService.getOne(new LambdaQueryWrapper<DataPatrolGroup>()
//				.eq(StrUtil.isNotBlank(groupId),DataPatrolGroup::getId,groupId));
//			if (patrolGroup == null)return AjaxResult.success("发布失败，根据行政区代码没有匹配到对应的巡查组");
			// 修改巡查任务的巡查组编号和名称
			patrolTask.setPatrolGroup(patrolGroup.getId());
			patrolTask.setPatrolGroupName(patrolGroup.getGroupName());
//			patrolTask.setRegionCode(patrolGroup.getCountyCode());

			// 根据巡查组编号获取该巡查组下面的所有用户编号
			List<String> userIds = groupUserService.getUserIdsByGroupId(patrolGroup.getId());

			//	剔除已经关联任务的人员
			if (ObjectUtil.isNotNull(publishedTaskUserIds)) {
				userIds.removeAll(publishedTaskUserIds);
			}
		// 根据巡查组下面的用户Id列表获取所有用户
			if (userIds.size() == 0) return AjaxResult.success("发布成功，该巡查组下没有添加外业巡查组员");
			List<SysUser> userList = userService.list(new LambdaQueryWrapper<SysUser>()
				.in(SysUser :: getUserId,userIds)
				.orderByDesc(SysUser :: getCreateTime));
			if (userList.size() == 0) return AjaxResult.success("发布成功，该巡查组下没有添加外业巡查组员");
			isSuccess = iDataPatrolTaskService.updateById(patrolTask);

			//任务状态修改成功后再更新核查表单任务id
			String landId = patrolTask.getIdleLandId();
			String taskId = patrolTask.getId();
			DataHouseCheck dataHouseCheck = iDataHouseCheckService.queryByCgltId(landId);
			dataHouseCheck.setTaskId(taskId);
			boolean update = iDataHouseCheckService.updateById(dataHouseCheck);

		boolean hasAdminUser = false;
			List<DataTaskUser> taskUserList = new ArrayList<>();
//			for (SysUser user : userList){
//				// 遍历
//				taskUserList.add(getTaskUser(user,patrolTask.getId(),true));
//				// 判断是不是县区管理员
//				if (!hasAdminUser){
////					if (userService.isCountyAdmin(user.getUserId())){
////						taskUserList.add(getTaskUser(user,patrolTask.getId(),true));
////						hasAdminUser = true;
////						break;
////					}else{
//						// 不是负责人
//						// taskUserList.add(getTaskUser(user,taskId,false));
////					}
//				}else {
//					// 不是负责人
//					// taskUserList.add(getTaskUser(user,taskId,false));
//				}
//			}
			// 防止所有的巡查组成员都不是县区管理员
			if (taskUserList.size() != 0){
////				if (!hasAdminUser) taskUserList.get(0).setIsLeader("1");
//				for (DataTaskUser taskUser : taskUserList) {
//					// 判断是否有相同任务和相同用户的记录
//					DataTaskUser tempTaskUser = iDataTaskUserService.getOne(new LambdaQueryWrapper<DataTaskUser>()
//						.eq(DataTaskUser::getTaskId, taskUser.getTaskId())
//						.eq(DataTaskUser::getUserId, taskUser.getUserId()), false);
//					if (tempTaskUser == null) {
//						isSuccess = iDataTaskUserService.save(taskUser);
//					} else {
//						tempTaskUser.setUserName(taskUser.getUserName());
//						tempTaskUser.setUserPhone(taskUser.getUserPhone());
//						tempTaskUser.setIsLeader(taskUser.getIsLeader());
//						tempTaskUser.setBusType(taskUser.getBusType());
//						isSuccess = iDataTaskUserService.updateById(tempTaskUser);
//					}
//				}
////			}
			if (isSuccess&&update){
				return AjaxResult.success("发布成功");
			}else {
				return AjaxResult.error("发布失败");
			}
		}else{
			// 已经有对应的外业人员，相当于只是修改一下任务发布状态的值0---->1
			return AjaxResult.success("发布成功");
		}
	}

	private DataTaskUser getTaskUser(SysUser user,String taskId,boolean isLeader){
		DataTaskUser taskUser = new DataTaskUser();
		taskUser.setTaskId(taskId);
		taskUser.setUserId(user.getUserId());
		taskUser.setUserName(user.getUserName());
		taskUser.setUserPhone(user.getPhonenumber());
		taskUser.setUserId(user.getUserId());
		taskUser.setIsLeader(isLeader ? "1":"0");
		taskUser.setBusType("0");
		taskUser.setRemark("外业巡查人员");
		taskUser.setCreateBy(SecurityUtils.getUsername());
		return taskUser;
	}

	/**
	 * 填写外业巡查意见
	 */
	@ApiOperation("填写外业巡查意见")
	@Log(title = "巡查任务")
	@PostMapping("/editPatrolOpinion")
	public AjaxResult<Void> editPatrolOpinion(String taskId, String opinion) {
		if (taskId == null) return AjaxResult.error("巡查任务编号不能为空");
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (null == patrolTask) return AjaxResult.success("没有匹配到对应的巡查任务");
		patrolTask.setPatrolOpinion(opinion);
		// 根据巡查编号去查询是否有附件，有的话就自动到审核阶段
		//LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		//queryWrapper.eq(DataAttachment::getPatrolTaskId, taskId);
		//List<DataAttachment> attachmentList = iDataAttachmentService.list(queryWrapper);
		//if (attachmentList != null && attachmentList.size() != 0) {
		//	patrolTask.setIsPatrol("2");
		//}
		return toAjax(iDataPatrolTaskService.updateById(patrolTask) ? 1 : 0);
	}

	/**
	 * 内业审核
	 */
	@ApiOperation("内业审核")
	@Log(title = "巡查任务")
	@PostMapping("/taskReview")
	public AjaxResult<Void> taskReview(String taskId, String status, String result) {
		return toAjax(changeTaskStatus(taskId, status, result));
	}

	private boolean changeTaskStatus(String taskId, String status, String result) {
		DataPatrolTask patrolTask = iDataPatrolTaskService.queryById(taskId);
		if (status.equals("0")) {
			// 驳回
			patrolTask.setIsPatrol("3");
			String msg = StringUtils.isNotBlank(result) ? "驳回,驳回的原因：" + result : "驳回";
			patrolTask.setRemark(msg);
			saveAuditRecord(taskId, "1", msg);
		} else if (status.equals("1")) {
			// 审核通过
			//patrolTask.setRemark("审核通过");
			patrolTask.setIsPatrol("1");
			patrolTask.setPatrolResult(result);
			saveAuditRecord(taskId, "0", "审核通过");
		} else if (status.equals("2")) {
			// 提交审核
			patrolTask.setIsPatrol("2");
			patrolTask.setSubmitRemark(result);
		}
		return iDataPatrolTaskService.updateById(patrolTask);
	}

	@ApiOperation("修改巡查任务状态")
	@Log(title = "巡查任务")
	@PostMapping("/updateTaskStatus")
	public AjaxResult changeTaskStatus(ChangeTaskStatusVo vo){
		boolean isSuccess = changeTaskStatus(vo.getTaskId(),vo.getStatus(),vo.getResult());
		return isSuccess ? AjaxResult.success("成功修改任务状态",null) : AjaxResult.error("修改任务状态失败");
	}

	@ApiOperation("保存或修改闲置土地核实结果记录")
	@Log(title = "巡查任务")
	@PostMapping("/saveIdleVerify")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult<Void> saveIdleVerifyData(@RequestBody List<DataIdleVerifyVo> idleVerifyVoList) {
		boolean isSuccess = false;
		if (idleVerifyVoList.size() == 0)return AjaxResult.error("提交的闲置土地核实结果为空");
		for (DataIdleVerifyVo idleVerifyVo : idleVerifyVoList){
			// step.1 根据任务编号查询对应的记录（得判断一下该任务是闲置土地0还是批而未供1）
			DataPatrolTask task = iDataPatrolTaskService.queryById(idleVerifyVo.getTaskId());
			if (task == null) return AjaxResult.error("没有找到对应的巡查任务记录");
			if (task.getProjectType().equals("1")) return AjaxResult.error("巡查任务项目类型是批而未供，请使用正确的接口");
			// step.2 根据闲置土地编号获取闲置土地信息
			DataClgtLand idleLand = iDataClgtLandService.queryById(task.getIdleLandId());
			if (idleLand == null) return AjaxResult.error("没有找到对应的存量国土记录");
			// step.3 根据任务编号、闲置土地编号、文件目录编号查询是否有核查结果存在
			LambdaQueryWrapper<DataVerifyRecord> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataVerifyRecord :: getTaskId,task.getId())
				.eq(DataVerifyRecord :: getDataId,idleLand.getId())
				.eq(DataVerifyRecord :: getCatalogId,idleVerifyVo.getCatalogId());

			DataVerifyRecord idleVerify = verifyRecordService.getOne(queryWrapper,false);

			if (idleVerify != null) {
				// step.3.1 有就修改
				idleVerify.setIsProcess(idleVerifyVo.getIsProcess());
				idleVerify.setVideoIsReal(idleVerifyVo.getVideoIsReal());
				idleVerify.setSiteIsReal(idleVerifyVo.getSiteIsReal());
				idleVerify.setHandleIsCorrect(idleVerifyVo.getHandleIsCorrect());
				idleVerify.setVerificationSelect(idleVerifyVo.getVerificationSelect());
				idleVerify.setVerificationSituation(idleVerifyVo.getVerificationSituation());
				DataFileCatalog fileCatalog = iDataFileCatalogService.getById(idleVerifyVo.getCatalogId());
				idleVerify.setCatalogName(fileCatalog == null ? "" : fileCatalog.getName());
				isSuccess = verifyRecordService.updateById(idleVerify);
			} else {
				// step.3.2 没有就创建核查结果实体赋值保存
				DataVerifyRecord newIdleVerify = new DataVerifyRecord();
				newIdleVerify.setTaskId(idleVerifyVo.getTaskId());
				newIdleVerify.setDataId(idleLand.getId());
				newIdleVerify.setProjectType("0");// 标识为闲置土地
				newIdleVerify.setCatalogId(idleVerifyVo.getCatalogId());
				newIdleVerify.setIsProcess(idleVerifyVo.getIsProcess());
				newIdleVerify.setVideoIsReal(idleVerifyVo.getVideoIsReal());
				newIdleVerify.setSiteIsReal(idleVerifyVo.getSiteIsReal());
				newIdleVerify.setHandleIsCorrect(idleVerifyVo.getHandleIsCorrect());
				newIdleVerify.setVerificationSelect(idleVerifyVo.getVerificationSelect());
				newIdleVerify.setVerificationSituation(idleVerifyVo.getVerificationSituation());
				DataFileCatalog fileCatalog = iDataFileCatalogService.getById(idleVerifyVo.getCatalogId());
				newIdleVerify.setCatalogName(fileCatalog == null ? "" : fileCatalog.getName());
				isSuccess = verifyRecordService.save(newIdleVerify);
			}
		}
		DataIdleVerifyVo tempVo = idleVerifyVoList.get(0);
		// 保存意见信息(怪怪的)
		DataPatrolTask tempTask = iDataPatrolTaskService.queryById(tempVo.getTaskId());
		if (tempTask != null){
			tempTask.setPatrolResult(tempVo.getPatrolResult());
			iDataPatrolTaskService.updateById(tempTask);
		}
		// step.3 修改巡查任务状态
//		if (tempVo.getIsSubmit()){
			changeTaskStatus(tempVo.getTaskId(), tempVo.getTaskStatus(), tempVo.getPatrolResult());
//		}
		return isSuccess ? AjaxResult.success("成功保存核查记录", null) : AjaxResult.error("保存失败");
	}

	@ApiOperation("保存或修改批而未供核实结果记录")
	@Log(title = "巡查任务")
	@PostMapping("/saveUnprovidedVerify")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult<Void> saveUnprovidedVerifyData(@RequestBody List<DataUnprovidedVerifyVo> unprovidedVerifyVoList) {
		boolean isSuccess = false;
		if (unprovidedVerifyVoList.size() == 0)return AjaxResult.error("提交的批而未供核实结果为空");
		for (DataUnprovidedVerifyVo unprovidedVerifyVo : unprovidedVerifyVoList){
			// step.1 根据任务编号查询对应的记录（得判断一下该任务是闲置土地0还是批而未供1）
			DataPatrolTask task = iDataPatrolTaskService.queryById(unprovidedVerifyVo.getTaskId());
			if (task == null) return AjaxResult.error("没有找到对应的巡查任务记录");
			if (task.getProjectType().equals("0")) return AjaxResult.error("巡查任务项目类型是闲置土地，请使用正确的接口");
			// step.2 根据批而未供编号获取批而未供信息
			DataLandNotProvided notProvided = notProvidedService.queryById(task.getIdleLandId());
			if (notProvided == null) return AjaxResult.error("没有找到对应的批而未供记录");
			// step.3 根据任务编号、批而未供编号、文件目录编号查询是否有核查结果存在
			LambdaQueryWrapper<DataVerifyRecord> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataVerifyRecord :: getTaskId,task.getId())
				.eq(DataVerifyRecord :: getDataId,notProvided.getId())
				.eq(DataVerifyRecord :: getCatalogId,unprovidedVerifyVo.getCatalogId());

			DataVerifyRecord unprovidedVerify = verifyRecordService.getOne(queryWrapper,false);

			if (unprovidedVerify != null) {
				// step.3.1 有就修改
				BeanUtils.copyProperties(unprovidedVerifyVo, unprovidedVerify);
				unprovidedVerify.setIsProcess(unprovidedVerifyVo.getIsProcess());
				unprovidedVerify.setHandleIsCorrect(unprovidedVerifyVo.getHandleIsCorrect());
				DataFileCatalog fileCatalog = iDataFileCatalogService.getById(unprovidedVerifyVo.getCatalogId());
				unprovidedVerify.setCatalogName(fileCatalog == null ? "" : fileCatalog.getName());
				isSuccess = verifyRecordService.updateById(unprovidedVerify);
			} else {
				// step.3.2 没有就创建核查结果实体赋值保存
				DataVerifyRecord newUnprovidedVerify = new DataVerifyRecord();
				newUnprovidedVerify.setTaskId(unprovidedVerifyVo.getTaskId());
				newUnprovidedVerify.setDataId(notProvided.getId());
				newUnprovidedVerify.setProjectType("1");// 标识为批而未供
				newUnprovidedVerify.setCatalogId(unprovidedVerifyVo.getCatalogId());
				newUnprovidedVerify.setIsProcess(unprovidedVerifyVo.getIsProcess());
				newUnprovidedVerify.setHandleIsCorrect(unprovidedVerifyVo.getHandleIsCorrect());
				DataFileCatalog fileCatalog = iDataFileCatalogService.getById(unprovidedVerifyVo.getCatalogId());
				newUnprovidedVerify.setCatalogName(fileCatalog == null ? "" : fileCatalog.getName());
				isSuccess = verifyRecordService.save(newUnprovidedVerify);
			}
		}
		DataUnprovidedVerifyVo tempVo = unprovidedVerifyVoList.get(0);
		// 保存意见信息(怪怪的)
		DataPatrolTask tempTask = iDataPatrolTaskService.queryById(tempVo.getTaskId());
		if (tempTask != null){
			tempTask.setPatrolResult(tempVo.getPatrolResult());
			iDataPatrolTaskService.updateById(tempTask);
		}
		// step.3 修改巡查任务状态
		if (tempVo.getIsSubmit()){
			changeTaskStatus(tempVo.getTaskId(), tempVo.getTaskStatus(), tempVo.getPatrolResult());
		}
		return isSuccess ? AjaxResult.success("成功保存核查记录", null) : AjaxResult.error("保存失败");
	}


	private void creatUnprovidedVerify(DataUnprovidedVerify newUnprovidedVerify, DataLandNotProvided notProvided) {
		newUnprovidedVerify.setCountyCode(notProvided.getCountyCode());
		newUnprovidedVerify.setCountyName(notProvided.getCountyName());
		newUnprovidedVerify.setProjectName(notProvided.getProjectName());
		newUnprovidedVerify.setYear(notProvided.getYear());
		newUnprovidedVerify.setQuarter(notProvided.getQuarter());
		newUnprovidedVerify.setAddress(notProvided.getAddress());
		newUnprovidedVerify.setSupplyType(notProvided.getSupplyType());
		newUnprovidedVerify.setSupervisionNo(notProvided.getSupervisionNo());
		newUnprovidedVerify.setContractNo(notProvided.getContractNo());
		newUnprovidedVerify.setParcelNum(notProvided.getParcelNum());
		newUnprovidedVerify.setLandUse(notProvided.getLandUse());
		newUnprovidedVerify.setUnprovidedId(notProvided.getId());
		newUnprovidedVerify.setLandArea(notProvided.getProvidedArea());
		newUnprovidedVerify.setDeleteFlag("0");
		newUnprovidedVerify.setStatus("0");
	}

	@ApiOperation("根据巡查任务编号获取核查结果")
	@Log(title = "巡查任务")
	@PostMapping("/getVerifyData")
	public AjaxResult getIdleVerifyByTaskId(String taskId) {
		// step.1 先根据任务编号获取对应的巡查任务判断是闲置土地还是批而未供
		DataPatrolTask task = iDataPatrolTaskService.getById(taskId);
		if (task == null) return AjaxResult.error("根据任务编号没有找到对应的巡查任务");
		//if (task.getProjectType().equals("0")) {
		//	// 闲置土地
		//	DataIdleVerify idleVerify = idleVerifyService.getOne(new LambdaQueryWrapper<DataIdleVerify>()
		//		.eq(DataIdleVerify::getTaskId, taskId), false);
		//	return AjaxResult.success("成功获取数据", idleVerify);
		//} else if (task.getProjectType().equals("1")) {
		//	// 批而未供
		//	DataUnprovidedVerify unprovidedVerify = unprovidedVerifyService.getOne(new LambdaQueryWrapper<DataUnprovidedVerify>()
		//		.eq(DataUnprovidedVerify::getTaskId, taskId), false);
		//	return AjaxResult.success("成功获取数据", unprovidedVerify);
		//} else {
		//	return AjaxResult.error("请检查该任务的项目类型字段是否填写正确");
		//}
		List<DataVerifyRecord> recordList = verifyRecordService.list(new LambdaQueryWrapper<DataVerifyRecord>()
			.eq(DataVerifyRecord :: getTaskId,taskId));
		return AjaxResult.success("成功获取数据", recordList);
	}

	/**
	 * 保存用户审核记录
	 */
	private void saveAuditRecord(String taskId, String isPass, String result) {
		DataAuditRecord record = new DataAuditRecord();
		record.setTaskId(taskId);
		record.setIsPass(isPass);
		record.setFailReason(result);
		record.setRemark(result);
		record.setDeleteFlag("0");
		record.setStatus("0");
		record.setCreateBy(SecurityUtils.getUsername());
		auditRecordService.save(record);
	}

	/**
	 * 获取我的待巡查任务
	 */
	@ApiOperation("获取我的待巡查任务")
	@Log(title = "巡查任务")
	@GetMapping("/getMyUndoTaskList")
	public AjaxResult getMyUndoTaskList(MyTaskQueryBo queryBo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 获取未巡查或正在审核的
			Page<DataPatrolTask> taskPage = getTaskList(sysUser, queryBo, "0");
			if (null != taskPage && taskPage.getRecords().size() != 0) {
				TableDataInfo<DataPatrolTask> tableData = PageUtils.buildDataInfo(taskPage);
				return AjaxResult.success("成功获取我的待巡查任务列表", tableData);
			} else {
				return AjaxResult.success("没有查询到和您相关的任务");
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	/**
	 * 获取我的待审核任务
	 */
	@ApiOperation("获取我的待审核任务")
	@Log(title = "巡查任务")
	@GetMapping("/getMyAuditTaskList")
	public AjaxResult getMyAuditTaskList(MyTaskQueryBo queryBo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			// 获取正在审核的任务列表
			Page<DataPatrolTask> taskPage = getTaskList(sysUser, queryBo, "2");
			if (null != taskPage && taskPage.getRecords().size() != 0) {
				TableDataInfo<DataPatrolTask> tableData = PageUtils.buildDataInfo(taskPage);
				return AjaxResult.success("成功获取我的待审核任务列表", tableData);
			} else {
				return AjaxResult.success("没有查询到和您相关的任务");
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	/**
	 * 获取我的已办任务
	 */
	@ApiOperation("获取我的已办任务")
	@Log(title = "巡查任务")
	@GetMapping("/getMyDoneTaskList")
	public AjaxResult getMyDoneTaskList(MyTaskQueryBo queryBo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			Page<DataPatrolTask> taskPage = getTaskList(sysUser, queryBo, "1");
			if (taskPage != null && taskPage.getRecords().size() != 0) {
				TableDataInfo<DataPatrolTask> tableData = PageUtils.buildDataInfo(taskPage);
				return AjaxResult.success("成功获取我的已办任务列表", tableData);
			} else {
				return AjaxResult.success("没有查询到和您相关的任务");
			}
		} else {
			return AjaxResult.error("您的令牌已过期,请您重新登录");
		}
	}

	private Page<DataPatrolTask> getTaskList(SysUser sysUser,
											 MyTaskQueryBo queryBo,
											 String type) {
		// 根据用户编号去获取对应的巡查任务编号列表
		LambdaQueryWrapper<DataTaskUser> queryWrapper1 = new LambdaQueryWrapper<>();
		queryWrapper1.eq(DataTaskUser::getUserId, sysUser.getUserId());
		List<DataTaskUser> taskUserList = iDataTaskUserService.list(queryWrapper1);
		List<String> taskIdList = new ArrayList<>();
		if (null != taskUserList && taskUserList.size() != 0) {
			for (DataTaskUser taskUser : taskUserList) {
				taskIdList.add(taskUser.getTaskId());
			}
			LambdaQueryWrapper<DataPatrolTask> taskQueryWrapper = new LambdaQueryWrapper<>();
			taskQueryWrapper.in(DataPatrolTask::getId, taskIdList);
			taskQueryWrapper.eq(StringUtils.isNotBlank(queryBo.getProjectType()), DataPatrolTask::getProjectType, queryBo.getProjectType())
				.like(StringUtils.isNotBlank(queryBo.getProjectName()), DataPatrolTask::getProjectName, queryBo.getProjectName())
				.eq(StringUtils.isNotBlank(queryBo.getPatrolType()), DataPatrolTask::getPatrolType, queryBo.getPatrolType())
				.like(StringUtils.isNotBlank(queryBo.getRegionCode()), DataPatrolTask::getRegionCode, queryBo.getRegionCode())
				.like(StringUtils.isNotBlank(queryBo.getRegionName()), DataPatrolTask::getRegionName, queryBo.getRegionName());

			//if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
			//	// 只能获取已经发布到巡查任务
			//	taskQueryWrapper.eq(DataPatrolTask::getPublishStatus, "1");
			//}

			// 根据电子监管号查询
			if (StringUtils.isNotBlank(queryBo.getProjectType()) && queryBo.getProjectType().equals("0")){
				// 闲置土地
				if (StringUtils.isNotBlank(queryBo.getSupervisionNo())){
					// 获取对应的闲置土地信息
					List<DataClgtLand> idleLands = iDataClgtLandService.list(new LambdaQueryWrapper<DataClgtLand>()
						.like(DataClgtLand :: getSupervisionNo,queryBo.getSupervisionNo()));
					if (idleLands.size() != 0){
						List<String> ids = new ArrayList<>();
						for (DataClgtLand idle:idleLands){
							ids.add(idle.getId());
						}
						if (ids.size() != 0){
							taskQueryWrapper.in(DataPatrolTask::getIdleLandId, ids);
						}
					}
				}
			}
//			else if(StringUtils.isNotBlank(queryBo.getProjectType()) && queryBo.getProjectType().equals("1")){
//				if (StringUtils.isNotBlank(queryBo.getSupervisionNo())){
//					// 获取对应的批而未供信息
//					List<DataLandNotProvided> notProvideds = notProvidedService.list(new LambdaQueryWrapper<DataLandNotProvided>()
//						.like(DataLandNotProvided :: getSupervisionNo,queryBo.getSupervisionNo()));
//					if (notProvideds.size() != 0){
//						List<String> ids = new ArrayList<>();
//						for (DataLandNotProvided notProvided:notProvideds){
//							ids.add(notProvided.getId());
//						}
//						if (ids.size() != 0){
//							taskQueryWrapper.in(DataPatrolTask::getIdleLandId, ids);
//						}
//					}
//				}
//			}



			taskQueryWrapper.eq(DataPatrolTask::getPublishStatus, "1");

			if (type.equals("0")) {
				taskQueryWrapper.in(DataPatrolTask::getIsPatrol,Arrays.asList("0","3"));
			}else {
				taskQueryWrapper.in(DataPatrolTask::getIsPatrol,type);
			}
			// 按照时间排序
			taskQueryWrapper.orderByDesc(DataPatrolTask::getCreateTime);
			Page<DataPatrolTask> tempPage = new Page<>(ObjectUtil.isNull(queryBo.getPageNum())?1:queryBo.getPageNum(),ObjectUtil.isNull(queryBo.getPageSize())?10:queryBo.getPageSize());
			Page<DataPatrolTask> data = iDataPatrolTaskService.page(tempPage,taskQueryWrapper);
			List<DataPatrolTask> taskList = data.getRecords();
			// 查询
			if (StringUtils.isNotBlank(type) && type.equals("1")){
				// 查询已完成巡查的需要查询对应的认定处置结果
				for (DataPatrolTask task : taskList){
					DataVerifyRecord verify = verifyRecordService.getOne(new LambdaQueryWrapper< DataVerifyRecord>().eq(DataVerifyRecord :: getTaskId,task.getId()),false);
					if (verify != null){
						task.setHandleIsCorrect(verify.getHandleIsCorrect());
					}
				}
			}

			if (StringUtils.isNotBlank(queryBo.getProjectType()) && queryBo.getProjectType().equals("0")) {
				// 闲置土地
				for (DataPatrolTask task : taskList){
					DataClgtLand idleLand = iDataClgtLandService.getById(task.getIdleLandId());
					if (idleLand != null){
						task.setContractNo(idleLand.getContractNo());
						task.setSupervisionNo(idleLand.getSupervisionNo());
						task.setDigestionType(idleLand.getDigestionType());
						task.setDataClgtLand(idleLand);
						task.setDataHouseCheck(iDataHouseCheckService.selectByTaskId(task.getId()));
					}
					DataVerifyRecord record = verifyRecordService.getOne(new LambdaQueryWrapper<DataVerifyRecord>()
					.eq(DataVerifyRecord :: getTaskId,task.getId()),false);
					if (record != null){
						task.setHasOnlineVideo(record.getVideoIsReal());
					}
				}
			}
//			else if (StringUtils.isNotBlank(queryBo.getProjectType()) && queryBo.getProjectType().equals("1")){
//				// 批而未供
//				for (DataPatrolTask task : taskList){
//					DataLandNotProvided notProvided = notProvidedService.getById(task.getIdleLandId());
//					if (notProvided != null){
//						task.setContractNo(notProvided.getContractNo());
//						task.setSupervisionNo(notProvided.getSupervisionNo());
//						task.setDigestionType("批而未供");
//					}
//				}
//			}

			return data;
		} else {
			return null;
		}
	}

	/**
	 * 导出巡查任务 列表
	 */
	@ApiOperation("导出巡查任务列表")
	@Log(title = "巡查任务", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataPatrolTask> export(@Validated DataPatrolTaskQueryBo bo) {
		List<DataPatrolTask> list = iDataPatrolTaskService.queryList(bo);
		ExcelUtil<DataPatrolTask> util = new ExcelUtil<DataPatrolTask>(DataPatrolTask.class);
		return util.exportExcel(list, "巡查任务");
	}

	/**
	 * 获取巡查任务 详细信息
	 */
	@ApiOperation("根据Id获取巡查任务")
	@GetMapping("/{id}")
	public AjaxResult<DataPatrolTask> getInfo(@NotNull(message = "主键不能为空")
											  @PathVariable("id") String id) {
		return AjaxResult.success(iDataPatrolTaskService.queryById(id));
	}

	/**
	 *村级任务总数，未提交，已提交，未巡查任务数
	 */
	@ApiOperation("村级任务总数，未提交，已提交，未巡查任务数")
	@GetMapping("countByPatrol/{PId}")
	public AjaxResult countByPatrol(@PathVariable("PId") String PId) {
		return AjaxResult.success(iDataPatrolTaskService.countByPatrol(PId));
	}

	/**
	 * 修改任务状态
	 */
	@ApiOperation("修改任务状态")
	@PutMapping()
	public AjaxResult edit(@RequestBody DataPatrolTaskQueryBo bo) {
		return AjaxResult.success(iDataPatrolTaskService.updateById(bo));
	}

}
