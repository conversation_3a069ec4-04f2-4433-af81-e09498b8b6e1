package com.ruoyi.idle.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 房屋建筑信息外业调查 分页查询对象 data_house_check
 *
 * <AUTHOR>
 * @date 2025-04-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("房屋建筑信息外业调查 分页查询对象")
public class DataHouseCheckQueryBo extends BaseEntity {

	/**
	 * 分页大小
	 */
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	/**
	 * 当前页数
	 */
	@ApiModelProperty("当前页数")
	private Integer pageNum;
	/**
	 * 排序列
	 */
	@ApiModelProperty("排序列")
	private String orderByColumn;
	/**
	 * 排序的方向desc或者asc
	 */
	@ApiModelProperty(value = "排序的方向", example = "asc,desc")
	private String isAsc;


	/**
	 * 建筑编号
	 */
	@ApiModelProperty("建筑编号")
	private String jzbh;
	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String mc;
	/**
	 * 基底面积(平方米)
	 */
	@ApiModelProperty("基底面积(平方米)")
	private BigDecimal jdmj;
	/**
	 * 地上层数
	 */
	@ApiModelProperty("地上层数")
	private Long dscs;
	/**
	 * 地上建筑面积(平方米)
	 */
	@ApiModelProperty("地上建筑面积(平方米)")
	private BigDecimal dsjzmj;
	/**
	 * 建筑高度(米)
	 */
	@ApiModelProperty("建筑高度(米)")
	private BigDecimal jzgd;
	/**
	 * 异形建筑
	 */
	@ApiModelProperty("异形建筑")
	private String yxjz;
	/**
	 * 房屋套数
	 */
	@ApiModelProperty("房屋套数")
	private Long fwts;
	/**
	 * 详细地址
	 */
	@ApiModelProperty("详细地址")
	private String xxdz;
	/**
	 * 建筑年代
	 */
	@ApiModelProperty("建筑年代")
	private Date jznd;
	/**
	 * 调查时间
	 */
	@ApiModelProperty("调查时间")
	private Date dcsj;
	/**
	 * 建筑状态
	 */
	@ApiModelProperty("建筑状态")
	private String jzzt;
	/**
	 * 结构类型
	 */
	@ApiModelProperty("结构类型")
	private String jglx;
	/**
	 * 实际用途
	 */
	@ApiModelProperty("实际用途")
	private String sjyt;
	/**
	 * 规划用途
	 */
	@ApiModelProperty("规划用途")
	private String ghyt;
	/**
	 * 竣工用途
	 */
	@ApiModelProperty("竣工用途")
	private String jgyt;
	/**
	 * 建筑分层用途
	 */
	@ApiModelProperty("建筑分层用途")
	private String jzfcyt;
	/**
	 * 分用途楼层段
	 */
	@ApiModelProperty("分用途楼层段")
	private String fytlcd;
	/**
	 * 分用途建筑面积(平方米)
	 */
	@ApiModelProperty("分用途建筑面积(平方米)")
	private BigDecimal fytjzmj;
	/**
	 * 地下层数
	 */
	@ApiModelProperty("地下层数")
	private String dxcs;
	/**
	 * 地下建筑面积(平方米)
	 */
	@ApiModelProperty("地下建筑面积(平方米)")
	private BigDecimal dxjzmj;
	/**
	 * 房屋性质
	 */
	@ApiModelProperty("房屋性质")
	private String fwxz;
	/**
	 * 闲置时间
	 */
	@ApiModelProperty("闲置时间")
	private String xzsj;
	/**
	 * 用地所有权
	 */
	@ApiModelProperty("用地所有权")
	private String ydsyq;
	/**
	 * 填写说明
	 */
	@ApiModelProperty("填写说明")
	private String txsm;
	/**
	 * 删除标志
	 */
	@ApiModelProperty("删除标志")
	private String deleteFlag;

}
