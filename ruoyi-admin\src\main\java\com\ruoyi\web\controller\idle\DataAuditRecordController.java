package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;

import com.ruoyi.idle.domain.DataAuditRecord;
import com.ruoyi.idle.domain.bo.DataAuditRecordQueryBo;
import com.ruoyi.idle.service.IDataAuditRecordService;

import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 审核记录 Controller
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
@Api(value = "审核记录 控制器", tags = {"审核记录 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/audit/record")
public class DataAuditRecordController extends BaseController {

	private final IDataAuditRecordService iDataAuditRecordService;

	/**
	 * 查询审核记录 列表
	 */
	@ApiOperation("查询审核记录 列表")
	//@PreAuthorize("@ss.hasPermi('idle:record:list')")
	@GetMapping("/list")
	public TableDataInfo<DataAuditRecord> list(@Validated DataAuditRecordQueryBo bo) {
		return iDataAuditRecordService.queryPageList(bo);
	}

	/**
	 * 获取审核记录 详细信息
	 */
	@ApiOperation("获取审核记录 详细信息")
	//@PreAuthorize("@ss.hasPermi('idle:record:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataAuditRecord> getInfo(@NotNull(message = "主键不能为空")
											   @PathVariable("id") String id) {
		return AjaxResult.success(iDataAuditRecordService.queryById(id));
	}
}
