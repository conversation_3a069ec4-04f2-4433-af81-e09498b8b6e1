package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 闲置土地 对象 data_idle_land
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_idle_land")
public class DataIdleLand implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 闲置土地所在市
	 */
	@ApiModelProperty(value = "闲置土地所在市")
	@Excel(name = "州市")
	private String city;

	/**
	 * 闲置土地所在县代码
	 */
	@ApiModelProperty(value = "闲置土地所在县代码")
	private String countyCode;

	/**
	 * 闲置土地所在县名称
	 */
	@ApiModelProperty(value = "闲置土地所在县名称")
	@Excel(name = "区县")
	private String countyName;

	/**
	 * 合同编号
	 */
	@ApiModelProperty(value = "合同编号")
	@Excel(name = "合同编号")
	private String contractNo;

	/**
	 * 供应方式
	 */
	@ApiModelProperty(value = "供应方式")
	@Excel(name = "供应方式")
	private String supplyType;

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	@Excel(name = "项目名称")
	private String projectName;

	/**
	 * 数据年度
	 */
	@ApiModelProperty(value = "数据年度")
	private String year;

	/**
	 * 数据季度
	 */
	@ApiModelProperty(value = "数据季度")
	private String quarter;

	/**
	 * 电子监管号
	 */
	@ApiModelProperty(value = "电子监管号")
	@Excel(name = "电子监管号")
	private String supervisionNo;

	/**
	 * 土地用途
	 */
	@ApiModelProperty(value = "土地用途")
	private String landUse;

	/**
	 * 土地面积（公顷）
	 */
	@ApiModelProperty(value = "土地面积")
	@Excel(name = "供应面积")
	private BigDecimal landArea;

	/**
	 * 签订日期
	 */
	@ApiModelProperty(value = "签订日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date signDate;

	/**
	 * 约定动工时间
	 */
	@ApiModelProperty(value = "约定动工时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date agreedStartTime;

	/**
	 * 实际动工时间
	 */
	@ApiModelProperty(value = "实际动工时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date actualStartTime;

	/**
	 * 实际竣工时间
	 */
	@ApiModelProperty(value = "实际竣工时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date actualEndTime;

	/**
	 * 闲置状态
	 */
	@ApiModelProperty(value = "闲置状态")
	private String idleStatus;

	/**
	 * 图斑编号
	 */
	@ApiModelProperty(value = "图斑编号")
	private String spotNumber;

	/**
	 * 内业备注
	 */
	@ApiModelProperty(value = "内业备注")
	private String insideRemark;

	/**
	 * 消化类型
	 */
	@ApiModelProperty(value = "消化类型")
	private String digestionType;

	/**
	 * 项目详细位置
	 */
	@ApiModelProperty(value = "项目详细位置")
	private String address;

	/**
	 * 项目空间数据
	 */
	@ApiModelProperty(value = "项目空间数据")
	private String geoData;

	/**
	 * 宗地号
	 */
	@ApiModelProperty(value = "宗地号")
	@Excel(name = "宗地号")
	private String parcelNum;

	/**
	 * 项目中心点经度
	 */
	@ApiModelProperty(value = "项目中心点经度")
	private String longitude;

	/**
	 * 项目中心点纬度
	 */
	@ApiModelProperty(value = "项目中心点纬度")
	private String latitude;

	/**
	 * 是否已分配巡查任务
	 */
	@ApiModelProperty(value = "是否已分配巡查任务")
	private String isAllot;

	/**
	 * 不动产权证号
	 */
	@ApiModelProperty(value = "不动产权证号")
	@Excel(name = "不动产权证号")
	private String warrantNum;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 删除标记
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 任务是否已认定完成
	 */
	@ApiModelProperty(value = "任务是否已认定完成")
	@TableField(exist = false)
	private String isPatrol;

}
