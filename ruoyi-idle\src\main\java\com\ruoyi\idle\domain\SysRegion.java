package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 中国行政区对象 sys_region
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_region")
public class SysRegion implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级")
	private String level;

	/**
	 * 父级编号
	 */
	@ApiModelProperty(value = "父级编号")
	private String parentId;

	/**
	 * 父级行政代码
	 */
	@ApiModelProperty(value = "父级行政代码")
	private String parentCode;

	/**
	 * 行政代码
	 */
	@ApiModelProperty(value = "行政代码")
	private String areaCode;

	/**
	 * 邮政编码
	 */
	@ApiModelProperty(value = "邮政编码")
	private String zipCode;

	/**
	 * 区号
	 */
	@ApiModelProperty(value = "区号")
	private String cityCode;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 简称
	 */
	@ApiModelProperty(value = "简称")
	private String shortName;

	/**
	 * 组合名
	 */
	@ApiModelProperty(value = "组合名")
	private String mergerName;

	/**
	 * 拼音
	 */
	@ApiModelProperty(value = "拼音")
	private String pinyin;

	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private BigDecimal lng;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private BigDecimal lat;

	/**
	 * 是否可用默认1
	 */
	@ApiModelProperty(value = "是否可用", hidden = true)
	private String enabled;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 删除标记默认0
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

}
