<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataFileCatalogMapper">

    <resultMap type="com.ruoyi.idle.domain.DataFileCatalog" id="DataFileCatalogResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="catalogType" column="catalog_type"/>
        <result property="orderNum" column="order_num"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
