package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataPatrolGroup;
import com.ruoyi.idle.domain.bo.DataPatrolGroupQueryBo;
import com.ruoyi.idle.mapper.DataPatrolGroupMapper;
import com.ruoyi.idle.service.IDataPatrolGroupService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 巡查组 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataPatrolGroupServiceImpl extends ServicePlusImpl<DataPatrolGroupMapper, DataPatrolGroup> implements IDataPatrolGroupService {

	@Override
	public DataPatrolGroup queryById(String id) {
		return getVoById(id, DataPatrolGroup.class);
	}

	@Override
	public String getGroupNameById(String id) {
		DataPatrolGroup group = queryById(id);
		return group == null ? null : group.getGroupName();
	}

	@Override
	public TableDataInfo<DataPatrolGroup> queryPageList(DataPatrolGroupQueryBo bo) {
		PagePlus<DataPatrolGroup, DataPatrolGroup> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataPatrolGroup.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataPatrolGroup> queryList(DataPatrolGroupQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataPatrolGroup.class);
	}

	private LambdaQueryWrapper<DataPatrolGroup> buildQueryWrapper(DataPatrolGroupQueryBo bo) {
		LambdaQueryWrapper<DataPatrolGroup> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataPatrolGroup::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataPatrolGroup::getCountyName, bo.getCountyName());
		lqw.like(StrUtil.isNotBlank(bo.getGroupName()), DataPatrolGroup::getGroupName, bo.getGroupName());
		lqw.eq(StrUtil.isNotBlank(bo.getGroupAuthor()), DataPatrolGroup::getGroupAuthor, bo.getGroupAuthor());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataPatrolGroup::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataPatrolGroup::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataPatrolGroup::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataPatrolGroup::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataPatrolGroup::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insert(DataPatrolGroup bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataPatrolGroup bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataPatrolGroup entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public List<DataPatrolGroup> getAllPatrolGroupList(String startTime) {
		return baseMapper.selectAllPatrolGroupList(startTime);
	}
}
