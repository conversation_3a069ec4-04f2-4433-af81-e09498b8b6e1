package com.ruoyi.socket;


import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.spring.SpringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Future;

@Component
@ServerEndpoint(value = "/wsocket/{userId}")
public class WebSocket {

	private static final Logger log = LoggerFactory.getLogger("WebSocket");

	private static int OnlineCount = 0;
	private static ConcurrentHashMap<String, WebSocket> rocketMap = new ConcurrentHashMap<>();

	// 记录空闲Session集合
	private static CopyOnWriteArraySet<Session> idle = new CopyOnWriteArraySet<Session>();
	// 记录正在使用中Session集合，value为Future，表示使用情况
	private static ConcurrentHashMap<Session, Future<Void>> busy = new ConcurrentHashMap<Session, Future<Void>>();

	private String userId = "";
	private Session session;

	@OnOpen
	public void onOpen(Session session, @PathParam(value = "userId") String userId, EndpointConfig config) {
		if (userId != null) {

			this.session = session;
			this.userId = userId;
			idle.add(session);
			if (rocketMap.containsKey(userId)) {

				WebSocket ws = rocketMap.get(userId);
				if (ws != null && ws.session != null) {
					if (ws.session.isOpen()) {
						try {
							ws.session.close();
						} catch (Exception ex) {
							log.error("open new socket,old close error ." + ex.getMessage());
						}
					}
				}

				rocketMap.remove(userId);
				rocketMap.put(userId, this);
				//加入set中
			} else {
				rocketMap.put(userId, this);
				//加入set中
				addOnlineCount();
				//在线数加1
			}
			MeetStatusManager.notifyUserForOpen(userId);

			log.info("rocket user:" + userId + ", count:" + getOnlineCount());
		} else {
			if (session != null) {
//                CloseReason cr=new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT,"need login");
				try {
					log.error("close not login session");
					session.close();
				} catch (IOException ioe) {
					log.error("close not login session error " + ioe.getMessage());
				}
			}

			log.info("rocket user:" + userId + ", 无法连接未登录.");
		}
	}

	@OnError
	public void OnError(Session session, Throwable t) {

		System.out.println("WebSocket may be client Close:\n" + t.getMessage());
		//t.p

	}

	@OnClose
	public void onClose() {

		idle.remove(session);
		busy.remove(session);

		if (userId == null)
			return;
		if (rocketMap.containsKey(userId)) {
			rocketMap.remove(userId);
			//从set中删除
			subOnlineCount();
		}
		log.info("user close:" + userId + ",connected count:" + getOnlineCount());
	}

	@OnMessage
	public void onMessage(String message) {

		if (StringUtils.isBlank(message))
			return;
		message = message.trim();
		if (message.indexOf("ping") == 0) {
			System.out.println("receive ping Message: " + message);

			return;
		}
		System.out.println("onMessage: " + message);
		JSONObject objJson = JSONObject.parseObject(message);
		if (objJson == null || objJson.isEmpty()) {
			System.out.println("Empty Json Message: " + message);

			return;
		}
		JSONObject objJson2 = objJson.getJSONObject("nameValuePairs");
		if (objJson2 != null) {
			objJson = objJson2;
		}
		Integer code = objJson.getInteger("code");
		if (code >= 101 && code < 199) {
			//meet
			MeetStatusManager.DealClientMsg(objJson);
		}
	}

	public static void sendInfoWithRedis(String userid, JSONObject jsonObj) {
		if (jsonObj == null)
			return;
		if (StringUtils.isBlank(userid))
			return;
		if (rocketMap == null || rocketMap.size() < 1) {
			return;
		}
//        log.info("send to:"+userid+"，message:"+message);
		if (StringUtils.isNotBlank(userid) && rocketMap.containsKey(userid)) {
			WebSocket socket = rocketMap.get(userid);
			if (socket != null) {
				socket.sendMessage(jsonObj.toJSONString());
			}
		} else if (StringUtils.isNotBlank(userid)) {
//不在本服务器
			jsonObj.put("toOtherServerUser", userid);
			StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
			stringRedisTemplate.convertAndSend("chat", jsonObj.toJSONString());
		}
	}

	public static void sendInfo(String userid, JSONObject jsonObj) {
		if (jsonObj == null)
			return;
		if (StringUtils.isBlank(userid))
			return;
		if (rocketMap == null || rocketMap.size() < 1) {
			return;
		}
		log.info("send to:" + userid + "，message:" + jsonObj.toJSONString());
		if (StringUtils.isNotBlank(userid) && rocketMap.containsKey(userid)) {
			WebSocket socket = rocketMap.get(userid);
			if (socket != null) {
				socket.sendMessage(jsonObj.toJSONString());
				log.info("sented :" + userid + "，message:" + jsonObj.toJSONString());
			}
		}

	}

	// 此为广播消息
	public static void sendAllMessage(String message) {
		if (StringUtils.isBlank(message))
			return;

//        log.info("【websocket消息】广播消息:" + message);
		for (Map.Entry<String, WebSocket> entry : rocketMap.entrySet()) {
			String userid = entry.getKey();
			WebSocket socket = entry.getValue();
			if (socket != null) {
				try {
					socket.sendMessage(message);

				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	int timeout = 3000;

	public void sendMessage(String message) {
		if (StringUtils.isBlank(message))
			return;

		if (timeout < 0) { // timeout后放弃本次发送
			return;
		}
		if (idle.remove(session)) { // 判断session是否空闲，抢占式

			busy.put(session, session.getAsyncRemote().sendText(message));

		} else {
			// 若session当前不在idle集合，则去busy集合中查看session上次是否已经发送完毕，即采用惰性判断
			synchronized (busy) {
				if (busy.containsKey(session) && busy.get(session).isDone()) {
					busy.remove(session);
					idle.add(session);
				}
			}
			// 重试
			// 重试
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			sendMessage(message, timeout - 100);
		}


	}

	public void sendMessage(String message, int timeOut) {
		if (StringUtils.isBlank(message))
			return;

		if (timeOut < 0) { // timeout后放弃本次发送
			return;
		}
		if (idle.remove(session)) { // 判断session是否空闲，抢占式

			busy.put(session, session.getAsyncRemote().sendText(message));

		} else {
			// 若session当前不在idle集合，则去busy集合中查看session上次是否已经发送完毕，即采用惰性判断
			synchronized (busy) {
				if (busy.containsKey(session) && busy.get(session).isDone()) {
					busy.remove(session);
					idle.add(session);
				}
			}
			// 重试
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			sendMessage(message, timeOut - 100);
		}


	}

	public static synchronized int getOnlineCount() {
		return OnlineCount;
	}

	public static synchronized void addOnlineCount() {
		OnlineCount = OnlineCount + 1;
	}

	public static synchronized void subOnlineCount() {
		OnlineCount = OnlineCount - 1;
	}
}
