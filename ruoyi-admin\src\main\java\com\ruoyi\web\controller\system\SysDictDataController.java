package com.ruoyi.web.controller.system;

import cn.hutool.core.lang.Validator;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@Api(value = "数据字典管理", tags = {"数据字典管理"})
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {
	@Autowired
	private ISysDictDataService dictDataService;

	@Autowired
	private ISysDictTypeService dictTypeService;

	//@PreAuthorize("@ss.hasPermi('system:dict:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysDictData dictData) {
		return dictDataService.selectPageDictDataList(dictData);
	}

	@Log(title = "字典数据", businessType = BusinessType.EXPORT)
	//@PreAuthorize("@ss.hasPermi('system:dict:export')")
	@GetMapping("/export")
	public AjaxResult export(SysDictData dictData) {
		List<SysDictData> list = dictDataService.selectDictDataList(dictData);
		ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
		return util.exportExcel(list, "字典数据");
	}

	/**
	 * 查询字典数据详细
	 */
	//@PreAuthorize("@ss.hasPermi('system:dict:query')")
	@GetMapping(value = "/{dictCode}")
	public AjaxResult getInfo(@PathVariable String dictCode) {
		return AjaxResult.success(dictDataService.selectDictDataById(dictCode));
	}

	/**
	 * 根据字典类型查询字典数据信息
	 */
	@ApiOperation("根据字典类型查询字典数据")
	@GetMapping(value = "/type/{dictType}")
	public AjaxResult dictType(@PathVariable String dictType) {
		List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
		if (Validator.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success(data);
	}

	/**
	 * 新增字典类型
	 */
	//@PreAuthorize("@ss.hasPermi('system:dict:add')")
	@Log(title = "字典数据", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@Validated @RequestBody SysDictData dict) {
		dict.setCreateBy(SecurityUtils.getUsername());
		return toAjax(dictDataService.insertDictData(dict));
	}

	/**
	 * 修改保存字典类型
	 */
	//@PreAuthorize("@ss.hasPermi('system:dict:edit')")
	@Log(title = "字典数据", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@Validated @RequestBody SysDictData dict) {
		dict.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(dictDataService.updateDictData(dict));
	}

	/**
	 * 删除字典类型
	 */
	//@PreAuthorize("@ss.hasPermi('system:dict:remove')")
	@Log(title = "字典类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dictCodes}")
	public AjaxResult remove(@PathVariable String[] dictCodes) {
		dictDataService.deleteDictDataByIds(dictCodes);
		return success();
	}
}
