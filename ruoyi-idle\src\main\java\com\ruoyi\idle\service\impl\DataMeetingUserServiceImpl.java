package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataMeetingUser;
import com.ruoyi.idle.domain.bo.DataMeetingUserQueryBo;
import com.ruoyi.idle.mapper.DataMeetingUserMapper;
import com.ruoyi.idle.service.IDataMeetingUserService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 与会人员 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
@Service
public class DataMeetingUserServiceImpl extends ServicePlusImpl<DataMeetingUserMapper, DataMeetingUser> implements IDataMeetingUserService {

	@Override
	public DataMeetingUser queryById(String id) {
		return getVoById(id, DataMeetingUser.class);
	}

	@Override
	public TableDataInfo<DataMeetingUser> queryPageList(DataMeetingUserQueryBo bo) {
		PagePlus<DataMeetingUser, DataMeetingUser> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataMeetingUser.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataMeetingUser> queryList(DataMeetingUserQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataMeetingUser.class);
	}

	private LambdaQueryWrapper<DataMeetingUser> buildQueryWrapper(DataMeetingUser bo) {
		LambdaQueryWrapper<DataMeetingUser> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getMeetingId() != null, DataMeetingUser::getMeetingId, bo.getMeetingId());
		lqw.eq(bo.getUserId() != null, DataMeetingUser::getUserId, bo.getUserId());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataMeetingUser::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataMeetingUser::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataMeetingUser::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataMeetingUser::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataMeetingUser bo) {
		DataMeetingUser add = BeanUtil.toBean(bo, DataMeetingUser.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataMeetingUser bo) {
		DataMeetingUser update = BeanUtil.toBean(bo, DataMeetingUser.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataMeetingUser entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
