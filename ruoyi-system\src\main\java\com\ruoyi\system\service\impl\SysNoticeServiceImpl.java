package com.ruoyi.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.mapper.SysNoticeMapper;
import com.ruoyi.system.service.ISysNoticeService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl extends ServicePlusImpl<SysNoticeMapper, SysNotice> implements ISysNoticeService {

	@Override
	public TableDataInfo<SysNotice> selectPageNoticeList(SysNotice notice) {
		LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<SysNotice>()
			.like(StrUtil.isNotBlank(notice.getNoticeTitle()), SysNotice::getNoticeTitle, notice.getNoticeTitle())
			.eq(StrUtil.isNotBlank(notice.getNoticeType()), SysNotice::getNoticeType, notice.getNoticeType())
			.like(StrUtil.isNotBlank(notice.getCreateBy()), SysNotice::getCreateBy, notice.getCreateBy());
		return PageUtils.buildDataInfo(page(PageUtils.buildPage(), lqw));
	}

	/**
	 * 查询公告信息
	 *
	 * @param noticeId 公告ID
	 * @return 公告信息
	 */
	@Override
	public SysNotice selectNoticeById(String noticeId) {
		return getById(noticeId);
	}

	/**
	 * 查询公告列表
	 *
	 * @param notice 公告信息
	 * @return 公告集合
	 */
	@Override
	public List<SysNotice> selectNoticeList(SysNotice notice) {
		return list(new LambdaQueryWrapper<SysNotice>()
			.like(StrUtil.isNotBlank(notice.getNoticeTitle()), SysNotice::getNoticeTitle, notice.getNoticeTitle())
			.eq(StrUtil.isNotBlank(notice.getNoticeType()), SysNotice::getNoticeType, notice.getNoticeType())
			.like(StrUtil.isNotBlank(notice.getCreateBy()), SysNotice::getCreateBy, notice.getCreateBy()));
	}

	/**
	 * 新增公告
	 *
	 * @param notice 公告信息
	 * @return 结果
	 */
	@Override
	public int insertNotice(SysNotice notice) {
		return baseMapper.insert(notice);
	}

	/**
	 * 修改公告
	 *
	 * @param notice 公告信息
	 * @return 结果
	 */
	@Override
	public int updateNotice(SysNotice notice) {
		return baseMapper.updateById(notice);
	}

	/**
	 * 删除公告对象
	 *
	 * @param noticeId 公告ID
	 * @return 结果
	 */
	@Override
	public int deleteNoticeById(String noticeId) {
		return baseMapper.deleteById(noticeId);
	}

	/**
	 * 批量删除公告信息
	 *
	 * @param noticeIds 需要删除的公告ID
	 * @return 结果
	 */
	@Override
	public int deleteNoticeByIds(String[] noticeIds) {
		return baseMapper.deleteBatchIds(Arrays.asList(noticeIds));
	}
}
