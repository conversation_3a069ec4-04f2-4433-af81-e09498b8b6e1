package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataLandNotProvided;
import com.ruoyi.idle.domain.bo.DataLandNotProvidedQueryBo;
import com.ruoyi.idle.domain.vo.StatisticsVo;

import java.util.Collection;
import java.util.List;

/**
 * 批而未供Service接口
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
public interface IDataLandNotProvidedService extends IServicePlus<DataLandNotProvided> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataLandNotProvided queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataLandNotProvided> queryPageList(DataLandNotProvidedQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataLandNotProvided> queryList(DataLandNotProvidedQueryBo bo);

	/**
	 * 根据新增业务对象插入批而未供
	 *
	 * @param bo 批而未供新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataLandNotProvided bo);

	/**
	 * 根据编辑业务对象修改批而未供
	 *
	 * @param bo 批而未供编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataLandNotProvided bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<StatisticsVo> unprovidedStat(String regionCode);
}
