package com.ruoyi.idle.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Created with IntelliJ IDEA.
 * @Title: MyTaskQueryBo
 * @Description: com.ruoyi.idle.domain.bo
 * @Author: HongDeng
 * @Date: 2021/8/27 15:25
 * @Version: 1.0.0
 **/
@Data
public class MyTaskQueryBo {

	@ApiModelProperty(value = "项目类型")
	private String projectType;
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	@ApiModelProperty(value = "行政区代码")
	private String regionCode;
	@ApiModelProperty(value = "行政区名称")
	private String regionName;
	@ApiModelProperty(value = "行政区名称")
	private String supervisionNo;
	@ApiModelProperty(value = "巡查类型")
	private String patrolType;
	@ApiModelProperty("分页大小")
	private Integer pageSize;
	@ApiModelProperty("当前页数")
	private Integer pageNum;
}
