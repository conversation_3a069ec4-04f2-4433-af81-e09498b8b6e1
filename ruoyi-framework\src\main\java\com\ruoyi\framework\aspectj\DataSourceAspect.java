package com.ruoyi.framework.aspectj;

import cn.hutool.core.lang.Validator;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.ruoyi.common.annotation.DataSource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 多数据源处理
 *
 * <AUTHOR>
 */
@Aspect
@Order(-500)
@Component
public class DataSourceAspect {

	@Value("${spring.datasource.dynamic.datasource.master.url:}")
	private String masterUrl;

	@Value("${spring.datasource.dynamic.datasource.slave.url:}")
	private String slaveUrl;

	@Pointcut("@annotation(com.ruoyi.common.annotation.DataSource)"
		+ "|| @within(com.ruoyi.common.annotation.DataSource)")
	public void dsPointCut() {
	}

	@Around("dsPointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		DataSource dataSource = getDataSource(point);

		if (Validator.isNotNull(dataSource)) {
			DynamicDataSourceContextHolder.poll();
			String source = dataSource.value().getSource();
			DynamicDataSourceContextHolder.push(source);
		}

		try {
			return point.proceed();
		} finally {
			// 销毁数据源 在执行方法之后
			DynamicDataSourceContextHolder.clear();
		}
	}

	@Pointcut(" !@annotation(org.springframework.transaction.annotation.Transactional) "
		+"&& (execution(* *..service.*.select*(..))"
		+"|| execution(* *..service.*.getAll*(..))" +
		"|| execution(* *..service.*.get*(..)) " +
		"|| execution(* *..service.*.list*(..)) " +
		"|| execution(* *..service.*.count*(..)) " +
		"|| execution(* *..service.*.page*(..)) " +
		"|| execution(* *..service.*.query*(..)) " +
		")")
	public void dsPointCutREAD() {

	}
	@Around("dsPointCutREAD()")
	public Object aroundREAD(ProceedingJoinPoint point) throws Throwable {

		String source="slave";
		if(this.slaveUrl!=null && slaveUrl.length()>9){
			source="slave";
		}else{
			source="master";
		}
		/*
		javax.sql.DataSource dataSource =dynamicDSConfig.dataSource();



		if (dataSource instanceof DynamicRoutingDataSource){
			DynamicRoutingDataSource currentDS = (DynamicRoutingDataSource)dataSource;

			javax.sql.DataSource s1=currentDS.getDataSource(source);

			if(s1 instanceof DruidDataSource){
				DruidDataSource ds1 = (DruidDataSource) s1;

			}
			if(s1==null){
				source="master";
				System.out.println("READ USE master Datasource");
			}else{
				source="slave";
				System.out.println("READ USE slave Datasource");
			}
			//dbStr是自己要改动的数据源

		}

		source="slave";
		*/
		System.out.println("READ USE "+source+" Datasource");

		if (Validator.isNotNull(source)) {
			DynamicDataSourceContextHolder.poll();

			DynamicDataSourceContextHolder.push(source);
		}

		try {
			return point.proceed();
		} finally {
			// 销毁数据源 在执行方法之后
			DynamicDataSourceContextHolder.clear();
		}
	}

	@Pointcut("@annotation(org.springframework.transaction.annotation.Transactional)"
		+"")
	public void dsPointCutWrite() {

	}
	@Around("dsPointCutWrite()")
	public Object aroundWrite(ProceedingJoinPoint point) throws Throwable {

//		javax.sql.DataSource dataSource =dynamicDSConfig.dataSource();
//
//		if (dataSource instanceof DynamicRoutingDataSource){
//			DynamicRoutingDataSource currentDS = (DynamicRoutingDataSource)dataSource;
//
//			currentDS.getDataSource("");
//			//dbStr是自己要改动的数据源
//
//		}
		System.out.println("Transactional OR WRITE master Datasource");
		String source="master";

		if (Validator.isNotNull(source)) {
			DynamicDataSourceContextHolder.poll();

			DynamicDataSourceContextHolder.push(source);
		}

		try {
			return point.proceed();
		} finally {
			// 销毁数据源 在执行方法之后
			DynamicDataSourceContextHolder.clear();
		}
	}
	/**
	 * 获取需要切换的数据源
	 */
	public DataSource getDataSource(ProceedingJoinPoint point) {
		MethodSignature signature = (MethodSignature) point.getSignature();
		DataSource dataSource = AnnotationUtils.findAnnotation(signature.getMethod(), DataSource.class);
		if (Objects.nonNull(dataSource)) {
			return dataSource;
		}

		return AnnotationUtils.findAnnotation(signature.getDeclaringType(), DataSource.class);
	}
}
