package com.ruoyi.idle.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.idle.domain.DataTaskUser;
import com.ruoyi.idle.domain.bo.DataTaskUserQueryBo;
import com.ruoyi.idle.mapper.DataTaskUserMapper;
import com.ruoyi.idle.service.IDataTaskUserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;

/**
 * 巡查任务用户关系 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataTaskUserServiceImpl extends ServicePlusImpl<DataTaskUserMapper, DataTaskUser> implements IDataTaskUserService {

	@Override
	public DataTaskUser queryById(String id) {
		return getVoById(id, DataTaskUser.class);
	}

	@Override
	public DataTaskUser queryByTaskIdAndUserId(String taskId, String userId) {
		return baseMapper.selectByTaskIdAndUserId(taskId, userId);
	}

	@Override
	public TableDataInfo<DataTaskUser> queryPageList(DataTaskUserQueryBo bo) {
		PagePlus<DataTaskUser, DataTaskUser> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataTaskUser.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataTaskUser> queryList(DataTaskUserQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataTaskUser.class);
	}

	private LambdaQueryWrapper<DataTaskUser> buildQueryWrapper(DataTaskUserQueryBo bo) {
		LambdaQueryWrapper<DataTaskUser> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getTaskId() != null, DataTaskUser::getTaskId, bo.getTaskId());
		lqw.eq(bo.getUserId() != null, DataTaskUser::getUserId, bo.getUserId());
		lqw.like(StrUtil.isNotBlank(bo.getUserName()), DataTaskUser::getUserName, bo.getUserName());
		lqw.eq(StrUtil.isNotBlank(bo.getUserPhone()), DataTaskUser::getUserPhone, bo.getUserPhone());
		lqw.eq(StrUtil.isNotBlank(bo.getIsLeader()), DataTaskUser::getIsLeader, bo.getIsLeader());
		lqw.eq(StrUtil.isNotBlank(bo.getBusType()), DataTaskUser::getBusType, bo.getBusType());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataTaskUser::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataTaskUser::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataTaskUser::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataTaskUser::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataTaskUser::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insert(DataTaskUser bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataTaskUser bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataTaskUser entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
