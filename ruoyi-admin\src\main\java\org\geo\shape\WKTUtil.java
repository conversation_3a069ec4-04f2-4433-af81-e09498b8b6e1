package org.geo.shape;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;

import java.io.StringWriter;

@Slf4j
public class WKTUtil {
    /**
     * 由wkt格式的geometry生成geojson
     * @param wkt
     * @return
     */
    public static JSONObject wktToJson(String wkt) {
        String json = null;

        try {
            WKTReader reader = new WKTReader();
            Geometry geometry = reader.read(wkt);
            StringWriter writer = new StringWriter();
            GeometryJSON g = new GeometryJSON(20);
            g.write(geometry, writer);
            json = writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(json);
        return jsonObject1;
    }

	/**
	 * 由wkt格式的geometry生成geojson
	 * @param wkt
	 * @return
	 */
	public static String wktToJsonStr(String wkt) {
		String json = null;

		try {
			WKTReader reader = new WKTReader();
			Geometry geometry = reader.read(wkt);
			StringWriter writer = new StringWriter();
			GeometryJSON g = new GeometryJSON(20);
			g.write(geometry, writer);
			json = writer.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		//JSONObject jsonObject1 = JSONObject.parseObject(json);
		return json;
	}
}
