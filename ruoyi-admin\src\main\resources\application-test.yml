# 项目相关配置
ruoyi:
  # 名称
  name: idle-patrol
  # 版本
  version: 2.4.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径
  profile: E:\\idle\\backend\\upload
  # 获取ip地址开关
  addressEnabled: true

server:
  # 服务器的HTTP端口，生产环境为8130端口 记得修改前端
  port: 8998

# 日志配置
logging:
  level:
    com.ruoyi: warn
    org.springframework: warn
  config: classpath:logback.xml

# 数据源配置
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      #设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: Zlzxrjyf12#$
        # 从库数据源
    #        slave:
    #          driverClassName: com.mysql.cj.jdbc.Driver
    #          url:
    #          username:
    #          password:
    druid:
      # 初始连接数
      initialSize: 4
      # 最小连接池数量
      minIdle: 4
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: true
      testOnReturn: false
      # 注意这个值和druid原生不一致，默认启动了stat
      filters: stat
      webStatFilter:
        enabled: false
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: false
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  #  redis:
  #    # 地址
  #    host: localhost
  #    # 端口，默认为6379
  #    port: 6379
  #    # 数据库索引
  #    database: 0
  #    # 密码
  #    password:
  #    # 连接超时时间
  #    timeout: 10s
  #    # 是否开启ssl
  #    ssl: false

  redis:
    database: 1
    host: ***********
    port: 6379
#    password: Zlzxrjyf12#$
    timeout: 6000

--- # redisson 客户端配置
redisson:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 传输模式
  transportMode: "NIO"
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ruoyi.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 如果尝试在此限制之内发送成功，则开始启用 timeout 计时。
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒
    retryInterval: 1500
    # 发布和订阅连接的最小空闲连接数
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 单个连接最大订阅数量
    subscriptionsPerConnection: 5
    # DNS监测时间间隔，单位：毫秒
    dnsMonitoringInterval: 5000
