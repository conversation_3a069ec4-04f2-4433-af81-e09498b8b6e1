package com.ruoyi.idle.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: ProjecPackageVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/12/1 9:53
 * @Version: 1.0.0
 **/
@Data
public class ProjectPackageVo {
	/**
	 * 项目打包类型（0是按项目打包 1是按行政区打包）
	 */
	String type;

	/**
	 * 项目打包类型(0是闲置土地 1是批而未供 2是都打包)
	 */
	String projectType;
	/**
	 * 行政区列表
	 */
	List<String> regionList;
	/**
	 * 年度列表
	 */
	List<String> yearList;
	/**
	 * 季度列表
	 */
	List<String> quarterList;

	/**
	 * 项目编号表
	 */
	List<String> taskIdList;

	/**
	 * 是否跳过信息不完整项目
	 */
	Boolean isSuccess = false;
}
