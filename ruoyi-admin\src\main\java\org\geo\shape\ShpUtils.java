package org.geo.shape;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.ruoyi.common.utils.file.FileUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.shapefile.ShapefileDataStore;
import org.geotools.data.shapefile.ShapefileDataStoreFactory;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.ows.ServiceException;
import org.geotools.referencing.CRS;
import org.opengis.feature.Property;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShpUtils {

	/**
	 * 将 MultipartFile 类型的 SHP 文件转换为临时 File，并读取其中所有 Geometry 对象
	 *
	 * @param file 上传的 MultipartFile 类型的 SHP 文件
	 * @return ParseInfo shp文件解析的内容
	 * @throws Exception 处理文件时可能出现的任何异常
	 */
	public static ParseInfo readGeometriesFromShp(MultipartFile file) throws Exception {
		if (!file.getName().toLowerCase().endsWith(".zip")) throw new ServiceException("请上传zip文件");

		try (InputStream inputStream = file.getInputStream()) {
			return readGeometriesFromZipInputStream(inputStream);
		}
	}

	/**
	 * 从本地 .shp 文件路径读取并解析
	 *
	 * @param shpFile 本地 .shp 文件
	 * @return 解析结果
	 * @throws Exception 异常
	 */
	public static ParseInfo readGeometriesFromShpFile(File shpFile) throws Exception {
		if (!shpFile.getName().toLowerCase().endsWith(".zip")) throw new ServiceException("请上传zip文件");
		try (InputStream inputStream = Files.newInputStream(shpFile.toPath())) {
			return readGeometriesFromZipInputStream(inputStream);
		}
	}

	/**
	 * 从 InputStream 类型的 zip 文件读取并解析 SHP 内容
	 */
	private static ParseInfo readGeometriesFromZipInputStream(InputStream inputStream) throws Exception {
		File unzipDir = null;
		try {
			Path tempDir = Files.createTempDirectory("shp_upload_");
			unzipDir = tempDir.toFile();

			// 解压
			ZipUtil.unzip(inputStream, unzipDir, StandardCharsets.UTF_8);

			// 查找 .shp 文件
			File[] shpFiles = unzipDir.listFiles(f -> f.getName().toLowerCase().endsWith(".shp"));
			if (shpFiles == null || shpFiles.length == 0) {
				throw new FileNotFoundException("未找到 .shp 文件");
			}

			return parseShapefile(shpFiles[0]);
		} finally {
			try {
				FileUtils.del(unzipDir);
			} catch (IORuntimeException ignore) {
			}
		}
	}

	/**
	 * 通用 SHP 文件解析方法
	 */
	private static ParseInfo parseShapefile(File shpFile) throws Exception {
		// 查找 .cpg 和 .prj 文件
		File cpgFile = new File(shpFile.getParent(), shpFile.getName().replace(".shp", ".cpg"));
		File prjFile = new File(shpFile.getParent(), shpFile.getName().replace(".shp", ".prj"));
		File shxFile = new File(shpFile.getParent(), shpFile.getName().replace(".shp", ".shx"));
		File dbfFile = new File(shpFile.getParent(), shpFile.getName().replace(".shp", ".dbf"));

		if (!cpgFile.exists()) throw new FileNotFoundException(".cpg 文件未找到");
		if (!prjFile.exists()) throw new FileNotFoundException(".prj 文件未找到");
		if (!shxFile.exists()) throw new FileNotFoundException(".shx 文件未找到");
		if (!dbfFile.exists()) throw new FileNotFoundException(".dbf 文件未找到");

		Charset charset = StandardCharsets.UTF_8;
		try (BufferedReader reader = new BufferedReader(new FileReader(cpgFile))) {
			String encoding = reader.readLine();
			if (StrUtil.isNotBlank(encoding)) {
				charset = Charset.forName(encoding.trim());
			}
		}

		String wkt = Files.readAllLines(prjFile.toPath(), charset).stream().collect(Collectors.joining(System.lineSeparator()));
		CoordinateReferenceSystem crs = CRS.parseWKT(wkt);

		Map<String, Object> params = new HashMap<>();
		params.put("url", shpFile.toURI().toURL());

		final ShapefileDataStore dataStore = (ShapefileDataStore) new ShapefileDataStoreFactory().createDataStore(params);
		dataStore.setCharset(charset);

		String typeName = dataStore.getTypeNames()[0];
		SimpleFeatureSource featureSource = dataStore.getFeatureSource(typeName);

		List<List<RecordMetaData>> parseResult = new ArrayList<>();
		try (SimpleFeatureIterator it = featureSource.getFeatures().features()) {
			while (it.hasNext()) {
				SimpleFeature feature = it.next();
				Collection<Property> properties = feature.getProperties();
				List<RecordMetaData> recordList = new ArrayList<>();
				for (Property property : properties) {
					String name = property.getName().getLocalPart();
					Class<?> classType = property.getType().getBinding();
					Object value = property.getValue();
					recordList.add(new RecordMetaData(name, classType, value));
				}
				parseResult.add(recordList);
			}
		} finally {
			dataStore.dispose();
		}

		return new ParseInfo(String.valueOf(CRS.lookupEpsgCode(crs, Boolean.FALSE)), parseResult);
	}

	/**
	 * 要素的Java元数据对象
	 */
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class RecordMetaData {

		/**
		 * 属性名称
		 */
		private String name;

		/**
		 * 属性的Java类型
		 */
		private Class<?> clazz;

		/**
		 * 对应的数据
		 */
		private Object value;

	}

	/**
	 * 解析结果
	 */
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ParseInfo {

		/**
		 * 坐标系信息
		 */
		private String crs;

		/**
		 * 解析出来的Java元数据
		 */
		private List<List<RecordMetaData>> parseRecords;

	}

}
