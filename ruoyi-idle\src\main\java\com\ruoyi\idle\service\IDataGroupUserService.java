package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataGroupUser;
import com.ruoyi.idle.domain.bo.DataGroupUserQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 巡查组用户关系 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataGroupUserService extends IServicePlus<DataGroupUser> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataGroupUser queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataGroupUser> queryPageList(DataGroupUserQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataGroupUser> queryList(DataGroupUserQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查组用户关系
	 *
	 * @param bo 巡查组用户关系 新增业务对象
	 * @return
	 */
	Boolean insert(DataGroupUser bo);

	/**
	 * 根据编辑业务对象修改巡查组用户关系
	 *
	 * @param bo 巡查组用户关系 编辑业务对象
	 * @return
	 */
	Boolean update(DataGroupUser bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<String> getUserIdsByGroupId(String groupId);
}
