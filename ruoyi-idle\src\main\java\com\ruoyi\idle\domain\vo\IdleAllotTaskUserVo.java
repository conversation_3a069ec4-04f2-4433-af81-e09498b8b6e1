package com.ruoyi.idle.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Created with IntelliJ IDEA.
 * @Title: IdleAllotTaskUserVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/7/7 13:50
 * @Version: 1.0.0
 **/
@Data
public class IdleAllotTaskUserVo {
	/**
	 * 用户编号
	 */
	@ApiModelProperty(value = "用户编号")
	private String userId;

	/**
	 * 用户姓名
	 */
	@ApiModelProperty(value = "用户姓名")
	private String userName;

	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String userPhone;

	/**
	 * 是否是负责人
	 */
	@ApiModelProperty(value = "是否是负责人")
	private String isLeader;

	/**
	 * 业务类型（0是现场 1是后台核实）
	 */
	@ApiModelProperty(value = "业务类型")
	private String busType;
}
