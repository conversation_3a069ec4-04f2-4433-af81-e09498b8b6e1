package com.ruoyi.system.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysRoleMenu;
import com.ruoyi.system.domain.vo.MetaVo;
import com.ruoyi.system.domain.vo.RouterVo;
import com.ruoyi.system.mapper.SysMenuMapper;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysRoleMenuMapper;
import com.ruoyi.system.service.ISysMenuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl extends ServicePlusImpl<SysMenuMapper, SysMenu> implements ISysMenuService {
	public static final String PREMISSION_STRING = "perms[\"{0}\"]";

	@Autowired
	private SysRoleMapper roleMapper;

	@Autowired
	private SysRoleMenuMapper roleMenuMapper;

	/**
	 * 根据用户查询系统菜单列表
	 *
	 * @param userId 用户ID
	 * @return 菜单列表
	 */
	@Override
	public List<SysMenu> selectMenuList(String userId) {
		return selectMenuList(new SysMenu(), userId);
	}

	/**
	 * 查询系统菜单列表
	 *
	 * @param menu 菜单信息
	 * @return 菜单列表
	 */
	@Override
	public List<SysMenu> selectMenuList(SysMenu menu, String userId) {
		List<SysMenu> menuList = null;
		// 管理员显示所有菜单信息
		if (SysUser.isAdmin(userId)) {
			menuList = list(new LambdaQueryWrapper<SysMenu>()
				.like(StrUtil.isNotBlank(menu.getMenuName()), SysMenu::getMenuName, menu.getMenuName())
				.eq(StrUtil.isNotBlank(menu.getVisible()), SysMenu::getVisible, menu.getVisible())
				.eq(StrUtil.isNotBlank(menu.getStatus()), SysMenu::getStatus, menu.getStatus())
				.orderByAsc(SysMenu::getParentId)
				.orderByAsc(SysMenu::getOrderNum));
		} else {
			menu.getParams().put("userId", userId);
			menuList = baseMapper.selectMenuListByUserId(menu);
		}
		return menuList;
	}

	/**
	 * 根据用户ID查询权限
	 *
	 * @param userId 用户ID
	 * @return 权限列表
	 */
	@Override
	public Set<String> selectMenuPermsByUserId(String userId) {
		List<String> perms = baseMapper.selectMenuPermsByUserId(userId);
		Set<String> permsSet = new HashSet<>();
		for (String perm : perms) {
			if (Validator.isNotEmpty(perm)) {
				permsSet.addAll(Arrays.asList(perm.trim().split(",")));
			}
		}
		return permsSet;
	}

	/**
	 * 根据用户ID查询菜单
	 *
	 * @param userId 用户名称
	 * @return 菜单列表
	 */
	@Override
	public List<SysMenu> selectMenuTreeByUserId(String userId) {
		List<SysMenu> menus = null;
		if (SecurityUtils.isAdmin(userId)) {
			menus = baseMapper.selectMenuTreeAll();
		} else {
			menus = baseMapper.selectMenuTreeByUserId(userId);
		}
		return getChildPerms(menus, "0");
	}

	/**
	 * 根据角色ID查询菜单树信息
	 *
	 * @param roleId 角色ID
	 * @return 选中菜单列表
	 */
	@Override
	public List<Integer> selectMenuListByRoleId(String roleId) {
		SysRole role = roleMapper.selectById(roleId);
		return baseMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
	}

	/**
	 * 构建前端路由所需要的菜单
	 *
	 * @param menus 菜单列表
	 * @return 路由列表
	 */
	@Override
	public List<RouterVo> buildMenus(List<SysMenu> menus) {
		List<RouterVo> routers = new LinkedList<RouterVo>();
		for (SysMenu menu : menus) {
			RouterVo router = new RouterVo();
			router.setHidden("1".equals(menu.getVisible()));
			router.setName(getRouteName(menu));
			router.setPath(getRouterPath(menu));
			router.setComponent(getComponent(menu));
			router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StrUtil.equals("1", menu.getIsCache())));
			List<SysMenu> cMenus = menu.getChildren();
			if (!cMenus.isEmpty() && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
				router.setAlwaysShow(true);
				router.setRedirect("noRedirect");
				router.setChildren(buildMenus(cMenus));
			} else if (isMenuFrame(menu)) {
				router.setMeta(null);
				List<RouterVo> childrenList = new ArrayList<RouterVo>();
				RouterVo children = new RouterVo();
				children.setPath(menu.getPath());
				children.setComponent(menu.getComponent());
				children.setName(StrUtil.upperFirst(menu.getPath()));
				children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StrUtil.equals("1", menu.getIsCache())));
				childrenList.add(children);
				router.setChildren(childrenList);
			}
			routers.add(router);
		}
		return routers;
	}

	/**
	 * 构建前端所需要树结构
	 *
	 * @param menus 菜单列表
	 * @return 树结构列表
	 */
	@Override
	public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
		List<SysMenu> returnList = new ArrayList<SysMenu>();
		List<String> tempList = new ArrayList<>();
		for (SysMenu dept : menus) {
			tempList.add(dept.getMenuId());
		}
		for (SysMenu menu : menus) {
			// 如果是顶级节点, 遍历该父节点的所有子节点
			if (!tempList.contains(menu.getParentId())) {
				recursionFn(menus, menu);
				returnList.add(menu);
			}
		}
		if (returnList.isEmpty()) {
			returnList = menus;
		}
		return returnList;
	}

	/**
	 * 构建前端所需要下拉树结构
	 *
	 * @param menus 菜单列表
	 * @return 下拉树结构列表
	 */
	@Override
	public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
		List<SysMenu> menuTrees = buildMenuTree(menus);
		return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
	}

	/**
	 * 根据菜单ID查询信息
	 *
	 * @param menuId 菜单ID
	 * @return 菜单信息
	 */
	@Override
	public SysMenu selectMenuById(String menuId) {
		return getById(menuId);
	}

	/**
	 * 是否存在菜单子节点
	 *
	 * @param menuId 菜单ID
	 * @return 结果
	 */
	@Override
	public boolean hasChildByMenuId(String menuId) {
		int result = count(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getParentId, menuId));
		return result > 0;
	}

	/**
	 * 查询菜单使用数量
	 *
	 * @param menuId 菜单ID
	 * @return 结果
	 */
	@Override
	public boolean checkMenuExistRole(String menuId) {
		int result = roleMenuMapper.selectCount(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getMenuId, menuId));
		return result > 0;
	}

	/**
	 * 新增保存菜单信息
	 *
	 * @param menu 菜单信息
	 * @return 结果
	 */
	@Override
	public int insertMenu(SysMenu menu) {
		return baseMapper.insert(menu);
	}

	/**
	 * 修改保存菜单信息
	 *
	 * @param menu 菜单信息
	 * @return 结果
	 */
	@Override
	public int updateMenu(SysMenu menu) {
		return baseMapper.updateById(menu);
	}

	/**
	 * 删除菜单管理信息
	 *
	 * @param menuId 菜单ID
	 * @return 结果
	 */
	@Override
	public int deleteMenuById(String menuId) {
		return baseMapper.deleteById(menuId);
	}

	/**
	 * 校验菜单名称是否唯一
	 *
	 * @param menu 菜单信息
	 * @return 结果
	 */
	@Override
	public String checkMenuNameUnique(SysMenu menu) {
		String menuId = StringUtils.isBlank(menu.getMenuId()) ? "" : menu.getMenuId();
		SysMenu info = getOne(new LambdaQueryWrapper<SysMenu>()
			.eq(SysMenu::getMenuName, menu.getMenuName())
			.eq(SysMenu::getParentId, menu.getParentId())
			.last("limit 1"));
		if (Validator.isNotNull(info) && !info.getMenuId().equals(menuId)) {
			return UserConstants.NOT_UNIQUE;
		}
		return UserConstants.UNIQUE;
	}

	/**
	 * 获取路由名称
	 *
	 * @param menu 菜单信息
	 * @return 路由名称
	 */
	public String getRouteName(SysMenu menu) {
		String routerName = StrUtil.upperFirst(menu.getPath());
		// 非外链并且是一级目录（类型为目录）
		if (isMenuFrame(menu)) {
			routerName = StrUtil.EMPTY;
		}
		return routerName;
	}

	/**
	 * 获取路由地址
	 *
	 * @param menu 菜单信息
	 * @return 路由地址
	 */
	public String getRouterPath(SysMenu menu) {
		String routerPath = menu.getPath();
		// 非外链并且是一级目录（类型为目录）
		if ("0".equals(menu.getParentId())&& UserConstants.TYPE_DIR.equals(menu.getMenuType())
			&& UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
			routerPath = "/" + menu.getPath();
		}
		// 非外链并且是一级目录（类型为菜单）
		else if (isMenuFrame(menu)) {
			routerPath = "/";
		}
		return routerPath;
	}

	/**
	 * 获取组件信息
	 *
	 * @param menu 菜单信息
	 * @return 组件信息
	 */
	public String getComponent(SysMenu menu) {
		String component = UserConstants.LAYOUT;
		if (StrUtil.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
			component = menu.getComponent();
		} else if (StrUtil.isEmpty(menu.getComponent()) && isParentView(menu)) {
			component = UserConstants.PARENT_VIEW;
		}
		return component;
	}

	/**
	 * 是否为菜单内部跳转
	 *
	 * @param menu 菜单信息
	 * @return 结果
	 */
	public boolean isMenuFrame(SysMenu menu) {
		return "0".equals(menu.getParentId()) && UserConstants.TYPE_MENU.equals(menu.getMenuType())
			&& menu.getIsFrame().equals(UserConstants.NO_FRAME);
	}

	/**
	 * 是否为parent_view组件
	 *
	 * @param menu 菜单信息
	 * @return 结果
	 */
	public boolean isParentView(SysMenu menu) {
		return !"0".equals(menu.getParentId()) && UserConstants.TYPE_DIR.equals(menu.getMenuType());
	}

	/**
	 * 根据父节点的ID获取所有子节点
	 *
	 * @param list     分类表
	 * @param parentId 传入的父节点ID
	 * @return String
	 */
	public List<SysMenu> getChildPerms(List<SysMenu> list, String parentId) {
		List<SysMenu> returnList = new ArrayList<SysMenu>();
		for (SysMenu t : list) {
			// 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
			if (t.getParentId().equals(parentId)) {
				recursionFn(list, t);
				returnList.add(t);
			}
		}
		return returnList;
	}

	/**
	 * 递归列表
	 *
	 * @param list
	 * @param t
	 */
	private void recursionFn(List<SysMenu> list, SysMenu t) {
		// 得到子节点列表
		List<SysMenu> childList = getChildList(list, t);
		t.setChildren(childList);
		for (SysMenu tChild : childList) {
			if (hasChild(list, tChild)) {
				recursionFn(list, tChild);
			}
		}
	}

	/**
	 * 得到子节点列表
	 */
	private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
		List<SysMenu> tlist = new ArrayList<SysMenu>();
		for (SysMenu n : list) {
			if (n.getParentId().equals(t.getMenuId())) {
				tlist.add(n);
			}
		}
		return tlist;
	}

	/**
	 * 判断是否有子节点
	 */
	private boolean hasChild(List<SysMenu> list, SysMenu t) {
		return getChildList(list, t).size() > 0;
	}
}
