package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 房屋建筑信息外业调查 对象 data_house_check
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@NoArgsConstructor
@FieldNameConstants
@Accessors(chain = true)
@TableName("data_house_check")
public class DataHouseCheck implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 主键编号
	 */
	@TableId(value = "id")
	private String id;

	/**
	 * 建筑编号
	 */
	private String jzbh;

	/**
	 * 建筑预编号
	 */
	private String jzybh;

	/**
	 * 建筑编码
	 */
	private String jzbm;

	/**
	 * 数据来源
	 */
	private String sjly;

	/**
	 * 地上建筑面积数据来源
	 */
	private String mjly;

	/**
	 * 任务id
	 */
	private String taskId;

	/**
	 * 国土空间表id
	 */
	private String clgtId;

	/**
	 * 名称
	 */
	private String mc;

	/**
	 * 基底面积(平方米)
	 */
	private Double jdmj;

	/**
	 * 地上层数
	 */
	private String dscs;

	/**
	 * 地上建筑面积(平方米)
	 */
	private Double dsjzmj;

	/**
	 * 建筑高度(米)
	 */
	private Double jzgd;

	/**
	 * 异形建筑
	 */
	private String yxjz;

	/**
	 * 房屋套数
	 */
	private Long fwts;

	/**
	 * 详细地址
	 */
	private String xxdz;

	/**
	 * 建筑年代
	 */
	@DateTimeFormat(pattern = "yyyy")
	@JsonFormat(pattern = "yyyy", timezone = "GMT+8")
	private Date jznd;

	/**
	 * 调查时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date dcsj;

	/**
	 * 建筑状态
	 */
	private String jzzt;

	/**
	 * 结构类型
	 */
	private String jglx;

	/**
	 * 实际用途
	 */
	private String sjyt;

	/**
	 * 规划用途
	 */
	private String ghyt;

	/**
	 * 竣工用途
	 */
	private String jgyt;

	/**
	 * 建筑分层用途
	 */
	private String jzfcyt;

	/**
	 * 分用途楼层段
	 */
	private String fytlcd;

	/**
	 * 分用途建筑面积(平方米)
	 */
	private Double fytjzmj;

	/**
	 * 地下层数
	 */
	private String dxcs;

	/**
	 * 地下建筑面积(平方米)
	 */
	private Double dxjzmj;

	/**
	 * 房屋性质
	 */
	private String fwxz;

	/**
	 * 闲置时间
	 */
	private String xzsj;

//	private String xzfw;

	/**
	 *属性来源标志
	 */
	private String lybz;

	/**
	 * 备注
	 */
	private String bz;

	/**
	 * 用地所有权
	 */
	private String ydsyq;

	/**
	 * 填写说明
	 */
	private String txsm;

	/**
	 * 删除标志
	 */
	private String deleteFlag;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
