package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataPatrolGroup;
import com.ruoyi.idle.domain.bo.DataPatrolGroupQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 巡查组 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataPatrolGroupService extends IServicePlus<DataPatrolGroup> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataPatrolGroup queryById(String id);

	/**
	 * 根据巡查组编号获取巡查组名称
	 *
	 * @return
	 */
	String getGroupNameById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataPatrolGroup> queryPageList(DataPatrolGroupQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataPatrolGroup> queryList(DataPatrolGroupQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查组
	 *
	 * @param bo 巡查组 新增业务对象
	 * @return
	 */
	Boolean insert(DataPatrolGroup bo);

	/**
	 * 根据编辑业务对象修改巡查组
	 *
	 * @param bo 巡查组 编辑业务对象
	 * @return
	 */
	Boolean update(DataPatrolGroup bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<DataPatrolGroup> getAllPatrolGroupList(String startTime);
}
