package com.ruoyi.socket;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.idle.domain.DataMeetingRecord;
import com.ruoyi.idle.domain.DataMeetingUser;
import com.ruoyi.idle.service.IDataMeetingRecordService;
import com.ruoyi.idle.service.IDataMeetingUserService;
import com.ruoyi.meet.MeetConsts;
import com.ruoyi.meet.MeetVo;
import com.ruoyi.meet.MeetuserVo;
import com.ruoyi.meet.utils.MeetingRecordUtils;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class MeetStatusManager {

	private static final Logger log = LoggerFactory.getLogger("WebSocket");

	private static ConcurrentHashMap<String, MeetVo> meetMap = new ConcurrentHashMap<>();

	//只在新建会议时会调用
	public static void addMeet(MeetVo meetVo) {
		MeetingRecordUtils recordUtils = new MeetingRecordUtils();
		if (!meetMap.containsKey(meetVo.getMtNo())) {
			meetMap.put(meetVo.getMtNo(), meetVo);
			// 保存会议记录
			recordUtils.saveMeetingRecord(meetVo);
		}
//		else {
//			meetMap.remove(meetVo.getMtNo());
//			meetMap.put(meetVo.getMtNo(), meetVo);
//			// 更新会议记录（暂时不做）
//		}
		log.info("创建" + String.format(msgFormat, meetVo.getCreatorName(), meetVo.getMtNo(), meetVo.getCreatePhone()));
		notifyUsers(meetVo);
	}

	final static String msgFormat = "{creatorName:\"%s\",mtNo:\"%s\",createPhone:\"%s\"}";

	public static void notifyUserForOpen(String userId) {
		MeetuserVo vuserNow = null;
		MeetVo meeNow = null;
		for (Map.Entry<String, MeetVo> entry : meetMap.entrySet()) {
			MeetVo mee = entry.getValue();
			List<MeetuserVo> listuserss = mee.getPnoneUsers();
			vuserNow = getuserVo(listuserss, userId);
			if (vuserNow != null) {
				meeNow = mee;
				break;
			}
		}
		if (vuserNow != null && meeNow != null) {
			JSONObject object1 = new JSONObject();

			//1.在JSONObject对象中放入键值对
			object1.put("code", MeetConsts.meetMsg_Invite);
			object1.put("creatorName", meeNow.getCreatorName());
			object1.put("creatorXzq", meeNow.getCreatorXzq());
			object1.put("projectXzq", meeNow.getProjectXzq());

			object1.put("mtNo", meeNow.getMtNo());
			object1.put("meetLongId", meeNow.getMeetLongId());
			object1.put("createPhone", meeNow.getCreatePhone());
			object1.put("date", DateUtils.dateTimeNow());

			WebSocket.sendInfo(vuserNow.getPhone(), object1);
		}
	}

	private static MeetuserVo getuserVo(List<MeetuserVo> listuserss, String uid) {
		MeetuserVo vuserNow = null;
		int lnghtt = listuserss.size();
		for (int i = 0; i < lnghtt; i++) {
			MeetuserVo vv = listuserss.get(i);
			if (vv.getPhone().equals(uid)) {
				vuserNow = vv;
				break;
			}
		}
		return vuserNow;
	}

	public static void notifyUsers(MeetVo meetVo) {
		if (meetVo.getPnoneUsers() != null && meetVo.getPnoneUsers().size() > 0) {
			//String msg = String.format(msgFormat,meetVo.getCreatorName(),meetVo.getMtNo(),meetVo.getCreatePhone());
			for (MeetuserVo meetuserVo :
				meetVo.getPnoneUsers()) {

				JSONObject object1 = new JSONObject();

				//1.在JSONObject对象中放入键值对
				object1.put("code", MeetConsts.meetMsg_Invite);
				object1.put("creatorName", meetVo.getCreatorName());
				object1.put("mtNo", meetVo.getMtNo());
				object1.put("meetLongId", meetVo.getMeetLongId());
				object1.put("createPhone", meetVo.getCreatePhone());
				object1.put("date", DateUtils.dateTimeNow());

				WebSocket.sendInfo(meetuserVo.getPhone(), object1);
			}

		}
	}

	public static void sendAllUsersMeetRunnig() {
		List<String> list = new ArrayList<>();
		for (Map.Entry<String, MeetVo> entry : meetMap.entrySet()) {
			MeetVo mee = entry.getValue();
			list.add(mee.getMeetLongId());

		}
		if (list.size() > 0) {
			JSONObject object1 = new JSONObject();

			//1.在JSONObject对象中放入键值对
			object1.put("code", MeetConsts.meet_running);
			object1.put("meetLongIds", list);
			WebSocket.sendAllMessage(object1.toJSONString());
		}
	}

	public static List<MeetVo> getAllMeetRunnig() {
		List<MeetVo> list = new ArrayList<>();
		for (Map.Entry<String, MeetVo> entry : meetMap.entrySet()) {
			MeetVo mee = entry.getValue();
			list.add(mee);

		}
		return list;
	}

	private static void sendUsersMeetRunnig(MeetVo meetVo) {
		if (meetVo.getPnoneUsers() != null && meetVo.getPnoneUsers().size() > 0) {
			//String msg = String.format(msgFormat,meetVo.getCreatorName(),meetVo.getMtNo(),meetVo.getCreatePhone());
			for (MeetuserVo meetuserVo :
				meetVo.getPnoneUsers()) {

				JSONObject object1 = new JSONObject();

				//1.在JSONObject对象中放入键值对
				object1.put("code", MeetConsts.meet_running);
				object1.put("meetLongId", meetVo.getMeetLongId());
				WebSocket.sendInfo(meetuserVo.getPhone(), object1);
			}

		}
	}

	final static String ClientMsgFormat = "{mtNo:\"%s\",user:\"%s\",phone:\"%s\"}";

	public static void DealClientMsg(JSONObject clientMsgJson) {
		Integer code = clientMsgJson.getInteger("code");

		String mtNo = StrConvert.toStrTrim(clientMsgJson.getString("mtNo"));
		if (code != null && code == MeetConsts.meetMsg_CreatoeExit) {
			meetDestroy(mtNo);

			return;
		}

		String user = StrConvert.toStrTrim(clientMsgJson.getString("user"));
		String phone = StrConvert.toStrTrim(clientMsgJson.getString("phone"));

		log.info("用户返回 " + code + "  " + String.format(ClientMsgFormat, mtNo, user, phone));

		if (code != null && code == MeetConsts.meetMsg_Location) {
			String longitude = StrConvert.toStrTrim(clientMsgJson.getString("longitude"));
			String latitude = StrConvert.toStrTrim(clientMsgJson.getString("latitude"));
			//meetRecordLocation(mtNo,  clientMsgJson);
			setUserLocation(mtNo, user, phone, longitude, latitude);
			return;
		} else if (code != null && code == MeetConsts.meetUsers_request) {
			sendMeetingUsers(mtNo, phone);
			return;
		}


		if (code != null && code == MeetConsts.meetMsg_Join) {
			setUserStatus(mtNo, user, phone, "join");
		} else if (code != null && code == MeetConsts.meetMsg_Reject) {
			setUserStatus(mtNo, user, phone, "reject");
		} else if (code != null && code == MeetConsts.meetMsg_exit) {
			setUserStatus(mtNo, user, phone, "exit");
		}
	}

	public static void sendMeetingUsers(MeetVo meetVo) {
		//if(meetMap.containsKey(mtNo)) {
		//	MeetVo meetVo = meetMap.get(mtNo);
		JSONObject object1 = new JSONObject();

		//1.在JSONObject对象中放入键值对
		object1.put("code", MeetConsts.meetUsers_hasLocation);
		object1.put("users", meetVo.getPnoneUsers());

		for (MeetuserVo userVo :
			meetVo.getPnoneUsers()) {

			WebSocket.sendInfo(userVo.getPhone(), object1);
		}

		//}
	}

	public static void sendMeetingUsers(String mtNo, String userphone) {
		if (meetMap.containsKey(mtNo)) {
			MeetVo meetVo = meetMap.get(mtNo);
			JSONObject object1 = new JSONObject();

			//1.在JSONObject对象中放入键值对
			object1.put("code", MeetConsts.meetUsers_request);
			object1.put("users", meetVo.getPnoneUsers());


			WebSocket.sendInfo(userphone, object1);


		}
	}

	private static void meetDestroy(String mtNo) {
		if (meetMap.containsKey(mtNo)) {
			MeetVo meetVo = meetMap.get(mtNo);
			meetVo.getPnoneUsers().clear();
			meetMap.remove(mtNo);
			log.info("会议结束 " + mtNo);
		}
	}


	private static void setUserLocation(String mtNo, String user, String phone, String longitude, String latitude) {
		if (StringUtils.isBlank(longitude)
			|| StringUtils.isBlank(latitude)
			|| StringUtils.isBlank(phone)) {
			return;
		}
		Double longitudeDouble = StrConvert.toDouble(longitude);
		Double latitudeDouble = StrConvert.toDouble(latitude);
		if (longitudeDouble == null || latitudeDouble == null)
			return;

		if (longitudeDouble < 2 || latitudeDouble < 2) {
			return;
		}
		if (meetMap.containsKey(mtNo)) {
			MeetVo meetVo = meetMap.get(mtNo);
			boolean userIsIn = false;

			//List<MeetuserVo> list = meetVo.getPnoneUsers();

			for (MeetuserVo meetuserVo :
				meetVo.getPnoneUsers()) {

				if (meetuserVo.getPhone().equals(phone)) {
					meetuserVo.setLongitude(longitudeDouble);
					meetuserVo.setLatitude(latitudeDouble);
					meetuserVo.setUser(user);
					userIsIn = true;
					break;
				}
			}
			if (!userIsIn) {
				MeetuserVo meetuserVo = new MeetuserVo();
				meetuserVo.setPhone(phone);
				meetuserVo.setLongitude(StrConvert.toDouble(longitude));
				meetuserVo.setLatitude(StrConvert.toDouble(latitude));
				meetuserVo.setUser(user);
				meetuserVo.setStatus("join");
				if (meetVo.getPnoneUsers() != null) {
					meetVo.getPnoneUsers().add(meetuserVo);
				}
			}
			//meetVo.setPnoneUsers(list);

			//meetMap.remove(meetVo.getMtNo());
			//meetMap.put(meetVo.getMtNo(), meetVo);


			//addMeet(meetVo);
			sendMeetingUsers(meetVo);
		}
	}

	public static List<MeetuserVo> getMeetUsers(String mtNo) {
		if (meetMap.containsKey(mtNo)) {
			MeetVo meetVo = meetMap.get(mtNo);

			return meetVo.getPnoneUsers();
		}

		return new ArrayList<>();
	}

	private static void setUserStatus(String mtNo, String user, String phone, String status) {
		if (meetMap.containsKey(mtNo)) {
			log.info("会议记录Map个数" + meetMap.mappingCount());
			MeetVo meetVo = meetMap.get(mtNo);
			boolean userIsIn = false;
			for (MeetuserVo meetuserVo : meetVo.getPnoneUsers()) {

				if (meetuserVo.getPhone().equals(phone) && status.equals("exit")) {
					meetVo.getPnoneUsers().remove(meetuserVo);
					userIsIn = true;
					break;
				} else if (meetuserVo.getPhone().equals(phone)) {
					meetuserVo.setStatus(status);
					meetuserVo.setUser(user);
					userIsIn = true;
					break;
				}
			}
			if (!userIsIn) {
				if (status.equals("join")) {
					// 保存加入会议的人员
					MeetingRecordUtils recordUtils = new MeetingRecordUtils();
					recordUtils.saveMeetingUser(meetVo, user, phone);
				}
				MeetuserVo meetuserVo = new MeetuserVo();
				meetuserVo.setPhone(phone);
				meetuserVo.setUser(user);
				meetuserVo.setStatus(status);
				if (meetVo.getPnoneUsers() != null) {
					meetVo.getPnoneUsers().add(meetuserVo);
				}
			} else {
				if (status.equals("join")) {
					// 保存加入会议的人员
					MeetingRecordUtils recordUtils = new MeetingRecordUtils();
					recordUtils.saveMeetingUser(meetVo, user, phone);
				}
			}
		}
	}
}
