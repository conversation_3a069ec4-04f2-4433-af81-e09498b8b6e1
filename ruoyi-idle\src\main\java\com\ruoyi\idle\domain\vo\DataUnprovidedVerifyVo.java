package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Created with IntelliJ IDEA.
 * @Title: DataUnprovidedVerifyVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/8/25 10:24
 * @Version: 1.0.0
 **/
@Data
public class DataUnprovidedVerifyVo {

	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;

	/**
	 * 目录编号
	 */
	@ApiModelProperty(value = "目录编号")
	private String catalogId;

	/**
	 * 是否审核通过
	 */
	@ApiModelProperty(value = "是否审核通过")
	private String isProcess;

	/**
	 * 处置是否合规
	 */
	@ApiModelProperty(value = "处置是否合规")
	private String handleIsCorrect;

	/**
	 * 巡查任务状态
	 */
	@ApiModelProperty(value = "巡查任务状态")
	private String taskStatus;
	/**
	 * 巡查结果
	 */
	@ApiModelProperty(value = "巡查结果")
	private String patrolResult;

	/**
	 * 是否提交
	 */
	@ApiModelProperty(value = "是否提交")
	private Boolean isSubmit;

}
