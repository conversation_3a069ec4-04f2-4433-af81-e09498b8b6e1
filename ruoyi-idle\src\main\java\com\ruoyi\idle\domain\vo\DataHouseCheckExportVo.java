package com.ruoyi.idle.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
@Data
@ApiModel("房屋建筑信息外业调查 视图对象")
public class DataHouseCheckExportVo {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@ApiModelProperty("主键编号")
	private String id;

	/**
	 * 建筑编号
	 */
	@Excel(name = "建筑编号")
	@ApiModelProperty("建筑编号")
	private String jzbh;

	/**
	 * 建筑预编号
	 */
	@Excel(name = "建筑预编号")
	@ApiModelProperty("建筑预编号")
	private String jzybh;

	/**
	 * 巡查人员
	 */
	@Excel(name = "巡查人员")
	@ApiModelProperty("巡查人员")
	private String updateBy;

	/**
	 * 行政区名称
	 */
	@Excel(name = "行政区名称")
	@ApiModelProperty("行政区名称")
	private String regionName;

	/**
	 * 行政区代码
	 */
	@Excel(name = "行政区代码")
	@ApiModelProperty("行政区代码")
	private String regionCode;
	/**
	 * 行政区名称
	 */
//	@Excel(name = "名称")
	@ApiModelProperty("名称")
	private String mc;

	/**
	 * 基底面积(平方米)
	 */
	@Excel(name = "基底面积(平方米)")
	@ApiModelProperty("基底面积(平方米)")
	private Double jdmj;

	/**
	 * 地上层数
	 */
	@Excel(name = "地上层数")
	@ApiModelProperty("地上层数")
	private String dscs;

	/**
	 * 地上建筑面积(平方米)
	 */
	@Excel(name = "地上建筑面积(平方米)")
	@ApiModelProperty("地上建筑面积(平方米)")
	private Double dsjzmj;

	/**
	 * 建筑高度(米)
	 */
	@Excel(name = "建筑高度(米)")
	@ApiModelProperty("建筑高度(米)")
	private Double jzgd;

	/**
	 * 异形建筑
	 */
	@Excel(name = "异形建筑")
	@ApiModelProperty("异形建筑")
	private String yxjz;

	/**
	 * 房屋套数
	 */
	@Excel(name = "房屋套数")
	@ApiModelProperty("房屋套数")
	private Long fwts;

	/**
	 * 详细地址
	 */
	@Excel(name = "详细地址")
	@ApiModelProperty("详细地址")
	private String xxdz;

	/**
	 * 建筑年代
	 */
	@Excel(name = "建筑年代", width = 30, dateFormat = "yyyy")
	@ApiModelProperty("建筑年代")
	@DateTimeFormat(pattern = "yyyy")
	@JsonFormat(pattern = "yyyy", timezone = "GMT+8")
	private Date jznd;

	/**
	 * 调查时间
	 */
	@Excel(name = "调查时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("调查时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date dcsj;

	/**
	 * 建筑状态
	 */
	@Excel(name = "建筑状态")
	@ApiModelProperty("建筑状态")
	private String jzzt;

	/**
	 * 结构类型
	 */
	@Excel(name = "结构类型")
	@ApiModelProperty("结构类型")
	private String jglx;

	/**
	 * 实际用途
	 */
	@Excel(name = "实际用途")
	@ApiModelProperty("实际用途")
	private String sjyt;

	/**
	 * 规划用途
	 */
	@Excel(name = "规划用途")
	@ApiModelProperty("规划用途")
	private String ghyt;

	/**
	 * 竣工用途
	 */
	@Excel(name = "竣工用途")
	@ApiModelProperty("竣工用途")
	private String jgyt;

	/**
	 * 建筑分层用途
	 */
	@Excel(name = "建筑分层用途")
	@ApiModelProperty("建筑分层用途")
	private String jzfcyt;

	/**
	 * 分用途楼层段
	 */
	@Excel(name = "分用途楼层段")
	@ApiModelProperty("分用途楼层段")
	private String fytlcd;

	/**
	 * 分用途建筑面积(平方米)
	 */
	@Excel(name = "分用途建筑面积(平方米)")
	@ApiModelProperty("分用途建筑面积(平方米)")
	private Double fytjzmj;

	/**
	 * 地下层数
	 */
	@Excel(name = "地下层数")
	@ApiModelProperty("地下层数")
	private String dxcs;

	/**
	 * 地下建筑面积(平方米)
	 */
	@Excel(name = "地下建筑面积(平方米)")
	@ApiModelProperty("地下建筑面积(平方米)")
	private Double dxjzmj;

	/**
	 * 房屋性质
	 */
	@Excel(name = "房屋性质")
	@ApiModelProperty("房屋性质")
	private String fwxz;

	/**
	 * 闲置时间
	 */
	@Excel(name = "闲置时间")
	@ApiModelProperty("闲置时间")
	private String xzsj;

	/**
	 * 用地所有权
	 */
	@Excel(name = "用地所有权")
	@ApiModelProperty("用地所有权")
	private String ydsyq;

	/**
	 * 填写说明
	 */
	@Excel(name = "填写说明")
	@ApiModelProperty("填写说明")
	private String txsm;

	/**
	 * 删除标志
	 */
//	@Excel(name = "删除标志")
	@ApiModelProperty("删除标志")
	private String deleteFlag;

	/**
	 * 任务id
	 */
	private String taskId;

	/**
	 * 国土空间表id
	 */
	private String clgtId;

	/**
	 * 巡查结果
	 */
	@ApiModelProperty(value = "巡查结果")
	private String patrolResult;

	/**
	 * 巡查意见
	 */
	@ApiModelProperty(value = "巡查意见")
	private String patrolOpinion;

	/**
	 * 是否提交
	 */
	@ApiModelProperty(value = "是否提交")
	private Boolean isSubmit;
}
