package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 闲置土地 对象 data_clgt_land
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@NoArgsConstructor
@FieldNameConstants
@Accessors(chain = true)
@TableName("data_clgt_land")
public class DataClgtLand implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 编号
	 */
	@TableId(value = "id")
	private String id;

	/**
	 * 闲置土地所在市
	 */
	private String city;

	/**
	 * 闲置土地所在县代码
	 */
	private String countyCode;

	/**
	 * 闲置土地所在县名称
	 */
	private String countyName;

	/**
	 * 合同编号
	 */
	private String contractNo;

	/**
	 * 供应方式
	 */
	private String supplyType;

	/**
	 * 项目名称
	 */
	private String projectName;

	/**
	 * 数据年度
	 */
	private String year;

	/**
	 * 数据季度
	 */
	private String quarter;

	/**
	 * 电子监管号
	 */
	private String supervisionNo;

	/**
	 * 土地用途
	 */
	private String landUse;

	/**
	 * 土地面积（公顷）
	 */
	private Double landArea;

	/**
	 * 签订日期
	 */
	private Date signDate;

	/**
	 * 约定动工时间
	 */
	private Date agreedStartTime;

	/**
	 * 实际动工时间
	 */
	private Date actualStartTime;

	/**
	 * 实际竣工时间
	 */
	private Date actualEndTime;

	/**
	 * 闲置状态
	 */
	private String idleStatus;

	/**
	 * 图斑编号
	 */
	private String spotNumber;

	/**
	 * 内业备注
	 */
	private String insideRemark;

	/**
	 * 消化类型
	 */
	private String digestionType;

	/**
	 * 项目详细位置
	 */
	private String address;

	/**
	 * 项目空间数据
	 */
	private String geoData;

	/**
	 * 宗地号
	 */
	private String parcelNum;

	/**
	 * 项目中心点经度
	 */
	private String longitude;

	/**
	 * 项目中心点纬度
	 */
	private String latitude;

	/**
	 * 是否已分配巡查任务
	 */
	private String isAllot;

	/**
	 * 不动产权证号
	 */
	private String warrantNum;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 删除标记
	 */
	private String deleteFlag;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
