package com.ruoyi.idle.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Created with IntelliJ IDEA.
 * @Title: DataIdleVerifyVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/8/25 10:22
 * @Version: 1.0.0
 **/
@Data
public class DataIdleVerifyVo {

	/**
	 * 巡查任务编号
	 */
	@ApiModelProperty(value = "巡查任务编号")
	private String taskId;

	/**
	 * 处置方式（0收回 1置换 2 动工开发）
	 */
	@ApiModelProperty(value = "处置方式")
	private String handleType;

	/**
	 * 目录编号
	 */
	@ApiModelProperty(value = "目录编号")
	private String catalogId;

	/**
	 * 是否审核通过
	 */
	@ApiModelProperty(value = "是否审核通过")
	private String isProcess;

	/**
	 * 视频连线情况是否属实
	 */
	@ApiModelProperty(value = "视频连线情况是否属实")
	private String videoIsReal;

	/**
	 * 实地核查情况是否属实
	 */
	@ApiModelProperty(value = "实地核查情况是否属实")
	private String siteIsReal;

	/**
	 * 处置是否合规
	 */
	@ApiModelProperty(value = "处置是否合规")
	private String handleIsCorrect;

	/**
	 * 巡查任务状态
	 */
	@ApiModelProperty(value = "巡查任务状态")
	private String taskStatus;
	/**
	 * 巡查结果
	 */
	@ApiModelProperty(value = "巡查结果")
	private String patrolResult;

	/** 核查情况 */
	@ApiModelProperty("核查情况")
	private String verificationSituation;

	/** 核查选项 */
	@ApiModelProperty("核查选项")
	private String verificationSelect;

	/**
	 * 是否提交
	 */
	@ApiModelProperty(value = "是否提交")
	private Boolean isSubmit;
}
