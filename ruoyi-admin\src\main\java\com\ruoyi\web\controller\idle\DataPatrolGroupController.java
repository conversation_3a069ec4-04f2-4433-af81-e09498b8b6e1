package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.DataPatrolGroup;
import com.ruoyi.idle.domain.bo.DataPatrolGroupQueryBo;
import com.ruoyi.idle.service.IDataPatrolGroupService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 巡查组 Controller
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Api(value = "巡查组控制器", tags = {"巡查组管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/patrol/group")
public class DataPatrolGroupController extends BaseController {

	private final IDataPatrolGroupService iDataPatrolGroupService;

	private final TokenService tokenService;

	/**
	 * 查询巡查组 列表
	 */
	@ApiOperation("分页查询巡查组列表")
//    @PreAuthorize("@ss.hasPermi('idle:group:list')")
	@GetMapping("/list")
	public TableDataInfo<DataPatrolGroup> list(@Validated DataPatrolGroupQueryBo bo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				bo.setCountyCode(sysUser.getRegionCode());
			}
		}
		return iDataPatrolGroupService.queryPageList(bo);
	}

	/**
	 * 查询巡查组列表
	 */
	@ApiOperation("查询巡查组列表")
//    @PreAuthorize("@ss.hasPermi('idle:group:list')")
	@GetMapping("/groupList")
	public AjaxResult groupList(@Validated DataPatrolGroupQueryBo bo) {
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				bo.setCountyCode(sysUser.getRegionCode());
			}
		}
		return AjaxResult.success(iDataPatrolGroupService.queryList(bo));
	}

	/**
	 * 导出巡查组 列表
	 */
	@ApiOperation("导出巡查组列表")
//    @PreAuthorize("@ss.hasPermi('idle:group:export')")
	@Log(title = "巡查组", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataPatrolGroup> export(@Validated DataPatrolGroupQueryBo bo) {
		List<DataPatrolGroup> list = iDataPatrolGroupService.queryList(bo);
		ExcelUtil<DataPatrolGroup> util = new ExcelUtil<DataPatrolGroup>(DataPatrolGroup.class);
		return util.exportExcel(list, "巡查组");
	}

	/**
	 * 获取巡查组 详细信息
	 */
	@ApiOperation("获取巡查组详细信息")
//    @PreAuthorize("@ss.hasPermi('idle:group:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataPatrolGroup> getInfo(@NotNull(message = "主键不能为空")
											   @PathVariable("id") String id) {
		return AjaxResult.success(iDataPatrolGroupService.queryById(id));
	}

	/**
	 * 新增巡查组
	 */
	@ApiOperation("新增巡查组")
//    @PreAuthorize("@ss.hasPermi('idle:group:add')")
	@Log(title = "巡查组", businessType = BusinessType.INSERT)
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataPatrolGroup bo) {
		bo.setCreateBy(SecurityUtils.getUsername());
		bo.setDeleteFlag("0");
		bo.setGroupAuthor("全部");
		return toAjax(iDataPatrolGroupService.insert(bo) ? 1 : 0);
	}

	/**
	 * 修改巡查组
	 */
	@ApiOperation("修改巡查组")
//    @PreAuthorize("@ss.hasPermi('idle:group:edit')")
	@Log(title = "巡查组", businessType = BusinessType.UPDATE)
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataPatrolGroup bo) {
		bo.setUpdateBy(SecurityUtils.getUsername());
		return toAjax(iDataPatrolGroupService.update(bo) ? 1 : 0);
	}

	/**
	 * 删除巡查组
	 */
	@ApiOperation("删除巡查组")
//    @PreAuthorize("@ss.hasPermi('idle:group:remove')")
	@Log(title = "巡查组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataPatrolGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}
}
