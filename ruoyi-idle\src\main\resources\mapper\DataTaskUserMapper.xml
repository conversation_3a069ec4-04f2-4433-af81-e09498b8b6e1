<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataTaskUserMapper">

    <resultMap type="com.ruoyi.idle.domain.DataTaskUser" id="DataTaskUserResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="userPhone" column="user_phone"/>
        <result property="isLeader" column="is_leader"/>
        <result property="busType" column="bus_type"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectByTaskIdAndUserId" parameterType="java.lang.String" resultMap="DataTaskUserResult">
        SELECT *
        FROM `data_task_user`
        WHERE task_id = #{taskId}
          AND user_id = #{userId}
    </select>


</mapper>
