package com.ruoyi.web.controller.idle;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.service.IDataPatrolTaskService;
import com.ruoyi.idle.service.ISysRegionService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("/idle")
public class AsyncZipController {
	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final ISysRegionService iSysRegionService;
	// 任务状态存储
	private final Map<String, CompressionTask> taskMap = new ConcurrentHashMap<>();
	private final ExecutorService executor = Executors.newFixedThreadPool(4);

	// 启动异步压缩
	@PostMapping("/startCompression")
	public ResponseEntity<Map<String, String>> startCompression(DataPatrolTaskQueryBo bo) throws Exception {
		if(bo.getRegionCode()==null || bo.getRegionCode()==""){throw new Exception("请选择行政区!");}
		List<String> codes = iSysRegionService.queryChildList(bo.getRegionCode());
		if(codes!=null && codes.size()!=0){
			bo.setRegionCodes(codes);
		}
		String taskId = IdUtil.fastSimpleUUID();
		List<Map<String, Object>> images = iDataPatrolTaskService.getImages(bo);
		CompressionTask task = new CompressionTask(images);
		taskMap.put(taskId, task);
		executor.submit(task);  // 提交异步任务
		Map<String,String> map = new HashMap<>();
		map.put("taskId", taskId);
		map.put("statusUrl", "/idle/task-status/" + taskId);
		return ResponseEntity.accepted()
			.header(HttpHeaders.LOCATION, "/task-status/" + taskId)
			.body(map);
	}

	// 查询任务状态
	@GetMapping("/taskStatus/{taskId}")
	public ResponseEntity<Map<String, String>> getTaskStatus(@PathVariable String taskId){
		CompressionTask task = taskMap.get(taskId);
		Map<String,String> map = new HashMap<>();
		map.put("error", "任务不存在或已过期");
		Map<String,String> map1 = new HashMap<>();
		map1.put("status", task.getStatus().name());
		map1.put("progress", task.getProgress() + "%");
		map1.put("downloadUrl", task.isDone() ? "/idle/download/" + taskId : "");
		if (task == null) {
			return ResponseEntity.status(HttpStatus.GONE)
				.body(map);
		}
		return ResponseEntity.ok(map1);
	}

	// 下载压缩结果
	@PostMapping("/download/{taskId}")
	public ResponseEntity<StreamingResponseBody> downloadZip(
		@PathVariable String taskId,
		HttpServletResponse response) {

		CompressionTask task = taskMap.get(taskId);
		if (task == null || !task.isDone()) {
			return ResponseEntity.notFound().build();
		}
		// 设置下载头
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
			"attachment; filename=\"" + task.getOutputFileName() + "\"");
//		response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
//			"attachment; filename=\"" + "temp2472956186573321786" + "\"");
		StreamingResponseBody stream = output -> {
			try (InputStream is = Files.newInputStream(task.getOutputPath())) {
//			try (InputStream is = Files.newInputStream(Paths.get(s))) {
				byte[] buffer = new byte[1024*1024*3];
				int bytesRead;
				while ((bytesRead = is.read(buffer)) != -1) {
					output.write(buffer, 0, bytesRead);
				}
//				StreamUtils.copy(is, output);
			} finally {
				// 清理临时文件（根据需求保留）
				Files.deleteIfExists(task.getOutputPath());
				taskMap.remove(taskId);
			}
		};
		return ResponseEntity.ok()
			.contentType(MediaType.APPLICATION_OCTET_STREAM)
			.body(stream);
	}

	// 压缩任务内部类
	private static class CompressionTask implements Runnable {
		private final List<Map<String,Object>> filePaths;
		private Path outputPath;
		private volatile Status status = Status.PENDING;
		private int progress;
		private Exception error;

		enum Status { PENDING, RUNNING, COMPLETED, FAILED }

		public CompressionTask(List<Map<String,Object>> filePaths) {
			this.filePaths = filePaths;
		}

		@Override
		public void run() {
			status = Status.RUNNING;
			try {
				String profilePath = RuoYiConfig.getProfile();
				String tempFilePath = profilePath +"/"+ "tempFile";
				// 1. 创建临时输出文件
				outputPath = File.createTempFile("temp", ".zip", new File(tempFilePath)).toPath();
//				outputPath = Files.createTempFile(profilePath+"\\"+"async_zip", ".zip");


				// 2. 执行压缩
				try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(outputPath))) {

					for (int i = 0; i < filePaths.size(); i++) {
						Map<String, Object> map = filePaths.get(i);
						String path = profilePath + map.get("location").toString();
						Path filePath = Paths.get(path);
						File realFile = new File(path);
						if (!realFile.exists()) continue;

						Object jzybh = map.get("jzybh");
						String s = "";
						if(ObjectUtil.isNotNull(jzybh)&&ObjectUtil.isNotEmpty(jzybh)){
							s = jzybh.toString();
						}else {
							s= map.get("id").toString()+map.get("regionName").toString();
						}
						String fileName = s+"\\"+map.get("name").toString()+"."+map.get("type").toString();
						try (InputStream is = Files.newInputStream(filePath)) {
							zos.putNextEntry(new ZipEntry(fileName));
							StreamUtils.copy(is, zos);
							zos.closeEntry();
						}
						// 更新进度
						progress = (i + 1) * 100 / filePaths.size();
					}
				}
				status = Status.COMPLETED;
			} catch (Exception e) {
				status = Status.FAILED;
				error = e;
			}
		}

		// 状态检查方法
		public Status getStatus() { return status; }
		public int getProgress() { return progress; }
		public boolean isDone() { return status == Status.COMPLETED; }
		public Path getOutputPath() { return outputPath; }
		public String getOutputFileName() {
			return outputPath.getFileName().toString();
		}
	}
}
