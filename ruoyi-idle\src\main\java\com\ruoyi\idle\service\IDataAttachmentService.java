package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataAttachment;
import com.ruoyi.idle.domain.bo.DataAttachmentQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 巡查附件 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataAttachmentService extends IServicePlus<DataAttachment> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataAttachment queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataAttachment> queryPageList(DataAttachmentQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataAttachment> queryList(DataAttachmentQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查附件
	 *
	 * @param bo 巡查附件 新增业务对象
	 * @return
	 */
	Boolean insert(DataAttachment bo);

	/**
	 * 根据编辑业务对象修改巡查附件
	 *
	 * @param bo 巡查附件 编辑业务对象
	 * @return
	 */
	Boolean update(DataAttachment bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 根据文件名和任务编号删除数据库记录
	 *
	 * @param fileName
	 * @param taskId
	 * @return
	 */
	int realDeleteRecord(String fileName, String taskId);
}
