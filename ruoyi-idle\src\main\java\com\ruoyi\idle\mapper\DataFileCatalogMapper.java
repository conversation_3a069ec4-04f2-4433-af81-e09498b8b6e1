package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataFileCatalog;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 巡查附件目录 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
//@CacheNamespace(implementation = MybatisPlusRedisCache.class, eviction = MybatisPlusRedisCache.class)
public interface DataFileCatalogMapper extends BaseMapperPlus<DataFileCatalog> {

}
