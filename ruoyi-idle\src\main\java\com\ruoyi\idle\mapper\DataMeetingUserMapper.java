package com.ruoyi.idle.mapper;

import com.ruoyi.common.core.mybatisplus.core.BaseMapperPlus;
import com.ruoyi.common.core.mybatisplus.cache.MybatisPlusRedisCache;
import com.ruoyi.idle.domain.DataMeetingUser;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * 与会人员 Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
// 如使需切换数据源 请勿使用缓存 会造成数据不一致现象
public interface DataMeetingUserMapper extends BaseMapperPlus<DataMeetingUser> {

}
