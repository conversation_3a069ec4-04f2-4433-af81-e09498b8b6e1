package com.ruoyi.web.controller.idle;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.bo.DataLandNotProvidedQueryBo;
import com.ruoyi.idle.domain.vo.IdleAllotTaskUserVo;
import com.ruoyi.idle.domain.vo.IdleAllotTaskVo;
import com.ruoyi.idle.domain.vo.StatisticsVo;
import com.ruoyi.idle.service.*;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 批而未供Controller
 *
 * <AUTHOR>
 * @date 2021-08-16
 */
@Api(value = "批而未供控制器", tags = {"批而未供管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/notProvided/land")
public class DataLandNotProvidedController extends BaseController {

	private final IDataLandNotProvidedService iDataLandNotProvidedService;

	private final IDataPatrolTaskService iDataPatrolTaskService;

	private final IDataTaskUserService iDataTaskUserService;

	private final IDataAttachmentService iDataAttachmentService;

	private final IDataAuditRecordService auditRecordService;

	private final IDataVerifyRecordService verifyRecordService;

	private final TokenService tokenService;

	/**
	 * 查询批而未供列表
	 */
	@ApiOperation("查询批而未供列表")
	//@PreAuthorize("@ss.hasPermi('idle:provided:list')")
	@GetMapping("/list")
	public TableDataInfo<DataLandNotProvided> list(@Validated DataLandNotProvidedQueryBo bo) {
		return iDataLandNotProvidedService.queryPageList(bo);
	}

	/**
	 * 导出批而未供列表
	 */
	@ApiOperation("导出批而未供列表")
	//@PreAuthorize("@ss.hasPermi('idle:provided:export')")
	@Log(title = "批而未供", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataLandNotProvided> export(@Validated DataLandNotProvidedQueryBo bo) {
		List<DataLandNotProvided> list = iDataLandNotProvidedService.queryList(bo);
		ExcelUtil<DataLandNotProvided> util = new ExcelUtil<DataLandNotProvided>(DataLandNotProvided.class);
		return util.exportExcel(list, "批而未供");
	}

	/**
	 * 获取批而未供详细信息
	 */
	@ApiOperation("获取批而未供详细信息")
	//@PreAuthorize("@ss.hasPermi('idle:provided:query')")
	@GetMapping("/{id}")
	public AjaxResult<DataLandNotProvided> getInfo(@NotNull(message = "主键不能为空")
												   @PathVariable("id") String id) {
		return AjaxResult.success(iDataLandNotProvidedService.queryById(id));
	}


	/**
	 * 一键分配批而未供核实任务
	 */
	@ApiOperation("一键分配批而未供核实任务")
//	@PreAuthorize("@ss.hasPermi('idle:provided:list')")
	@PostMapping("/allotAllTask")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult allotAllTask(){
		// step.1 查询出所有的未分配闲置土地记录
		LambdaQueryWrapper<DataLandNotProvided> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DataLandNotProvided :: getIsAllot,"0");

		List<DataLandNotProvided> idleLandList = iDataLandNotProvidedService.list(queryWrapper);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginUser loginUser = SecurityUtils.getLoginUser();
		List<IdleAllotTaskUserVo> userList = new ArrayList<>();
		IdleAllotTaskUserVo user1 = new IdleAllotTaskUserVo();
		user1.setUserId("102");
		user1.setUserName("杨社锋");
		user1.setUserPhone("18088138716");
		user1.setIsLeader("0");
		user1.setBusType("1");
		IdleAllotTaskUserVo user2 = new IdleAllotTaskUserVo();
		user2.setUserId("8dfaae0e444db0b861da749f962615ef");
		user2.setUserName("敖朝刚");
		user2.setUserPhone("15012382334");
		user2.setIsLeader("0");
		user2.setBusType("1");
		IdleAllotTaskUserVo user3 = new IdleAllotTaskUserVo();
		user3.setUserId("b134ae9aafd6b8f22e78a1fc19d2b9de");
		user3.setUserName("姜宝鑫");
		user3.setUserPhone("18082759810");
		user3.setIsLeader("1");
		user3.setBusType("1");
		IdleAllotTaskUserVo user4 = new IdleAllotTaskUserVo();
		user4.setUserId("ee46da99f56232b42a9ea8fe22cff626");
		user4.setUserName("陈柯洁");
		user4.setUserPhone("13708795276");
		user4.setIsLeader("0");
		user4.setBusType("1");
		IdleAllotTaskUserVo user5 = new IdleAllotTaskUserVo();
		user5.setUserId("fd703f751fcbe442a2b9bb5a06f3edac");
		user5.setUserName("和晓庆");
		user5.setUserPhone("18760898958");
		user5.setIsLeader("0");
		user5.setBusType("1");
		userList.add(user1);
		userList.add(user2);
		userList.add(user3);
		userList.add(user4);
		userList.add(user5);
		if (idleLandList.size() == 0) return AjaxResult.success("您的所有批而未供项目均已分配，无需再次分配",null);
		for (DataLandNotProvided notProvided : idleLandList){
			// 修改批而未供的分配字段
			notProvided.setIsAllot("1");
			iDataLandNotProvidedService.updateById(notProvided);
			// 在巡查任务中新增记录
			DataPatrolTask patrolTask = new DataPatrolTask();
//			BeanUtils.copyProperties(taskVo, patrolTask);

			patrolTask.setIdleLandId(notProvided.getId());
			patrolTask.setProjectName(notProvided.getProjectName());
			patrolTask.setRegionCode(notProvided.getCountyCode());
			patrolTask.setRegionName(notProvided.getCountyName());
			patrolTask.setQuarter(notProvided.getQuarter());
			String now = DateUtil.now();
			Date date = DateUtil.parse(now);
			Date newDate = DateUtil.offsetDay(date, 10);
			patrolTask.setMeetingEndTime(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss"));
			// 根据巡查组编号获取巡查组名称(不能直接分配到巡查组)
			// patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById(Long.valueOf(taskVo.getPatrolGroup())));
			patrolTask.setPublishStatus("0");
			patrolTask.setProjectType("1");
			patrolTask.setPatrolType("2");
			patrolTask.setIsPatrol("0");
			patrolTask.setDeleteFlag("0");
			if (loginUser != null) {
				//设置创建的用户和创建时间
				patrolTask.setCreateBy(loginUser.getUsername());
			}
			if(notProvided.getYear()!=null){
				patrolTask.setYear(notProvided.getYear().trim());
			}
			iDataPatrolTaskService.save(patrolTask);


			// 保存配置的巡查组成员（一般是后台的核实人员）

			for (IdleAllotTaskUserVo user : userList) {
				DataTaskUser taskUser = new DataTaskUser();
				BeanUtils.copyProperties(user, taskUser);
				taskUser.setTaskId(patrolTask.getId());
				if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("1");
				taskUser.setRemark("内业核实人员");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					taskUser.setCreateBy(loginUser.getUsername());
				}
				iDataTaskUserService.save(taskUser);
			}

		}

		return AjaxResult.success("成功分配批而未供巡查任务");
	}

	/**
	 * 分配批而未供核实任务
	 */
	@ApiOperation("分配批而未供核实任务")
//	@PreAuthorize("@ss.hasPermi('idle:provided:list')")
	@PostMapping("/allotTask")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult allotTask(@RequestBody IdleAllotTaskVo taskVo) throws ParseException {
		if (null == taskVo) return AjaxResult.error("提交的表单内容不能为空");
		DataLandNotProvided notProvided = iDataLandNotProvidedService.queryById(taskVo.getIdleLandId());
		if (null == notProvided) return AjaxResult.success("没有找到对应的批而未供数据");
		if (notProvided.getIsAllot().equals("1")) return AjaxResult.success("您选择的闲置土地记录已经分配巡查任务，不能多次分配");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		LoginUser loginUser = SecurityUtils.getLoginUser();
		// 修改批而未供的分配字段
		notProvided.setIsAllot("1");
		iDataLandNotProvidedService.updateById(notProvided);
		// 在巡查任务中新增记录
		DataPatrolTask patrolTask = new DataPatrolTask();
		BeanUtils.copyProperties(taskVo, patrolTask);
		patrolTask.setIdleLandId(notProvided.getId());
		patrolTask.setProjectName(notProvided.getProjectName());
		patrolTask.setRegionCode(notProvided.getCountyCode());
		patrolTask.setRegionName(notProvided.getCountyName());
		patrolTask.setQuarter(notProvided.getQuarter());
		if (patrolTask.getMeetingTime() != null && patrolTask.getMeetingEndTime() == null) {
			// 设置默认的预计结束时间为两小时
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(sdf.parse(patrolTask.getMeetingTime()));
			calendar.add(Calendar.HOUR, 2);//加上两小时
			patrolTask.setMeetingEndTime(calendar.getTime().toString());
		}
		// 根据巡查组编号获取巡查组名称(不能直接分配到巡查组)
		// patrolTask.setPatrolGroupName(iDataPatrolGroupService.getGroupNameById(Long.valueOf(taskVo.getPatrolGroup())));
		patrolTask.setPublishStatus("0");
		patrolTask.setProjectType("1");
		patrolTask.setPatrolType("2");
		patrolTask.setIsPatrol("0");
		patrolTask.setDeleteFlag("0");
		if (loginUser != null) {
			//设置创建的用户和创建时间
			patrolTask.setCreateBy(loginUser.getUsername());
		}
		if(notProvided.getYear()!=null){
			patrolTask.setYear(notProvided.getYear().trim());
		}
		iDataPatrolTaskService.save(patrolTask);

		// 保存配置的巡查组成员（一般是后台的核实人员）
		List<IdleAllotTaskUserVo> userList = taskVo.getUserList();
		if (null != userList && userList.size() != 0) {
			for (IdleAllotTaskUserVo user : userList) {
				DataTaskUser taskUser = new DataTaskUser();
				BeanUtils.copyProperties(user, taskUser);
				taskUser.setTaskId(patrolTask.getId());
				if (StringUtils.isBlank(taskUser.getBusType())) taskUser.setBusType("1");
				taskUser.setRemark("内业核实人员");
				if (loginUser != null) {
					//设置创建的用户和创建时间
					taskUser.setCreateBy(loginUser.getUsername());
				}
				iDataTaskUserService.save(taskUser);
			}
		}

		return AjaxResult.success("成功分配批而未供核查任务");
	}

	/**
	 * 撤销已分配的批而未供核查任务
	 */
	@ApiOperation("撤销已分配的批而未供核查任务")
//	@PreAuthorize("@ss.hasPermi('idle:land:list')")
	@PostMapping("/revokeAllotTask/{id}")
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult revokeAllotTask(@PathVariable(value = "id") String id) {
		// step.1 根据批而未供记录编号去巡查任务表中查找符合条件的记录
		List<DataPatrolTask> taskList = iDataPatrolTaskService.list(new LambdaQueryWrapper<DataPatrolTask>()
			.eq(DataPatrolTask::getIdleLandId, id));
		// step.2 循环查询出来的巡查任务记录
		if (taskList.size() != 0) {
			for (DataPatrolTask task : taskList) {
				// step.2.1 删除和任务相关的巡查人员记录
				iDataTaskUserService.remove(new LambdaQueryWrapper<DataTaskUser>().eq(DataTaskUser::getTaskId, task.getId()));
				task.setRemark("任务撤销");
				iDataPatrolTaskService.updateById(task);
				iDataPatrolTaskService.removeById(task);
				// step.2.2 删除和任务相关的审核记录（假删除）
				auditRecordService.remove(new LambdaQueryWrapper<DataAuditRecord>().eq(DataAuditRecord::getTaskId, task.getId()));
				// step.2.3 删除和任务相关的核查结果记录（假删除）
				verifyRecordService.remove(new LambdaQueryWrapper<DataVerifyRecord>()
					.eq(DataVerifyRecord :: getTaskId, task.getId()));
				// step.2.4 删除和任务相关的巡查附件（假删除）
				iDataAttachmentService.remove(new LambdaQueryWrapper<DataAttachment>().eq(DataAttachment::getPatrolTaskId, task.getId()));

			}
		}
		// step.3 修改批而未供记录的分配状态字段
		DataLandNotProvided notProvided = iDataLandNotProvidedService.getById(id);
		if (notProvided != null) {
			notProvided.setIsAllot("0");
			iDataLandNotProvidedService.updateById(notProvided);
		}
		return AjaxResult.success("批而未供核查任务撤销成功");
	}

	/**
	 * 新增批而未供
	 */
	@ApiOperation("新增批而未供")
	//@PreAuthorize("@ss.hasPermi('idle:provided:add')")
	@Log(title = "批而未供", businessType = BusinessType.INSERT)
	@RepeatSubmit
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataLandNotProvided bo) {
		return toAjax(iDataLandNotProvidedService.insertByAddBo(bo) ? 1 : 0);
	}

	/**
	 * 批量新增批而未供数据
	 */
	@ApiOperation("批量新增批而未供数据")
	@Log(title = "批而未供", businessType = BusinessType.INSERT)
	@PostMapping("/batchAdd")
	public AjaxResult<Void> batchAdd(@Validated @RequestBody List<DataLandNotProvided> boList) {
		if (null == boList || boList.size() == 0) return AjaxResult.error("新增的批而未供数据不能为空");
		int successCount = 0;
		StringBuilder failMsg = new StringBuilder();
		for (DataLandNotProvided notProvided : boList) {
			// 先根据电子监管号判断是否已经存在该项目
			LambdaQueryWrapper<DataLandNotProvided> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DataLandNotProvided::getSupervisionNo, notProvided.getSupervisionNo())
				.eq(DataLandNotProvided::getYear, notProvided.getYear())
				.eq(DataLandNotProvided::getQuarter, notProvided.getQuarter())
				// 因为批而未供数据存在相同电子监管号的情况，所以多加了合同编号和供地面积字段查询是否存在
				.eq(DataLandNotProvided::getContractNo, notProvided.getContractNo())
				.eq(DataLandNotProvided::getProvidedArea, notProvided.getProvidedArea());

			DataLandNotProvided dataLandNotProvided = iDataLandNotProvidedService.getOne(queryWrapper);
			if (dataLandNotProvided != null) {
				DataLandNotProvided temp = new DataLandNotProvided();
				String[] ignoreFiled = new String[]{"id", "deleteFlag","status","createBy","create_time","updateBy","updateTime","isAllot"};
				BeanUtils.copyProperties(notProvided, temp,ignoreFiled);
				temp.setId(dataLandNotProvided.getId());
				//BeanUtils.copyProperties(notProvided, dataLandNotProvided);
				successCount += iDataLandNotProvidedService.updateById(temp) ? 1 : 0;
				failMsg.append(notProvided.getSupervisionNo()).append(",");
			} else {
				String contractNo = notProvided.getContractNo();
				if (StringUtils.isNotBlank(contractNo)){
					if (contractNo.contains("CR") || contractNo.contains("cr") || contractNo.contains("Cr") || contractNo.contains("cR")){
						notProvided.setSupplyType("CR");
					}else if (contractNo.contains("HB") || contractNo.contains("hb") || contractNo.contains("Hb") || contractNo.contains("hB")){
						notProvided.setSupplyType("HB");
					}
				}
				notProvided.setCreateBy(SecurityUtils.getUsername());
				notProvided.setDeleteFlag("0");
				notProvided.setStatus("0");
				if (notProvided.getIsAllot() == null) notProvided.setIsAllot("0");
				successCount += iDataLandNotProvidedService.save(notProvided) ? 1 : 0;
			}
		}
		if (successCount == 0) {
			return AjaxResult.error("导入失败");
		} else {
			return AjaxResult.success("导入成功，其中" + failMsg.toString() + "重复，已经更新");
		}
	}

	/**
	 * 修改批而未供
	 */
	@ApiOperation("修改批而未供")
	//@PreAuthorize("@ss.hasPermi('idle:provided:edit')")
	@Log(title = "批而未供", businessType = BusinessType.UPDATE)
	@RepeatSubmit
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataLandNotProvided bo) {
		return toAjax(iDataLandNotProvidedService.updateByEditBo(bo) ? 1 : 0);
	}

	/**
	 * 删除批而未供
	 */
	@ApiOperation("删除批而未供")
	//@PreAuthorize("@ss.hasPermi('idle:provided:remove')")
	@Log(title = "批而未供", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataLandNotProvidedService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}

	@ApiOperation("统计批而未供数据")
	@GetMapping("/unProvidedStat")
	public AjaxResult unProvidedStat(String regionCode){
		LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
		SysUser sysUser = loginUser.getUser();
		List<StatisticsVo> voList = new ArrayList<>();
		if (null != sysUser) {
			if (StringUtils.isBlank(sysUser.getUserType()) || sysUser.getUserType().equals("1")) {
				// 只能看自己县区的
				voList = iDataLandNotProvidedService.unprovidedStat(sysUser.getRegionCode());
			}else{
				// 能查看所有的
				if (StringUtils.isNotBlank(regionCode)){
					// 根据指定的行政区过滤
					voList = iDataLandNotProvidedService.unprovidedStat(regionCode);
				}else{
					// 统计所有行政区
					voList = iDataLandNotProvidedService.unprovidedStat("");
				}
			}
		}
		return AjaxResult.success("成功获取批而未供统计数据",voList);
	}
}
