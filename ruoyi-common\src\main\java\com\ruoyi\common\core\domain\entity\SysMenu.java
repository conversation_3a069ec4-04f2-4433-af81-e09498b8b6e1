package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.*;

/**
 * 菜单权限表 sys_menu
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_menu")
public class SysMenu implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 菜单ID
	 */
	@TableId(value = "menu_id", type = IdType.ASSIGN_UUID)
	private String menuId;

	/**
	 * 菜单名称
	 */
	@NotBlank(message = "菜单名称不能为空")
	@Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
	private String menuName;

	/**
	 * 父菜单名称
	 */
	@TableField(exist = false)
	private String parentName;

	/**
	 * 父菜单ID
	 */
	private String parentId;

	/**
	 * 显示顺序
	 */
	@NotBlank(message = "显示顺序不能为空")
	private String orderNum;

	/**
	 * 路由地址
	 */
	@Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
	private String path;

	/**
	 * 组件路径
	 */
	@Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
	private String component;

	/**
	 * 是否为外链（0是 1否）
	 */
	private String isFrame;

	/**
	 * 是否缓存（0缓存 1不缓存）
	 */
	private String isCache;

	/**
	 * 类型（M目录 C菜单 F按钮）
	 */
	@NotBlank(message = "菜单类型不能为空")
	private String menuType;

	/**
	 * 显示状态（0显示 1隐藏）
	 */
	private String visible;

	/**
	 * 菜单状态（0显示 1隐藏）
	 */
	private String status;

	/**
	 * 权限字符串
	 */
	@Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
	private String perms;

	/**
	 * 菜单图标
	 */
	private String icon;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 请求参数
	 */
	@TableField(exist = false)
	private Map<String, Object> params = new HashMap<>();

	/**
	 * 子菜单
	 */
	@TableField(exist = false)
	private List<SysMenu> children = new ArrayList<SysMenu>();

}
