package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataMeetingRecord;
import com.ruoyi.idle.domain.bo.DataMeetingRecordQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 会议记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-19
 */
public interface IDataMeetingRecordService extends IServicePlus<DataMeetingRecord> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataMeetingRecord queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataMeetingRecord> queryPageList(DataMeetingRecordQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataMeetingRecord> queryList(DataMeetingRecordQueryBo bo);

	/**
	 * 根据新增业务对象插入会议记录
	 *
	 * @param bo 会议记录 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataMeetingRecord bo);

	/**
	 * 根据编辑业务对象修改会议记录
	 *
	 * @param bo 会议记录 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataMeetingRecord bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
