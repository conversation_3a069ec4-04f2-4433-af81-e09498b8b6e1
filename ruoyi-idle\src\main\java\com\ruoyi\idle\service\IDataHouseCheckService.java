package com.ruoyi.idle.service;


import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataHouseCheck;
import com.ruoyi.idle.domain.bo.DataHouseCheckQueryBo;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataHouseCheckExportVo;
import com.ruoyi.idle.domain.vo.DataHouseCheckVo;

import java.util.Collection;
import java.util.List;

/**
 * 房屋建筑信息外业调查 Service接口
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IDataHouseCheckService extends IServicePlus<DataHouseCheck> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataHouseCheckVo queryById(Long id);

	DataHouseCheck queryByCgltId(String clgtId);

	DataHouseCheck selectByTaskId(String clgtId);

	List<DataHouseCheck> appPulList(String regionCode, String userId);
	/**
	 * 查询列表
	 */
	TableDataInfo<DataHouseCheckVo> queryPageList(DataHouseCheckQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataHouseCheckVo> queryList(DataHouseCheckQueryBo bo);

	List<DataHouseCheckExportVo> queryHouseList(DataPatrolTaskQueryBo bo);

	/**
	 * 根据新增业务对象插入房屋建筑信息外业调查
	 *
	 * @param bo 房屋建筑信息外业调查 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataHouseCheckQueryBo bo);

	/**
	 * 根据编辑业务对象修改房屋建筑信息外业调查
	 *
	 * @param bo 房屋建筑信息外业调查 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataHouseCheckQueryBo bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
