package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;
    import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 核查记录 视图对象 data_verify_record
 *
 * <AUTHOR>
 * @date 2023-07-13
 */
@Data
@ApiModel("核查记录 视图对象")
public class DataVerifyRecordVo {

    private static final long serialVersionUID = 1L;

    /** 主键编号 */
    @ApiModelProperty("主键编号")
    private String id;

                                    /** 巡查任务编号 */
                                                            @Excel(name = "巡查任务编号")
                    @ApiModelProperty("巡查任务编号")
        private String taskId;

                                /** 闲置土地或批而未供数据编号 */
                                                            @Excel(name = "闲置土地或批而未供数据编号")
                    @ApiModelProperty("闲置土地或批而未供数据编号")
        private String dataId;

                                /** 项目类型 */
                                                            @Excel(name = "项目类型")
                    @ApiModelProperty("项目类型")
        private String projectType;

                                /** 目录编号 */
                                                            @Excel(name = "目录编号")
                    @ApiModelProperty("目录编号")
        private String catalogId;

                                /** 目录名称 */
                                                            @Excel(name = "目录名称")
                    @ApiModelProperty("目录名称")
        private String catalogName;

                                /** 是否审核通过 */
                                                            @Excel(name = "是否审核通过")
                    @ApiModelProperty("是否审核通过")
        private String isProcess;

                                /** 视频连线情况是否属实 */
                                                            @Excel(name = "视频连线情况是否属实")
                    @ApiModelProperty("视频连线情况是否属实")
        private String videoIsReal;

                                /** 实地核查情况是否属实 */
                                                            @Excel(name = "实地核查情况是否属实")
                    @ApiModelProperty("实地核查情况是否属实")
        private String siteIsReal;

                                /** 处置是否合规 */
                                                            @Excel(name = "处置是否合规")
                    @ApiModelProperty("处置是否合规")
        private String handleIsCorrect;

                                                                                /** 核查情况 */
                                                            @Excel(name = "核查情况")
                    @ApiModelProperty("核查情况")
        private String verificationSituation;

                                /** 核查选项 */
                                                            @Excel(name = "核查选项")
                    @ApiModelProperty("核查选项")
        private String verificationSelect;


}
