package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataIdleVerify;
import com.ruoyi.idle.domain.bo.DataIdleVerifyQueryBo;
import com.ruoyi.idle.mapper.DataIdleVerifyMapper;
import com.ruoyi.idle.service.IDataIdleVerifyService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 闲置土地核查记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Service
public class DataIdleVerifyServiceImpl extends ServicePlusImpl<DataIdleVerifyMapper, DataIdleVerify> implements IDataIdleVerifyService {

	@Override
	public DataIdleVerify queryById(String id) {
		return getVoById(id, DataIdleVerify.class);
	}

	@Override
	public TableDataInfo<DataIdleVerify> queryPageList(DataIdleVerifyQueryBo bo) {
		PagePlus<DataIdleVerify, DataIdleVerify> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataIdleVerify.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataIdleVerify> queryList(DataIdleVerifyQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataIdleVerify.class);
	}

	private LambdaQueryWrapper<DataIdleVerify> buildQueryWrapper(DataIdleVerifyQueryBo bo) {
		LambdaQueryWrapper<DataIdleVerify> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getTaskId() != null, DataIdleVerify::getTaskId, bo.getTaskId());
		lqw.eq(bo.getIdleId() != null, DataIdleVerify::getIdleId, bo.getIdleId());
		lqw.eq(StrUtil.isNotBlank(bo.getCountyCode()), DataIdleVerify::getCountyCode, bo.getCountyCode());
		lqw.like(StrUtil.isNotBlank(bo.getCountyName()), DataIdleVerify::getCountyName, bo.getCountyName());
		lqw.like(StrUtil.isNotBlank(bo.getProjectName()), DataIdleVerify::getProjectName, bo.getProjectName());
		lqw.eq(StrUtil.isNotBlank(bo.getYear()), DataIdleVerify::getYear, bo.getYear());
		lqw.eq(StrUtil.isNotBlank(bo.getQuarter()), DataIdleVerify::getQuarter, bo.getQuarter());
		lqw.eq(StrUtil.isNotBlank(bo.getAddress()), DataIdleVerify::getAddress, bo.getAddress());
		lqw.eq(StrUtil.isNotBlank(bo.getSupplyType()), DataIdleVerify::getSupplyType, bo.getSupplyType());
		lqw.eq(StrUtil.isNotBlank(bo.getSupervisionNo()), DataIdleVerify::getSupervisionNo, bo.getSupervisionNo());
		lqw.eq(StrUtil.isNotBlank(bo.getContractNo()), DataIdleVerify::getContractNo, bo.getContractNo());
		lqw.eq(StrUtil.isNotBlank(bo.getParcelNum()), DataIdleVerify::getParcelNum, bo.getParcelNum());
		lqw.eq(bo.getLandArea() != null, DataIdleVerify::getLandArea, bo.getLandArea());
		lqw.eq(StrUtil.isNotBlank(bo.getLandUse()), DataIdleVerify::getLandUse, bo.getLandUse());
		lqw.eq(StrUtil.isNotBlank(bo.getWarrantNum()), DataIdleVerify::getWarrantNum, bo.getWarrantNum());
		lqw.eq(StrUtil.isNotBlank(bo.getHandleType()), DataIdleVerify::getHandleType, bo.getHandleType());
		lqw.eq(StrUtil.isNotBlank(bo.getHasReturnPact()), DataIdleVerify::getHasReturnPact, bo.getHasReturnPact());
		lqw.eq(StrUtil.isNotBlank(bo.getHasReplacePact()), DataIdleVerify::getHasReplacePact, bo.getHasReplacePact());
		lqw.eq(StrUtil.isNotBlank(bo.getHasRevokeData()), DataIdleVerify::getHasRevokeData, bo.getHasRevokeData());
		lqw.eq(StrUtil.isNotBlank(bo.getHasReplaceData()), DataIdleVerify::getHasReplaceData, bo.getHasReplaceData());
		lqw.eq(StrUtil.isNotBlank(bo.getHasPayProof()), DataIdleVerify::getHasPayProof, bo.getHasPayProof());
		lqw.eq(StrUtil.isNotBlank(bo.getHasNewSupplyData()), DataIdleVerify::getHasNewSupplyData, bo.getHasNewSupplyData());
		lqw.eq(StrUtil.isNotBlank(bo.getHasStartData()), DataIdleVerify::getHasStartData, bo.getHasStartData());
		lqw.eq(StrUtil.isNotBlank(bo.getHasStartPhoto()), DataIdleVerify::getHasStartPhoto, bo.getHasStartPhoto());
		lqw.eq(StrUtil.isNotBlank(bo.getVideoIsReal()), DataIdleVerify::getVideoIsReal, bo.getVideoIsReal());
		lqw.eq(StrUtil.isNotBlank(bo.getSiteIsReal()), DataIdleVerify::getSiteIsReal, bo.getSiteIsReal());
		lqw.eq(StrUtil.isNotBlank(bo.getHandleIsCorrect()), DataIdleVerify::getHandleIsCorrect, bo.getHandleIsCorrect());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataIdleVerify::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataIdleVerify::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataIdleVerify::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataIdleVerify::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataIdleVerify::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataIdleVerify::getUpdateTime, bo.getUpdateTime());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataIdleVerify bo) {
		return save(bo);
	}

	@Override
	public Boolean updateByEditBo(DataIdleVerify bo) {
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataIdleVerify entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
