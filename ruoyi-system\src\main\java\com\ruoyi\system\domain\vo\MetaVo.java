package com.ruoyi.system.domain.vo;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * 路由显示信息
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MetaVo {
	/**
	 * 设置该路由在侧边栏和面包屑中展示的名字
	 */
	private String title;

	/**
	 * 设置该路由的图标，对应路径src/assets/icons/svg
	 */
	private String icon;

	/**
	 * 设置为true，则不会被 <keep-alive>缓存
	 */
	private boolean noCache;

	public MetaVo(String title, String icon) {
		this.title = title;
		this.icon = icon;
	}

	public MetaVo(String title, String icon, boolean noCache) {
		this.title = title;
		this.icon = icon;
		this.noCache = noCache;
	}

}
