package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 巡查组 对象 data_patrol_group
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_patrol_group")
public class DataPatrolGroup implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 行政区代码
	 */
	@ApiModelProperty(value = "行政区代码")
	@Excel(name = "行政区代码")
	private String countyCode;

	/**
	 * 行政区名称
	 */
	@ApiModelProperty(value = "行政区名称")
	@Excel(name = "行政区名称")
	private String countyName;

	/**
	 * 巡查组名称
	 */
	@ApiModelProperty(value = "巡查组名称")
	@Excel(name = "巡查组名称")
	private String groupName;

	/**
	 * 巡查组权限（默认所有）
	 */
	@ApiModelProperty(value = "巡查组权限")
	private String groupAuthor;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Excel(name = "备注")
	private String remark;

	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}
