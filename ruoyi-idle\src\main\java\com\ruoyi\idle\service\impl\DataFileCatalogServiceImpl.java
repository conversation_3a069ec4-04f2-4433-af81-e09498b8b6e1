package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.bo.DataFileCatalogQueryBo;
import com.ruoyi.idle.mapper.DataFileCatalogMapper;
import com.ruoyi.idle.service.IDataFileCatalogService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 巡查附件目录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class DataFileCatalogServiceImpl extends ServicePlusImpl<DataFileCatalogMapper, DataFileCatalog> implements IDataFileCatalogService {

	@Override
	public DataFileCatalog queryById(String id) {
		return getVoById(id, DataFileCatalog.class);
	}

	@Override
	public TableDataInfo<DataFileCatalog> queryPageList(DataFileCatalogQueryBo bo) {
		PagePlus<DataFileCatalog, DataFileCatalog> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataFileCatalog.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataFileCatalog> queryList(DataFileCatalogQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataFileCatalog.class);
	}

	private LambdaQueryWrapper<DataFileCatalog> buildQueryWrapper(DataFileCatalogQueryBo bo) {
		LambdaQueryWrapper<DataFileCatalog> lqw = Wrappers.lambdaQuery();
		lqw.like(StrUtil.isNotBlank(bo.getName()), DataFileCatalog::getName, bo.getName());
		lqw.eq(bo.getParentId() != null, DataFileCatalog::getParentId, bo.getParentId());
		lqw.eq(StrUtil.isNotBlank(bo.getCatalogType()), DataFileCatalog::getCatalogType, bo.getCatalogType());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataFileCatalog::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataFileCatalog::getStatus, bo.getStatus());
		lqw.eq(StrUtil.isNotBlank(bo.getCreateBy()), DataFileCatalog::getCreateBy, bo.getCreateBy());
		lqw.eq(bo.getCreateTime() != null, DataFileCatalog::getCreateTime, bo.getCreateTime());
		lqw.eq(StrUtil.isNotBlank(bo.getUpdateBy()), DataFileCatalog::getUpdateBy, bo.getUpdateBy());
		lqw.eq(bo.getUpdateTime() != null, DataFileCatalog::getUpdateTime, bo.getUpdateTime());
		lqw.orderByAsc(DataFileCatalog::getOrderNum);
		return lqw;
	}

	@Override
	public Boolean insert(DataFileCatalog bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean update(DataFileCatalog bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataFileCatalog entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
