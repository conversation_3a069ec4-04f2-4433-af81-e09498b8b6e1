package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataHouseCheck;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.bo.DataHouseCheckQueryBo;
import com.ruoyi.idle.domain.bo.DataPatrolTaskQueryBo;
import com.ruoyi.idle.domain.vo.DataHouseCheckExportVo;
import com.ruoyi.idle.domain.vo.DataHouseCheckVo;
import com.ruoyi.idle.mapper.DataHouseCheckMapper;
import com.ruoyi.idle.service.IDataHouseCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 房屋建筑信息外业调查 Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class DataHouseCheckServiceImpl extends ServicePlusImpl<DataHouseCheckMapper, DataHouseCheck> implements IDataHouseCheckService {
	@Autowired
	DataHouseCheckMapper dataHouseCheckMapper;
	@Override
	public DataHouseCheckVo queryById(Long id) {
		return getVoById(id, DataHouseCheckVo.class);
	}

	@Override
	public List<DataHouseCheck> appPulList(String regionCode, String userId){
		return dataHouseCheckMapper.appPulList(regionCode,userId);
	}

	@Override
	public DataHouseCheck queryByCgltId(String clgtId) {
		return dataHouseCheckMapper.selectByClgtId(clgtId);
	}

	@Override
	public DataHouseCheck selectByTaskId(String clgtId) {
		return dataHouseCheckMapper.selectByTaskId(clgtId);
	}

	@Override
	public TableDataInfo<DataHouseCheckVo> queryPageList(DataHouseCheckQueryBo bo) {
		PagePlus<DataHouseCheck, DataHouseCheckVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataHouseCheckVo.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataHouseCheckVo> queryList(DataHouseCheckQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataHouseCheckVo.class);
	}

	@Override
	public List<DataHouseCheckExportVo> queryHouseList(DataPatrolTaskQueryBo bo) {
		return dataHouseCheckMapper.queryHouseList(bo);
	}

	private LambdaQueryWrapper<DataHouseCheck> buildQueryWrapper(DataHouseCheckQueryBo bo) {
		Map<String, Object> params = bo.getParams();
		LambdaQueryWrapper<DataHouseCheck> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getJzbh()), DataHouseCheck::getJzbh, bo.getJzbh());
		lqw.eq(StrUtil.isNotBlank(bo.getMc()), DataHouseCheck::getMc, bo.getMc());
		lqw.eq(bo.getJdmj() != null, DataHouseCheck::getJdmj, bo.getJdmj());
		lqw.eq(bo.getDscs() != null, DataHouseCheck::getDscs, bo.getDscs());
		lqw.eq(bo.getDsjzmj() != null, DataHouseCheck::getDsjzmj, bo.getDsjzmj());
		lqw.eq(bo.getJzgd() != null, DataHouseCheck::getJzgd, bo.getJzgd());
		lqw.eq(StrUtil.isNotBlank(bo.getYxjz()), DataHouseCheck::getYxjz, bo.getYxjz());
		lqw.eq(bo.getFwts() != null, DataHouseCheck::getFwts, bo.getFwts());
		lqw.eq(StrUtil.isNotBlank(bo.getXxdz()), DataHouseCheck::getXxdz, bo.getXxdz());
		lqw.eq(bo.getJznd() != null, DataHouseCheck::getJznd, bo.getJznd());
		lqw.eq(bo.getDcsj() != null, DataHouseCheck::getDcsj, bo.getDcsj());
		lqw.eq(StrUtil.isNotBlank(bo.getJzzt()), DataHouseCheck::getJzzt, bo.getJzzt());
		lqw.eq(StrUtil.isNotBlank(bo.getJglx()), DataHouseCheck::getJglx, bo.getJglx());
		lqw.eq(StrUtil.isNotBlank(bo.getSjyt()), DataHouseCheck::getSjyt, bo.getSjyt());
		lqw.eq(StrUtil.isNotBlank(bo.getGhyt()), DataHouseCheck::getGhyt, bo.getGhyt());
		lqw.eq(StrUtil.isNotBlank(bo.getJgyt()), DataHouseCheck::getJgyt, bo.getJgyt());
		lqw.eq(StrUtil.isNotBlank(bo.getJzfcyt()), DataHouseCheck::getJzfcyt, bo.getJzfcyt());
		lqw.eq(StrUtil.isNotBlank(bo.getFytlcd()), DataHouseCheck::getFytlcd, bo.getFytlcd());
		lqw.eq(bo.getFytjzmj() != null, DataHouseCheck::getFytjzmj, bo.getFytjzmj());
		lqw.eq(StrUtil.isNotBlank(bo.getDxcs()), DataHouseCheck::getDxcs, bo.getDxcs());
		lqw.eq(bo.getDxjzmj() != null, DataHouseCheck::getDxjzmj, bo.getDxjzmj());
		lqw.eq(StrUtil.isNotBlank(bo.getFwxz()), DataHouseCheck::getFwxz, bo.getFwxz());
		lqw.eq(StrUtil.isNotBlank(bo.getXzsj()), DataHouseCheck::getXzsj, bo.getXzsj());
		lqw.eq(StrUtil.isNotBlank(bo.getYdsyq()), DataHouseCheck::getYdsyq, bo.getYdsyq());
		lqw.eq(StrUtil.isNotBlank(bo.getTxsm()), DataHouseCheck::getTxsm, bo.getTxsm());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataHouseCheck::getDeleteFlag, bo.getDeleteFlag());
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataHouseCheckQueryBo bo) {
		DataHouseCheck add = BeanUtil.toBean(bo, DataHouseCheck.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	@Override
	public Boolean updateByEditBo(DataHouseCheckQueryBo bo) {
		DataHouseCheck update = BeanUtil.toBean(bo, DataHouseCheck.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataHouseCheck entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
