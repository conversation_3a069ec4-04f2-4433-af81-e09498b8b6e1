<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataVerifyRecordMapper">

    <resultMap type="com.ruoyi.idle.domain.DataVerifyRecord" id="DataVerifyRecordResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="dataId" column="data_id"/>
        <result property="projectType" column="project_type"/>
        <result property="catalogId" column="catalog_id"/>
        <result property="catalogName" column="catalog_name"/>
        <result property="isProcess" column="is_process"/>
        <result property="videoIsReal" column="video_is_real"/>
        <result property="siteIsReal" column="site_is_real"/>
        <result property="handleIsCorrect" column="handle_is_correct"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="verificationSituation" column="verification_situation"/>
        <result property="verificationSelect" column="verification_select"/>
    </resultMap>


</mapper>
