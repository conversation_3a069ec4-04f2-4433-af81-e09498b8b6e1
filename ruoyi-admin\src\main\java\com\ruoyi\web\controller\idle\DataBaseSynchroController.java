package com.ruoyi.web.controller.idle;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysDictType;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.idle.domain.*;
import com.ruoyi.idle.domain.bo.DataAuditRecordQueryBo;
import com.ruoyi.idle.service.*;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.message.ReusableMessage;
import org.apache.poi.ss.formula.functions.FactDouble;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;


import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * @Created with IntelliJ IDEA.
 * @Title: DataBaseSynchroController
 * @Description: com.ruoyi.web.controller.idle
 * @Author: HongDeng
 * @Date: 2021/9/2 11:02
 * @Version: 1.0.0
 **/
@Api(value = "数据同步控制器", tags = {"数据同步"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/db")
@Slf4j
public class DataBaseSynchroController {

	private final IDataIdleLandService idleLandService;
	private final IDataPatrolTaskService patrolTaskService;
	private final IDataPatrolGroupService patrolGroupService;
	private final IDataGroupUserService groupUserService;
	private final IDataTaskUserService taskUserService;
	private final ISysUserService userService;
	private final ISysRoleService roleService;
	private final SysUserRoleMapper userRoleMapper;
	private final ISysDictDataService dictDataService;
	private final ISysDictTypeService dictTypeService;
	private final IDataAttachmentService attachmentService;
	private final IDataMeetingRecordService recordService;
	private final IDataMeetingUserService meetingUserService;
	private final IDataSynchroRecordService synchroRecordService;


	/**
	 * 数据同步列表
	 */
	@ApiOperation("数据同步列表分页")
	@GetMapping("/pageList")
	public TableDataInfo<DataSynchroRecord> list(@Validated DataSynchroRecord bo) {
		return synchroRecordService.queryPageList(bo);
	}


	/**
	 * Mysql 数据备份
	 *
	 * @return
	 */
	@ApiOperation("数据库备份")
	@GetMapping("/backup")
	public AjaxResult dumpMysql() {
		log.info("当前数据库备份时间：{}", DateUtil.now());
		String FILE_PATH = RuoYiConfig.getBackUpPath();
		// 获取Runtime实例
		Runtime runtime = Runtime.getRuntime();
		String backFilePath = "backup_" + new Date().getTime() + ".sql";
		StringBuilder cmd = new StringBuilder();
		// 拼接备份命令
		cmd.append("mysqldump")
//			.append(" --lock-all-tables")
//			.append("--single-transaction")
			.append(" --skip-extended-insert")
			.append(" -h").append("127.0.0.1")
			.append(" -P").append("3306")
			.append(" -u").append("root")
			.append(" -p").append("hd531840")
			.append(" --default-character-set=utf8")
			.append(" idle_patrol")
			.append(" >").append(backFilePath);
		// 执行CMD命令
		log.info("数据库备份命令为：{}", cmd);
		if (!FileUtil.exist(FILE_PATH)) {
			FileUtil.mkdir(FILE_PATH);
		}
		try {
			// windows 操作系统
			String[] command = {"cmd", "/c", cmd.toString()};
			// linux 操作系统
			// String[] command = {"/bin/sh", "-c",  stmt};
			Process process = runtime.exec(command);
			InputStream input = process.getInputStream();
			System.out.println(IOUtils.toString(input, String.valueOf(StandardCharsets.UTF_8)));
			// 若有错误或者警告信息则输出
			InputStream errorStream = process.getErrorStream();
			System.out.println(IOUtils.toString(errorStream, "gbk"));
			input.close();
			errorStream.close();
			if (process.waitFor() == 0) {
				log.info("Mysql 数据库备份成功,备份文件名：{}", backFilePath);
				return AjaxResult.success("数据库备份成功,备份文件所在地:" + FILE_PATH + backFilePath);
			} else {
				return AjaxResult.error("网络异常 数据库备份失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxResult.error("网络异常");
		}
	}


	/**
	 * mysql 数据恢复
	 *
	 * @param fileName
	 * @return
	 */
	@ApiOperation("数据库恢复")
	@GetMapping("/rollback")
	public AjaxResult restoreMysql(String fileName) {
		String FILE_PATH = RuoYiConfig.getBackUpPath();
		try {
			Runtime runtime = Runtime.getRuntime();
			String realFilePath = FILE_PATH + fileName;
			StringBuilder cmd = new StringBuilder();
			// 拼接备份命令
			cmd.append("mysql")
				.append(" -h").append("127.0.0.1")
				.append(" -P").append("3306")
				.append(" -u").append("root")
				.append(" -p").append("hd531840")
				.append(" idle_patrol")
				.append(" <").append(realFilePath);

			// windows 操作系统
			String[] command = {"cmd", "/c", cmd.toString()};
			// linux 操作系统
			// String[] command = {"/bin/sh", "-c",  stmt};
			Process process = runtime.exec(command);
			OutputStream outputStream = process.getOutputStream();
			OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
			writer.flush();
			outputStream.close();
			writer.close();
			if (process.waitFor() == 0) {
				return AjaxResult.success("还原的备份文件" + FILE_PATH + fileName + "成功");
			} else {
				return AjaxResult.error("网络异常 数据库备份失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return AjaxResult.error("网络异常 数据库备份失败");
		}
	}
	/**
	 * 将文件转换成byte数组
	 * @param tradeFile
	 * @return
	 */
	public static byte[] File2Byte(File tradeFile){
		byte[] buffer = null;
		try
		{
			FileInputStream fis = new FileInputStream(tradeFile);
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			byte[] b = new byte[1024];
			int n;
			while ((n = fis.read(b)) != -1)
			{
				bos.write(b, 0, n);
			}
			fis.close();
			bos.close();
			buffer = bos.toByteArray();
		}catch (FileNotFoundException e){
			e.printStackTrace();
		}catch (IOException e){
			e.printStackTrace();
		}
		return buffer;
	}

	private void saveRecord(DataSynchroRecord record,String status,String msg){
		if (record != null){
			record.setErrorMsg(msg);
			record.setStatus(status);
			synchroRecordService.save(record);
		}
	}


	private void generatorZipData(String fileName,String jsonData, ZipOutputStream zip) throws IOException {
		zip.putNextEntry(new ZipEntry(Objects.requireNonNull(fileName)));
		IOUtils.write(jsonData, zip, "UTF-8");
		zip.flush();
		zip.closeEntry();
	}

	private <T>void dataToZip(List<T> list,String fileName,ZipOutputStream zip) throws IOException {
		if (list.size() != 0){
			JSONArray jsonArray = JSONUtil.parseArray(list,getJsonConfig());
			String jsonStr = jsonArray.toStringPretty();
			generatorZipData(fileName,jsonStr,zip);
		}
	}

	private void generatorZipData(File file, ZipOutputStream zip) throws IOException {
		if (file != null){
			zip.putNextEntry(new ZipEntry(Objects.requireNonNull(FileNameUtil.getName(file))));
			byte[] bytes = File2Byte(file);
			IOUtils.write(bytes, zip);
			zip.flush();
			zip.closeEntry();
		}
	}

	private JSONConfig getJsonConfig(){
		JSONConfig config = new JSONConfig();
		config.setIgnoreNullValue(false);
		config.setDateFormat("yyyy-MM-dd HH:mm:ss");
		return config;
	}


	private Date getLastSynchroTime(String type){
		// 如果这个表中没有记录也是返回现在的时间
		LambdaQueryWrapper<DataSynchroRecord> queryWrapper = new LambdaQueryWrapper<>();
		if (type.equals("0")){
			// 内到外
			queryWrapper.eq(DataSynchroRecord :: getSynchroType,"获取内网数据");
		}else if (type.equals("1")){
			// 外到内
			queryWrapper.eq(DataSynchroRecord :: getSynchroType,"获取外网数据");
		}
		// 成功的记录
		queryWrapper.eq(DataSynchroRecord :: getStatus,"0");
		// 按照时间降序
		queryWrapper.orderByDesc(DataSynchroRecord :: getCreateTime);
		List<DataSynchroRecord> records = synchroRecordService.list(queryWrapper);
		if (records.size() != 0){
			// 取第一条数据的创建时间
			DataSynchroRecord record = records.get(0);
			return record.getCreateTime();
		}else{
			// 不然就取过去三天的数据
			return DateUtil.offsetDay(DateUtil.parse(DateUtil.today()),-3);
		}
	}

	// 从内网同步到外网
	private void getIdleLand(String startTime,String date,ZipOutputStream zip) throws IOException {
		List<DataIdleLand> landList = idleLandService.getAllIdleLandList(startTime);
		String fileName = "DataIdleLand_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getPatrolGroup(String startTime,String date,ZipOutputStream zip) throws IOException {
		List<DataPatrolGroup> landList = patrolGroupService.getAllPatrolGroupList(startTime);
		String fileName = "DataPatrolGroup_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getGroupUser(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<DataGroupUser> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataGroupUser :: getUpdateTime,startTime)
				.or()
				.ge(DataGroupUser :: getCreateTime,startTime);
		}
		List<DataGroupUser> landList = groupUserService.list(queryWrapper);
		String fileName = "DataGroupUser_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getPatrolTask(String startTime,String date,ZipOutputStream zip) throws IOException {
		List<DataPatrolTask> landList = patrolTaskService.getAllPatrolTaskList(startTime);
		String fileName = "DataPatrolTask_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getTaskUser(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<DataTaskUser> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataTaskUser :: getUpdateTime,startTime)
				.or()
				.ge(DataTaskUser :: getCreateTime,startTime);
		}
		List<DataTaskUser> landList = taskUserService.list(queryWrapper);
		String fileName = "DataTaskUser_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getDictData(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(SysDictData :: getUpdateTime,startTime)
				.or()
				.ge(SysDictData :: getCreateTime,startTime);
		}
		List<SysDictData> landList = dictDataService.list(queryWrapper);
		String fileName = "SysDictData_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getDictType(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<SysDictType> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(SysDictType :: getUpdateTime,startTime)
				.or()
				.ge(SysDictType :: getCreateTime,startTime);
		}
		List<SysDictType> landList = dictTypeService.list(queryWrapper);
		String fileName = "SysDictType_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getUser(String startTime,String date,ZipOutputStream zip) throws IOException {
		List<SysUser> landList = userService.getAllUserList(startTime);
		String fileName = "SysUser_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getRole(String startTime,String date,ZipOutputStream zip) throws IOException {
		List<SysRole> landList = roleService.getAllUserList(startTime);
		String fileName = "SysRole_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getUserRole(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(SysUserRole :: getUpdateTime,startTime)
				.or()
				.ge(SysUserRole :: getCreateTime,startTime);
		}
		List<SysUserRole> landList = userRoleMapper.selectList(queryWrapper);
		String fileName = "SysUserRole_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}


	// 从外网同步到内网
	private void getAttachment(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<DataAttachment> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataAttachment :: getUpdateTime,startTime)
				.or()
				.ge(DataAttachment :: getCreateTime,startTime);
		}
		List<DataAttachment> attachments = attachmentService.list(queryWrapper);
		if (attachments.size() != 0){
			JSONArray jsonArray = JSONUtil.parseArray(attachments,getJsonConfig());
			String fileName = "DataAttachment_" + date + ".json";
			String jsonStr = jsonArray.toStringPretty();
			generatorZipData(fileName,jsonStr,zip);

			// 获取需要同步到内网的附件数据打包到一个zip文件中-->再打包到同步数据zip中
			// step.1
			File tempFile = getAttachmentZipFile(attachments,date);
			// step.2
			generatorZipData(tempFile,zip);
			// step.3 删除打包的文件
			FileUtil.del(tempFile);
		}
	}

	private void getMeetingRecord(String startTime,String date,ZipOutputStream zip) throws IOException{
		LambdaQueryWrapper<DataMeetingRecord> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataMeetingRecord :: getUpdateTime,startTime)
				.or()
				.ge(DataMeetingRecord :: getCreateTime,startTime);
		}
		List<DataMeetingRecord> landList = recordService.list(queryWrapper);
		String fileName = "DataMeetingRecord_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getMeetingUser(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<DataMeetingUser> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataMeetingUser :: getUpdateTime,startTime)
				.or()
				.ge(DataMeetingUser :: getCreateTime,startTime);
		}
		List<DataMeetingUser> landList = meetingUserService.list(queryWrapper);
		String fileName = "DataMeetingUser_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private void getSynchroRecord(String startTime,String date,ZipOutputStream zip) throws IOException {
		LambdaQueryWrapper<DataSynchroRecord> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(startTime)){
			queryWrapper.ge(DataSynchroRecord :: getUpdateTime,startTime)
				.or()
				.ge(DataSynchroRecord :: getCreateTime,startTime);
		}
		List<DataSynchroRecord> landList = synchroRecordService.list(queryWrapper);
		String fileName = "DataSynchroRecord_" + date + ".json";
		dataToZip(landList,fileName,zip);
	}

	private File getAttachmentZipFile(List<DataAttachment> attachments,String date){
		String uploadPath = RuoYiConfig.getProfile();
		String zipPath = RuoYiConfig.getBackUpPath() +"/zip/file.zip";
		File[] files = new File[attachments.size()];
		for (int i = 0; i < attachments.size(); i++) {
			DataAttachment item = attachments.get(i);
			String fileLocation = item.getLocation();
			String filePath = uploadPath + fileLocation;
			if (FileUtil.exist(filePath)){
				files[i] = FileUtil.file(filePath);
			}
		}
		return ZipUtil.zip(FileUtil.file(zipPath), false,files);
	}


	/**
	 * 获取内网数据
	 * @return
	 */
	@ApiOperation("获取内网数据")
	@GetMapping("/backup/intranet")
	public void getIntranetJsonData(HttpServletResponse response) {
		DataSynchroRecord record = new DataSynchroRecord();
		record.setSynchroType("获取内网数据");
		record.setDeleteFlag("0");
		record.setCreateBy(SecurityUtils.getUsername());
		String format = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
		String oriName = "intranet_"+format+".zip";
		record.setFileName(oriName);
		try {
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			ZipOutputStream zip = new ZipOutputStream(outputStream);
			// 设置压缩级别-->老牛说的压缩和解压都快
			zip.setLevel(Deflater.NO_COMPRESSION);
			Date startDate = getLastSynchroTime("0");
			String formatDateTime = DateUtil.formatDateTime(startDate);
			// 去同步记录表中获取同步成功的最新的一条数据-->获取创建时间字段作为本次同步的开始时间

			getIdleLand(formatDateTime,format,zip);// 备份闲置土地数据
			getPatrolGroup(formatDateTime,format,zip); // 巡查组
			getGroupUser(formatDateTime,format,zip);// 巡查组用户
			getPatrolTask(formatDateTime,format,zip);// 巡查任务
			getTaskUser(formatDateTime,format,zip);// 任务用户
			getDictData(formatDateTime,format,zip);// 字典数据
			getDictType(formatDateTime,format,zip);// 字典类型
			getUser(formatDateTime,format,zip);// 用户
			getRole(formatDateTime,format,zip);// 角色
			getUserRole(formatDateTime,format,zip);// 用户角色关系

			IOUtils.closeQuietly(zip);
			byte[] data = outputStream.toByteArray();
			genCode(response,"intranet_" + format, data);
			saveRecord(record,"0","");
		}catch (Exception ex){
			String errMsg = "获取内网数据出错，原因："+ex.getMessage();
			saveRecord(record,"1",errMsg);
			log.error(errMsg);
		}
	}

	/**
	 * 获取外网数据
	 * @return
	 */
	@ApiOperation("获取外网数据")
	@GetMapping("/backup/extranet")
	public void getExtranetJsonData(HttpServletResponse response) {
		DataSynchroRecord record = new DataSynchroRecord();
		record.setSynchroType("获取外网数据");
		record.setDeleteFlag("0");
		record.setCreateBy(SecurityUtils.getUsername());
		String format = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
		String oriName = "extranet_"+format+".zip";
		record.setFileName(oriName);
		try {
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			ZipOutputStream zip = new ZipOutputStream(outputStream);
			// 设置压缩级别-->老牛说的压缩和解压都快
			zip.setLevel(Deflater.NO_COMPRESSION);
			Date startDate = getLastSynchroTime("1");
			String formatDateTime = DateUtil.formatDateTime(startDate);

			getAttachment(formatDateTime,format,zip);// 备份闲置土地数据
			getMeetingRecord(formatDateTime,format,zip); // 巡查组
			getMeetingUser(formatDateTime,format,zip);// 巡查组用户
			getPatrolTask(formatDateTime,format,zip);// 巡查任务
			getSynchroRecord(formatDateTime,format,zip); // 外网同步记录

			IOUtils.closeQuietly(zip);
			byte[] data = outputStream.toByteArray();
			genCode(response,"extranet_" + format, data);
			saveRecord(record,"0","");
		}catch (Exception ex){
			String errMsg = "获取外网数据出错，原因："+ex.getMessage();
			saveRecord(record,"1",errMsg);
			log.error(errMsg);
		}
	}

	/**
	 * 同步内网数据到外网
	 * @return
	 */
	@ApiOperation("内网数据同步到外网")
	@PostMapping("/synchro/extranet")
	@Transactional(rollbackFor = Exception.class)
	@ApiImplicitParam(name = "file", value = "内网下载的zip文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true)
	public AjaxResult synchroToExtranet(@RequestPart(value = "file")  MultipartFile file){
		String fileDir = RuoYiConfig.getBackUpPath();
		DataSynchroRecord record = new DataSynchroRecord();
		String oriName = file.getOriginalFilename();
		record.setFileName(oriName);
		record.setSynchroType("内网数据同步到外网");
		record.setDeleteFlag("0");
		record.setCreateBy(SecurityUtils.getUsername());
		try{
			if (!StrUtil.contains(oriName,"intranet")){
				String errorMsg = "同步数据文件名称不符合规范，请不要修改同步数据文件名称";
				saveRecord(record,"1",errorMsg);
				return AjaxResult.error(errorMsg);
			}
			String zipExtName = FileNameUtil.extName(oriName);
			if (!"zip".equals(zipExtName)){
				String errorMsg = "同步数据文件格式不符合规范，请不要修改同步数据文件后缀名";
				saveRecord(record,"1",errorMsg);
				return AjaxResult.error(errorMsg);
			}
			String name = FileNameUtil.mainName(oriName);
			String targetDir =  fileDir +"/unZip/"+ name;
			File targetFile = new File(targetDir);
			File unzipFile = ZipUtil.unzip(file.getInputStream(), targetFile,StandardCharsets.UTF_8);
			File[] fileList = FileUtil.ls(targetDir);
			int fileCount = fileList.length;
			if (fileCount == 0){
				String errorMsg = "给定的数据中没有需要同步的数据";
				saveRecord(record,"1",errorMsg);;
				return AjaxResult.error(errorMsg);
			}
			for (File item : fileList) {
				String jsonFileName = FileNameUtil.mainName(item);
				String[] entityArr = jsonFileName.split("_");
				if (entityArr.length == 0){
					String errorMsg = "给定的json数据命名方式不合法,文件名称"+jsonFileName;
					saveRecord(record,"1",errorMsg);
					AjaxResult.error(errorMsg);
				}
				String entityName = entityArr[0];
				JSONArray jsonArray = JSONUtil.readJSONArray(item, StandardCharsets.UTF_8);
				if (jsonArray.size() != 0) {
					switch (entityName) {
						case "DataIdleLand":
							setIdleLand(jsonArray);
							break;
						case "DataPatrolGroup":
							setPatrolGroup(jsonArray);
							break;
						case "DataGroupUser":
							setGroupUser(jsonArray);
							break;
						case "DataPatrolTask":
							setPatrolTask(jsonArray,"0");
							break;
						case "DataTaskUser":
							setTaskUser(jsonArray);
							break;
						case "SysDictData":
							setDictData(jsonArray);
							break;
						case "SysDictType":
							setDictType(jsonArray);
							break;
						case "SysUser":
							setUser(jsonArray);
							break;
						case "SysRole":
							setRole(jsonArray);
							break;
						case "SysUserRole":
							setUserRole(jsonArray);
							break;
					}
				}
			}
			// 删除解压的同步的文件
			FileUtil.del(unzipFile);
			saveRecord(record,"0","");
			return AjaxResult.success("内网数据成功同步到外网");
		}catch (Exception ex){
			String errorMsg = "内网数据同步到外网,错误原因:"+ex.getMessage();
			saveRecord(record,"1",errorMsg);
			return AjaxResult.error(errorMsg);
		}
	}

	/**
	 * 同步外网数据到内网
	 * @return
	 */
	@ApiOperation("外网数据同步到内网")
	@PostMapping("/synchro/intranet")
	@Transactional(rollbackFor = Exception.class)
	@ApiImplicitParam(name = "file", value = "外网下载的zip文件", dataTypeClass = java.io.File.class, dataType = "java.io.File", required = true)
	public AjaxResult synchroToIntranet(@RequestPart(value = "file")  MultipartFile file){
		String fileDir = RuoYiConfig.getBackUpPath();
		DataSynchroRecord record = new DataSynchroRecord();
		String oriName = file.getOriginalFilename();
		record.setFileName(oriName);
		record.setSynchroType("外网数据同步到内网");
		record.setDeleteFlag("0");
		record.setCreateBy(SecurityUtils.getUsername());
		try{
			if (!StrUtil.contains(oriName,"extranet")){
				String errorMsg = "同步数据文件名称不符合规范，请不要修改同步数据文件名称";
				saveRecord(record,"1",errorMsg);
				return AjaxResult.error(errorMsg);
			}
			String zipExtName = FileNameUtil.extName(oriName);
			if (!"zip".equals(zipExtName)){
				String errorMsg = "同步数据文件格式不符合规范，请不要修改同步数据文件后缀名";
				saveRecord(record,"1",errorMsg);
				return AjaxResult.error(errorMsg);
			}
			String name = FileNameUtil.mainName(oriName);
			String targetDir =  fileDir +"unZip/"+ name;
			File targetFile = new File(targetDir);
			File unzipFile = ZipUtil.unzip(file.getInputStream(), targetFile,StandardCharsets.UTF_8);
			File[] fileList = FileUtil.ls(targetDir);
			int fileCount = fileList.length;
			if (fileCount == 0){
				String errorMsg = "给定的数据中没有需要同步的数据";
				saveRecord(record,"1",errorMsg);
				return AjaxResult.error(errorMsg);
			}
			// 首先判断是否有zip文件，有就先解压，再执行后面的代码
			unZipAttachmentFile(fileList,targetDir);
			for (File item : fileList) {
				JSONArray jsonArray = new JSONArray();
				String extName = FileNameUtil.extName(item);
				if (extName != null && extName.equals("json")){
					jsonArray = JSONUtil.readJSONArray(item, StandardCharsets.UTF_8);
					String jsonFileName = FileNameUtil.mainName(item);
					String[] entityArr = jsonFileName.split("_");
					if (entityArr.length == 0) {
						String errorMsg = "给定的json数据命名方式不合法,文件名称"+jsonFileName;
						saveRecord(record,"1",errorMsg);
						return AjaxResult.error(errorMsg);
					}
					String entityName = entityArr[0];
					if (jsonArray.size() != 0) {
						switch (entityName) {
							case "DataAttachment":
								setAttachment(jsonArray,targetDir);
								break;
							case "DataMeetingRecord":
								setMeetingRecord(jsonArray);
								break;
							case "DataMeetingUser":
								setMeetingUser(jsonArray);
								break;
							case "DataPatrolTask":
								setPatrolTask(jsonArray,"1");
								break;
							case "DataSynchroRecord":
								setSynchroRecord(jsonArray);
								break;
						}
					}
				}
			}
			// 删除解压的同步的文件
			FileUtil.del(unzipFile);
			saveRecord(record,"0","");
			return AjaxResult.success("外网数据成功同步到内网");
		}catch (Exception ex){
			String errorMsg = "外网数据同步到内网,错误原因:"+ex.getMessage();
			saveRecord(record,"1",errorMsg);
			return AjaxResult.error(errorMsg);
		}
	}

	// 内网同步到外网
	private void setIdleLand(JSONArray jsonArray) {
		List<DataIdleLand> landList = JSONUtil.toList(jsonArray, DataIdleLand.class);
		if (landList.size() != 0){
			for (DataIdleLand land : landList){
				if (StringUtils.isNotBlank(land.getDeleteFlag()) && land.getDeleteFlag().equals("1")){
					// 删除
					idleLandService.removeById(land.getId());
				}else{
					// 根据id和更新时间判断是否是最新的数据
					DataIdleLand temp = idleLandService.getById(land.getId());
					if ( temp== null){
						// 新增
						idleLandService.insert(land);
					}else{
						// 更新
						if (!ObjectUtil.equal(temp,land)){
							idleLandService.updateById(land);
						}
					}
				}
			}
		}
	}

	private void setPatrolGroup(JSONArray jsonArray) {
		List<DataPatrolGroup> groupList = JSONUtil.toList(jsonArray, DataPatrolGroup.class);
		if (groupList.size() != 0){
			for (DataPatrolGroup item : groupList){
				if (StringUtils.isNotBlank(item.getDeleteFlag()) && item.getDeleteFlag().equals("1")){
					patrolGroupService.removeById(item.getId());
				}else{
					// 根据id和更新时间判断是否是最新的数据
					DataPatrolGroup temp = patrolGroupService.getById(item.getId());
					if ( temp== null) {
						patrolGroupService.insert(item);
					} else {
						if (!ObjectUtil.equal(temp,item)){
							patrolGroupService.updateById(item);
						}
					}
				}
			}
		}
	}

	private void setTaskUser(JSONArray jsonArray) {
		List<DataTaskUser> taskUserList = JSONUtil.toList(jsonArray, DataTaskUser.class);
		if (taskUserList.size() != 0){
			for (DataTaskUser item : taskUserList){
				taskUserService.remove(new LambdaQueryWrapper<DataTaskUser>()
					.eq(DataTaskUser :: getTaskId,item.getTaskId())
				.eq(DataTaskUser :: getUserId,item.getUserId()));
				// 根据id和更新时间判断是否是最新的数据
				DataTaskUser temp = taskUserService.getById(item.getId());
				if (temp == null) {
					taskUserService.insert(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						taskUserService.updateById(item);
					}
				}
			}
		}
	}

	private void setGroupUser(JSONArray jsonArray) {
		List<DataGroupUser> groupUserList = JSONUtil.toList(jsonArray, DataGroupUser.class);
		if (groupUserList.size() != 0){
			for (DataGroupUser item : groupUserList){
				// 根据id和更新时间判断是否是最新的数据
				DataGroupUser temp = groupUserService.getById(item.getId());
				if (temp == null) {
					groupUserService.insert(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						groupUserService.updateById(item);
					}
				}
			}
		}
	}

	private void setPatrolTask(JSONArray jsonArray,String type) {
		List<DataPatrolTask> patrolTasks = JSONUtil.toList(jsonArray, DataPatrolTask.class);
		if (patrolTasks.size() != 0){
			for (DataPatrolTask item : patrolTasks){
				// 根据id和更新时间判断是否是最新的数据
				DataPatrolTask temp = patrolTaskService.getById(item.getId());
				if (type.equals("0")){
					// 内----->外
					if (StringUtils.isNotBlank(item.getDeleteFlag()) && item.getDeleteFlag().equals("1")){
						patrolTaskService.removeById(item.getId());
						// 删除关联的用户
						taskUserService.remove(new LambdaQueryWrapper<DataTaskUser>()
						.eq(DataTaskUser :: getTaskId,item.getId()));
					}else{
						if ( temp== null) {
							patrolTaskService.insert(item);
						} else {
							if (!ObjectUtil.equal(temp,item)){
								patrolTaskService.updateById(item);
							}
						}
					}
				}else if (type.equals("1")){
					// 外----->内
					if(temp != null){
						temp.setPatrolOpinion(item.getPatrolOpinion());
						patrolTaskService.updateById(temp);
					}else{
						log.info("由于内网数据被删除，所以"+item.getProjectName()+"【"+item.getSupervisionNo()+"】"+"该项目无法同步回去");
					}
				}
			}
		}
	}

	private void setDictData(JSONArray jsonArray) {
		List<SysDictData> dictDataList = JSONUtil.toList(jsonArray, SysDictData.class);
		if (dictDataList.size() != 0){
			for (SysDictData item : dictDataList){
				// 根据id和更新时间判断是否是最新的数据
				SysDictData temp = dictDataService.getById(item.getDictCode());
				if (temp == null) {
					dictDataService.insertDictData(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						dictDataService.updateById(item);
					}
				}
			}
		}
	}

	private void setDictType(JSONArray jsonArray) {
		List<SysDictType> dictTypeList = JSONUtil.toList(jsonArray, SysDictType.class);
		if (dictTypeList.size() != 0){
			for (SysDictType item : dictTypeList){
				// 根据id和更新时间判断是否是最新的数据
				SysDictType temp = dictTypeService.getById(item.getDictId());
				if (temp == null) {
					dictTypeService.insertDictType(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						dictTypeService.updateById(item);
					}
				}
			}
		}
	}

	private void setUser(JSONArray jsonArray) {
		List<SysUser> userList = JSONUtil.toList(jsonArray, SysUser.class);
		if (userList.size() != 0){
			for (SysUser item : userList){
				if (StringUtils.isNotBlank(item.getDelFlag()) && item.getDelFlag().equals("1")){
					userService.removeById(item.getUserId());
					 // 删除用户关联的角色
					userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole :: getUserId,item.getUserId()));
				}else{
					LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(SysUser :: getUserId,item.getUserId());
//					queryWrapper.eq(SysUser :: getUserName,item.getUserName())
//						.eq(SysUser :: getPhonenumber,item.getPhonenumber());
					//userService.remove(queryWrapper);
					// 根据id和更新时间判断是否是最新的数据
					SysUser temp = userService.getOne(queryWrapper,false);
					if (temp == null) {
						userService.insertUser(item);
					} else {
						if (!ObjectUtil.equal(temp,item)){
							userService.updateById(item);
						}
					}
				}
			}
		}
	}

	private void setRole(JSONArray jsonArray) {
		List<SysRole> roleList = JSONUtil.toList(jsonArray, SysRole.class);
		if (roleList.size() != 0){
			for (SysRole item : roleList){
				if (StringUtils.isNotBlank(item.getDelFlag()) && item.getDelFlag().equals("1")){
					roleService.removeById(item.getRoleId());
					// 删除用户关联的角色
					userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole :: getRoleId,item.getRoleId()));
				}else{
					// 根据id和更新时间判断是否是最新的数据
					SysRole temp = roleService.getById(item.getRoleId());
					if (temp == null) {
						roleService.insertRole(item);
					} else {
						if (!ObjectUtil.equal(temp,item)){
							roleService.updateById(item);
						}
					}
				}
			}
		}
	}

	private void setUserRole(JSONArray jsonArray) {
		List<SysUserRole> userRoleList = JSONUtil.toList(jsonArray, SysUserRole.class);
		if (userRoleList.size() != 0){
			for (SysUserRole item : userRoleList){
				// 根据用户编号和角色编号判断是否存在记录
				LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
				queryWrapper.eq(SysUserRole :: getUserId,item.getUserId())
					.eq(SysUserRole :: getRoleId,item.getRoleId());
				userRoleMapper.delete(queryWrapper);
				// 根据id和更新时间判断是否是最新的数据
				SysUserRole temp = userRoleMapper.selectById(item.getId());
				if (temp == null) {
					userRoleMapper.insert(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						userRoleMapper.updateById(item);
					}
				}
			}
		}
	}

	// 外网同步到内网
	private void setAttachment(JSONArray jsonArray,String unZipDir) {
		List<DataAttachment> attachmentList = JSONUtil.toList(jsonArray, DataAttachment.class);
		if (attachmentList.size() != 0){
			String profilePath = RuoYiConfig.getProfile();
			log.warn("开始处理附件数据");
			for (DataAttachment item : attachmentList){
				log.warn("开始处理ID为"+item.getId()+"的附件数据");
				// 根据id和更新时间判断是否是最新的数据
				DataAttachment temp = attachmentService.getById(item.getId());
				if ( temp== null) {
					attachmentService.insert(item);
				} else {
					//if (!ObjectUtil.equal(temp,item)){
					//	attachmentService.updateById(item);
					//}
					if (temp.getUrl().equals(item.getUrl())){
						attachmentService.updateById(item);
					}
				}
				String tagFilePath = "";
				if (temp ==null){
					// 使用新增的数据的存储位置
					tagFilePath= profilePath + item.getLocation();
				}else{
					tagFilePath= profilePath + temp.getLocation();
				}
				log.warn("ID为"+item.getId()+"的附件存储位置是"+tagFilePath);
				// 复制文件
				log.warn("开始复制ID为"+item.getId()+"的文件");
				String srcFilePath = unZipDir+ "/file/" + FileNameUtil.getName(new File(tagFilePath));
				if (FileUtil.exist(srcFilePath)){
					FileUtil.copy(srcFilePath,tagFilePath,true);
				}
				log.warn("ID为的文件复制完成");
			}
		}
	}

	private void setMeetingRecord(JSONArray jsonArray) {
		List<DataMeetingRecord> recordList = JSONUtil.toList(jsonArray, DataMeetingRecord.class);
		if (recordList.size() != 0){
			for (DataMeetingRecord item : recordList){
				// 根据id和更新时间判断是否是最新的数据
				DataMeetingRecord temp = recordService.getById(item.getId());
				if ( temp== null) {
					recordService.save(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						recordService.updateById(item);
					}
				}
			}
		}
	}

	private void setMeetingUser(JSONArray jsonArray) {
		List<DataMeetingUser> meetingUserList = JSONUtil.toList(jsonArray, DataMeetingUser.class);
		if (meetingUserList.size() != 0){
			for (DataMeetingUser item : meetingUserList){
				// 根据id和更新时间判断是否是最新的数据
				DataMeetingUser temp = meetingUserService.getById(item.getId());
				if ( temp== null) {
					meetingUserService.save(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						meetingUserService.updateById(item);
					}
				}
			}
		}
	}

	private void setSynchroRecord(JSONArray jsonArray) {
		List<DataSynchroRecord> recordList = JSONUtil.toList(jsonArray, DataSynchroRecord.class);
		if (recordList.size() != 0){
			for (DataSynchroRecord item : recordList){
				// 根据id和更新时间判断是否是最新的数据
				DataSynchroRecord temp = synchroRecordService.getById(item.getId());
				if ( temp== null) {
					synchroRecordService.save(item);
				} else {
					if (!ObjectUtil.equal(temp,item)){
						synchroRecordService.updateById(item);
					}
				}
			}
		}
	}

	private void unZipAttachmentFile(File[] fileList,String targetDir) throws FileNotFoundException {
		for (File item : fileList){
			String extName = FileNameUtil.extName(item);
			if (extName != null ){
				if (extName.equals("zip")){
					// 解压缩
					String attachmentFileDir = targetDir + "/file";
					File attachmentZipFile = new File(attachmentFileDir);
					InputStream zipInputStream = new FileInputStream(item);
					ZipUtil.unzip(zipInputStream, attachmentZipFile,StandardCharsets.UTF_8);
					break;
				}
			}
		}
	}


	/**
	 * 生成zip文件
	 */
	private void genCode(HttpServletResponse response,String fileName, byte[] data) throws IOException
	{
		response.reset();
		response.addHeader("Access-Control-Allow-Origin", "*");
		response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
		response.setHeader("Content-Disposition", "attachment; filename=\""+fileName+".zip\"");
		response.addHeader("Content-Length", "" + data.length);
		response.setContentType("application/octet-stream; charset=UTF-8");
		IOUtils.write(data, response.getOutputStream());
	}
}
