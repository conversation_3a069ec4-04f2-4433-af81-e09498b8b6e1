package com.ruoyi.idle.domain.vo;

;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Created with IntelliJ IDEA.
 * @Title: GroupUserSearchVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/7/9 10:40
 * @Version: 1.0.0
 **/
@Data
public class GroupUserSearchVo {

	/**
	 * 用户组编号
	 */
	@ApiModelProperty(value = "用户组编号")
	private String groupId;

	/**
	 * 用户账号
	 */
	@ApiModelProperty(value = "用户账号")
	private String userName;

	/**
	 * 用户昵称
	 */
	@ApiModelProperty(value = "用户昵称")
	private String nickName;

	/**
	 * 行政区代码
	 */
	@ApiModelProperty(value = "行政区代码")
	private String regionCode;

	/**
	 * 行政区名称
	 */
	@ApiModelProperty(value = "行政区名称")
	private String regionName;

	/**
	 * 手机号码
	 */
	@ApiModelProperty(value = "手机号码")
	private String phoneNumber;

}
