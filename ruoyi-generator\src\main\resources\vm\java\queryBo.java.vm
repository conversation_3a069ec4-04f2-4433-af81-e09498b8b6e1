package ${packageName}.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

#foreach ($import in $importList)
import ${import};
#end
#if($table.crud || $table.sub)
import com.ruoyi.common.core.domain.BaseEntity;
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}分页查询对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
    #if($table.crud || $table.sub)
        #set($Entity="BaseEntity")
    #elseif($table.tree)
        #set($Entity="TreeEntity")
    #end

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("${functionName}分页查询对象")
    public class ${ClassName}QueryBo extends ${Entity} {

    /** 分页大小 */
    @ApiModelProperty("分页大小")
    private Integer pageSize;
    /** 当前页数 */
    @ApiModelProperty("当前页数")
    private Integer pageNum;
    /** 排序列 */
    @ApiModelProperty("排序列")
    private String orderByColumn;
    /** 排序的方向desc或者asc */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;


    #foreach ($column in $columns)
        #if(!$table.isSuperColumn($column.javaField) && $column.query)
            /** $column.columnComment */
            @ApiModelProperty("$column.columnComment")
            private $column.javaType $column.javaField;
        #end
    #end

}
