package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataIdleVerify;
import com.ruoyi.idle.domain.bo.DataIdleVerifyQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 闲置土地核查记录 Service接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface IDataIdleVerifyService extends IServicePlus<DataIdleVerify> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataIdleVerify queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataIdleVerify> queryPageList(DataIdleVerifyQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataIdleVerify> queryList(DataIdleVerifyQueryBo bo);

	/**
	 * 根据新增业务对象插入闲置土地核查记录
	 *
	 * @param bo 闲置土地核查记录 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataIdleVerify bo);

	/**
	 * 根据编辑业务对象修改闲置土地核查记录
	 *
	 * @param bo 闲置土地核查记录 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataIdleVerify bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
