<template>
    <div class="app-container">
        <div v-show="allotstatus">
            <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
                #foreach($column in $columns)
                    #if($column.query)
                        #set($dictType=$column.dictType)
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        #if($column.htmlType == "input")
                            <el-form-item label="${comment}" prop="${column.javaField}">
                                <el-input
                                    v-model="queryParams.${column.javaField}"
                                    placeholder="请输入${comment}"
                                    clearable
                                    size="small"
                                    @keyup.enter.native="handleQuery"
                                />
                            </el-form-item>
                        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
                            <el-form-item label="${comment}" prop="${column.javaField}">
                                <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
                                    <el-option
                                        v-for="dict in ${column.javaField}Options"
                                        :key="dict.dictValue"
                                        :label="dict.dictLabel"
                                        :value="dict.dictValue"
                                    />
                                </el-select>
                            </el-form-item>
                        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
                            <el-form-item label="${comment}" prop="${column.javaField}">
                                <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
                                    <el-option label="请选择字典生成" value="" />
                                </el-select>
                            </el-form-item>
                        #elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
                            <el-form-item label="${comment}" prop="${column.javaField}">
                                <el-date-picker clearable size="small"
                                                v-model="queryParams.${column.javaField}"
                                                type="date"
                                                value-format="yyyy-MM-dd"
                                                placeholder="选择${comment}">
                                </el-date-picker>
                            </el-form-item>
                        #elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                            <el-form-item label="${comment}">
                                <el-date-picker
                                    v-model="daterange${AttrName}"
                                    size="small"
                                    style="width: 240px"
                                    value-format="yyyy-MM-dd"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                ></el-date-picker>
                            </el-form-item>
                        #end
                    #end
                #end
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        type="primary"
                        plain
                        icon="el-icon-plus"
                        size="mini"
                        @click="handleAdd"
                        v-hasPermi="['${moduleName}:${businessName}:add']"
                    >新增</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        type="success"
                        plain
                        icon="el-icon-edit"
                        size="mini"
                        :disabled="single"
                        @click="handleUpdate"
                        v-hasPermi="['${moduleName}:${businessName}:edit']"
                    >修改</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        type="danger"
                        plain
                        icon="el-icon-delete"
                        size="mini"
                        :disabled="multiple"
                        @click="handleDelete"
                        v-hasPermi="['${moduleName}:${businessName}:remove']"
                    >删除</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        type="warning"
                        plain
                        icon="el-icon-download"
                        size="mini"
                        :loading="exportLoading"
                        @click="handleExport"
                        v-hasPermi="['${moduleName}:${businessName}:export']"
                    >导出</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="${businessName}List" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                #foreach($column in $columns)
                    #set($javaField=$column.javaField)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if($column.pk)
                        <el-table-column label="${comment}" align="center" prop="${javaField}" v-if="${column.list}"/>
                    #elseif($column.list && $column.htmlType == "datetime")
                        <el-table-column label="${comment}" align="center" prop="${javaField}" width="180">
                            <template slot-scope="scope">
                                <span>{{ parseTime(scope.row.${javaField}, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                    #elseif($column.list && "" != $column.dictType)
                        <el-table-column label="${comment}" align="center" prop="${javaField}" :formatter="${javaField}Format" />
                    #elseif($column.list && "" != $javaField)
                        <el-table-column label="${comment}" align="center" prop="${javaField}" />
                    #end
                #end
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click="handleUpdate(scope.row)"
                            v-hasPermi="['${moduleName}:${businessName}:edit']"
                        >修改</el-button>
                        <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click="handleDelete(scope.row)"
                            v-hasPermi="['${moduleName}:${businessName}:remove']"
                        >删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total>0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </div>

        <div v-show="!allotstatus">

            <${BusinessName}Form
                :formData="form"
                @back="detailBack"
                :subTitle="title"
                :action="action"
                ref="model2"
            />
        </div>

    </div>


</template>

<script>
    import { list${BusinessName}, get${BusinessName}, del${BusinessName},  export${BusinessName} } from "@/api/${moduleName}/${businessName}";
    //详情组件
    import ${BusinessName}Form from './formModel.vue';


    export default {
        name: "${BusinessName}",
        components: {
                ${BusinessName}Form
        },
        data() {
            return {
                //详细页面 true 显示列表
                allotstatus: true,
                //按钮loading
                buttonLoading: false,
                // 遮罩层
                loading: true,
                // 导出遮罩层
                exportLoading: false,
                action: '',
                // 选中数组
                ids: [],
                #if($table.sub)
                    // 子表选中数据
                    checked${subClassName}: [],
                #end
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // ${functionName}表格数据
                    ${businessName}List: [],
                #if($table.sub)
                    // ${subTable.functionName}表格数据
                        ${subclassName}List: [],
                #end
                // 弹出层标题
                title: "",
                // 是否显示弹出层
                open: false,
                #foreach ($column in $columns)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if(${column.dictType} != '')
                        // $comment字典
                            ${column.javaField}Options: [],
                    #elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        // $comment时间范围
                        daterange${AttrName}: [],
                    #end
                #end
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
            #foreach ($column in $columns)
                #if($column.query)
                    $column.javaField: undefined#if($velocityCount != $columns.size()),#end

                #end
            #end
        },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                #foreach ($column in $columns)
                    #if($column.required)
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        $column.javaField: [
                        { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }
                    ]#if($velocityCount != $columns.size()),#end

                    #end
                #end
            }
        };
        },
        created() {
            this.getList();
            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    this.getDicts("${column.dictType}").then(response => {
                        this.${column.javaField}Options = response.data;
                    });
                #end
            #end
        },
        methods: {
            /** 查询${functionName}列表 */
            getList() {
                this.loading = true;
                #foreach ($column in $columns)
                    #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                        this.queryParams.params = {};
                        #break
                    #end
                #end
                #foreach ($column in $columns)
                    #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        if (null != this.daterange${AttrName} && '' != this.daterange${AttrName}) {
                            this.queryParams.params["begin${AttrName}"] = this.daterange${AttrName}[0];
                            this.queryParams.params["end${AttrName}"] = this.daterange${AttrName}[1];
                        }
                    #end
                #end
                list${BusinessName}(this.queryParams).then(response => {
                    this.${businessName}List = response.rows;
                    this.total = response.total;
                    this.loading = false;
                });
            },
            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    // $comment字典翻译
                        ${column.javaField}Format(row, column) {
                        return this.selectDictLabel#if($column.htmlType == "checkbox")s#end(this.${column.javaField}Options, row.${column.javaField});
                    },
                #end
            #end
            //  从窗体返回时
            detailBack() {
                this.allotstatus = !this.allotstatus;
                //刷新列表
                if(this.action==='new'){
                    this.queryParams.pageNum = 1;
                    this.getList();
                }else{
                    this.getList();
                }
            },

            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                #foreach ($column in $columns)
                    #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        this.daterange${AttrName} = [];
                    #end
                #end
                this.resetForm("queryForm");
                this.handleQuery();
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.${pkColumn.javaField})
                this.single = selection.length!==1
                this.multiple = !selection.length
            },

            /** 删除按钮操作 */
            handleDelete(row) {
                const ${pkColumn.javaField}s = row.${pkColumn.javaField} || this.ids;
                this.$confirm('是否确认删除${functionName}编号为"' + ${pkColumn.javaField}s + '"的数据项?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.loading = true;
                    return del${BusinessName}(${pkColumn.javaField}s);
                }).then(() => {
                    this.loading = false;
                    this.getList();
                    this.msgSuccess("删除成功");
                }).catch(() => {});
            },
            #if($table.sub)
                /** ${subTable.functionName}序号 */
                row${subClassName}Index({ row, rowIndex }) {
                    row.index = rowIndex + 1;
                },
                /** ${subTable.functionName}添加按钮操作 */
                handleAdd${subClassName}() {
                    let obj = {};
                    #foreach($column in $subTable.columns)
                        #if($column.pk || $column.javaField == ${subTableFkclassName})
                        #elseif($column.list && "" != $javaField)
                            obj.$column.javaField = "";
                        #end
                    #end
                    this.${subclassName}List.push(obj);
                },
                /** ${subTable.functionName}删除按钮操作 */
                handleDelete${subClassName}() {
                    if (this.checked${subClassName}.length == 0) {
                        this.$alert("请先选择要删除的${subTable.functionName}数据", "提示", { confirmButtonText: "确定", });
                    } else {
                        this.${subclassName}List.splice(this.checked${subClassName}[0].index - 1, 1);
                    }
                },
                /** 单选框选中数据 */
                handle${subClassName}SelectionChange(selection) {
                    if (selection.length > 1) {
                        this.$refs.${subclassName}.clearSelection();
                        this.$refs.${subclassName}.toggleRowSelection(selection.pop());
                    } else {
                        this.checked${subClassName} = selection;
                    }
                },
            #end
            /** 导出按钮操作 */
            handleExport() {
                const queryParams = this.queryParams;
                this.$confirm('是否确认导出所有${functionName}数据项?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.exportLoading = true;
                    return export${BusinessName}(queryParams);
                }).then(response => {
                    this.download(response.msg);
                    this.exportLoading = false;
                }).catch(() => {});
            },
            handleUpdate(row) {
                this.loading = true;
                this.$refs.model2.reset();
                const ${pkColumn.javaField} = row.${pkColumn.javaField} || this.ids
                get${BusinessName}(${pkColumn.javaField}).then(response => {
                    this.loading = false;
                    this.form = response.data;
                    #foreach ($column in $columns)
                        #if($column.htmlType == "checkbox")
                            this.form.$column.javaField = this.form.${column.javaField}.split(",");
                        #end
                    #end
                    #if($table.sub)
                        this.${subclassName}List = response.data.${subclassName}List;
                    #end
                    this.open = true;
                    this.title = "修改${functionName}";
                    this.allotstatus = false;
                });
            },
            handleAdd() {

                this.title = "修改${functionName}";
                this.form = {};
                this.allotstatus = false;
                this.$refs.model2.reset();
            }
        }
    };
</script>
