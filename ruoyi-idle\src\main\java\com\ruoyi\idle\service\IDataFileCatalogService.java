package com.ruoyi.idle.service;

import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataFileCatalog;
import com.ruoyi.idle.domain.bo.DataFileCatalogQueryBo;

import java.util.Collection;
import java.util.List;

/**
 * 巡查附件目录 Service接口
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
public interface IDataFileCatalogService extends IServicePlus<DataFileCatalog> {
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataFileCatalog queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DataFileCatalog> queryPageList(DataFileCatalogQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataFileCatalog> queryList(DataFileCatalogQueryBo bo);

	/**
	 * 根据新增业务对象插入巡查附件目录
	 *
	 * @param bo 巡查附件目录 新增业务对象
	 * @return
	 */
	Boolean insert(DataFileCatalog bo);

	/**
	 * 根据编辑业务对象修改巡查附件目录
	 *
	 * @param bo 巡查附件目录 编辑业务对象
	 * @return
	 */
	Boolean update(DataFileCatalog bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
