package com.ruoyi.idle.domain.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Created with IntelliJ IDEA.
 * @Title: IdleAllotTaskVo
 * @Description: com.ruoyi.idle.domain.vo
 * @Author: HongDeng
 * @Date: 2021/7/7 11:10
 * @Version: 1.0.0
 **/
@Data
public class IdleAllotTaskVo {
	/**
	 * 闲置项目编号
	 */
	@ApiModelProperty(value = "闲置项目编号")
	private String idleLandId;

	/**
	 * 巡查类型（0自行举证 1连线核查 2内业核查）
	 */
	@ApiModelProperty(value = "巡查类型")
	private String patrolType;

	/**
	 * 在线核查开始时间
	 */
	@ApiModelProperty(value = "在线核查开始时间")
	@Excel(name = "核查时间")
	private String meetingTime;

	/**
	 * 在线核查预计结束时间
	 */
	@ApiModelProperty(value = "在线核查预计结束时间")
	private String meetingEndTime;

	/** 巡查组 */
//	@ApiModelProperty(value = "巡查组")
//	@Excel(name = "巡查组")
//	private String patrolGroup;

	/**
	 * 内页核实人员
	 */
	@ApiModelProperty(value = "内页核实人员")
	@Excel(name = "内页核实人员")
	private List<IdleAllotTaskUserVo> userList;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
