package com.ruoyi.web.controller.idle;

import java.util.List;
import java.util.Arrays;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.idle.domain.DataVerifyRecord;
import com.ruoyi.idle.domain.bo.DataVerifyRecordQueryBo;
import com.ruoyi.idle.service.IDataVerifyRecordService;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 核查记录 Controller
 *
 * <AUTHOR>
 * @date 2021-11-17
 */
@Api(value = "核查记录 控制器", tags = {"核查记录 管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/idle/verify/record")
public class DataVerifyRecordController extends BaseController {

	private final IDataVerifyRecordService iDataVerifyRecordService;

	/**
	 * 查询核查记录 列表
	 */
	@ApiOperation("查询核查记录列表")
	@GetMapping("/list")
	public TableDataInfo<DataVerifyRecord> list(@Validated DataVerifyRecordQueryBo bo) {
		return iDataVerifyRecordService.queryPageList(bo);
	}

	/**
	 * 导出核查记录 列表
	 */
	@ApiOperation("导出核查记录列表")
	@Log(title = "核查记录 ", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult<DataVerifyRecord> export(@Validated DataVerifyRecordQueryBo bo) {
		List<DataVerifyRecord> list = iDataVerifyRecordService.queryList(bo);
		ExcelUtil<DataVerifyRecord> util = new ExcelUtil<DataVerifyRecord>(DataVerifyRecord.class);
		return util.exportExcel(list, "核查记录 ");
	}

	/**
	 * 获取核查记录 详细信息
	 */
	@ApiOperation("获取核查记录详细信息")
	@GetMapping("/{id}")
	public AjaxResult<DataVerifyRecord> getInfo(@NotNull(message = "主键不能为空")
												  @PathVariable("id") String id) {
		return AjaxResult.success(iDataVerifyRecordService.queryById(id));
	}

	/**
	 * 新增核查记录
	 */
	@ApiOperation("新增核查记录")
	@Log(title = "核查记录 ", businessType = BusinessType.INSERT)
	@RepeatSubmit
	@PostMapping()
	public AjaxResult<Void> add(@Validated @RequestBody DataVerifyRecord bo) {
		return toAjax(iDataVerifyRecordService.insertByAddBo(bo) ? 1 : 0);
	}

	/**
	 * 修改核查记录
	 */
	@ApiOperation("修改核查记录 ")
	@Log(title = "核查记录", businessType = BusinessType.UPDATE)
	@RepeatSubmit
	@PutMapping()
	public AjaxResult<Void> edit(@Validated @RequestBody DataVerifyRecord bo) {
		return toAjax(iDataVerifyRecordService.updateByEditBo(bo) ? 1 : 0);
	}

	/**
	 * 删除核查记录
	 */
	@ApiOperation("删除核查记录 ")
	@Log(title = "核查记录 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult<Void> remove(@NotEmpty(message = "主键不能为空")
								   @PathVariable String[] ids) {
		return toAjax(iDataVerifyRecordService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
	}
}
