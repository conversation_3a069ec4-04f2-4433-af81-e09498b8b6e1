package com.ruoyi.idle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataExportRecord;
import com.ruoyi.idle.domain.bo.DataExportRecordQueryBo;
import com.ruoyi.idle.mapper.DataExportRecordMapper;
import com.ruoyi.idle.service.IDataExportRecordService;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 成果导出记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
@Service
public class DataExportRecordServiceImpl extends ServicePlusImpl<DataExportRecordMapper, DataExportRecord> implements IDataExportRecordService {

	@Override
	public DataExportRecord queryById(Long id) {
		return getVoById(id, DataExportRecord.class);
	}

	@Override
	public TableDataInfo<DataExportRecord> queryPageList(DataExportRecordQueryBo bo) {
		PagePlus<DataExportRecord, DataExportRecord> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), DataExportRecord.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<DataExportRecord> queryList(DataExportRecordQueryBo bo) {
		return listVo(buildQueryWrapper(bo), DataExportRecord.class);
	}

	private LambdaQueryWrapper<DataExportRecord> buildQueryWrapper(DataExportRecordQueryBo bo) {
		LambdaQueryWrapper<DataExportRecord> lqw = Wrappers.lambdaQuery();
		lqw.eq(bo.getStartTime() != null, DataExportRecord::getStartTime, bo.getStartTime());
		lqw.eq(bo.getEndTime() != null, DataExportRecord::getEndTime, bo.getEndTime());
		lqw.eq(StrUtil.isNotBlank(bo.getTakeUpTime()), DataExportRecord::getTakeUpTime, bo.getTakeUpTime());
		lqw.like(StrUtil.isNotBlank(bo.getZipName()), DataExportRecord::getZipName, bo.getZipName());
		lqw.eq(bo.getZipSize() != null, DataExportRecord::getZipSize, bo.getZipSize());
		lqw.eq(StrUtil.isNotBlank(bo.getZipUrl()), DataExportRecord::getZipUrl, bo.getZipUrl());
		lqw.eq(StrUtil.isNotBlank(bo.getZipType()), DataExportRecord::getZipType, bo.getZipType());
		lqw.eq(StrUtil.isNotBlank(bo.getDeleteFlag()), DataExportRecord::getDeleteFlag, bo.getDeleteFlag());
		lqw.eq(StrUtil.isNotBlank(bo.getStatus()), DataExportRecord::getStatus, bo.getStatus());
		lqw.orderByDesc(DataExportRecord :: getCreateTime);
		return lqw;
	}

	@Override
	public Boolean insertByAddBo(DataExportRecord bo) {
		validEntityBeforeSave(bo);
		return save(bo);
	}

	@Override
	public Boolean updateByEditBo(DataExportRecord bo) {
		validEntityBeforeSave(bo);
		return updateById(bo);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(DataExportRecord entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}
}
