<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataPatrolTaskMapper">

    <resultMap type="com.ruoyi.idle.domain.DataPatrolTask" id="DataPatrolTaskResult">
        <result property="id" column="id"/>
        <result property="idleLandId" column="idle_land_id"/>
        <result property="quarter" column="quarter"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionName" column="region_name"/>
        <result property="patrolType" column="patrol_type"/>
        <result property="meetingTime" column="meeting_time"/>
        <result property="meetingEndTime" column="meeting_end_time"/>
        <result property="patrolGroup" column="patrol_group"/>
        <result property="patrolGroupName" column="patrol_group_name"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="isPatrol" column="is_patrol"/>
        <result property="patrolResult" column="patrol_result"/>
        <result property="patrolOpinion" column="patrol_opinion"/>
        <result property="submitRemark" column="submit_remark"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="year" column="year"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.ruoyi.idle.domain.DataClgtLand" id="DataClgtLandResult">
        <result property="id" column="dataClgtLandId"/>
        <result property="city" column="city"/>
        <result property="countyCode" column="county_code"/>
        <result property="countyName" column="county_name"/>
        <result property="contractNo" column="contract_no"/>
        <result property="supplyType" column="supply_type"/>
        <result property="projectName" column="project_name"/>
        <result property="year" column="year"/>
        <result property="quarter" column="quarter"/>
        <result property="supervisionNo" column="supervision_no"/>
        <result property="landUse" column="land_use"/>
        <result property="landArea" column="land_area"/>
        <result property="signDate" column="sign_date"/>
        <result property="agreedStartTime" column="agreed_start_time"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="idleStatus" column="idle_status"/>
        <result property="spotNumber" column="spot_number"/>
        <result property="insideRemark" column="inside_remark"/>
        <result property="digestionType" column="digestion_type"/>
        <result property="address" column="address"/>
        <result property="geoData" column="geo_data"/>
        <result property="parcelNum" column="parcel_num"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="isAllot" column="is_allot"/>
        <result property="warrantNum" column="warrant_num"/>
        <result property="remark" column="remark"/>
        <result property="taskNo" column="task_no"/>
        <result property="deleteFlag" column="clgtDeleteFlag"/>
        <result property="createBy" column="clgtCreateBy"/>
        <result property="createTime" column="clgtCreateTime"/>
        <result property="updateBy" column="clgtUpdateBy"/>
        <result property="updateTime" column="clgtUpdateTime"/>
    </resultMap>


    <resultMap type="com.ruoyi.idle.domain.DataHouseCheck" id="DataHouseCheckResult">
        <result property="id" column="dataHouseCheckId"/>
        <result property="jzbh" column="jzbh"/>
        <result property="jzybh" column="jzybh"/>
        <result property="jzbm" column="jzbm"/>
        <result property="mc" column="mc"/>
        <result property="jdmj" column="jdmj"/>
        <result property="dscs" column="dscs"/>
        <result property="dsjzmj" column="dsjzmj"/>
        <result property="jzgd" column="jzgd"/>
        <result property="yxjz" column="yxjz"/>
        <result property="fwts" column="fwts"/>
        <result property="xxdz" column="xxdz"/>
        <result property="jznd" column="jznd"/>
        <result property="dcsj" column="dcsj"/>
        <result property="jzzt" column="jzzt"/>
        <result property="jglx" column="jglx"/>
        <result property="sjyt" column="sjyt"/>
        <result property="ghyt" column="ghyt"/>
        <result property="jgyt" column="jgyt"/>
        <result property="jzfcyt" column="jzfcyt"/>
        <result property="fytlcd" column="fytlcd"/>
        <result property="fytjzmj" column="fytjzmj"/>
        <result property="dxcs" column="dxcs"/>
        <result property="dxjzmj" column="dxjzmj"/>
        <result property="fwxz" column="fwxz"/>
        <result property="xzsj" column="xzsj"/>
        <result property="ydsyq" column="ydsyq"/>
        <result property="txsm" column="txsm"/>
        <result property="deleteFlag" column="houseDeleteFlag"/>
        <result property="createBy" column="houseCreateBy"/>
        <result property="createTime" column="houseCreateTime"/>
        <result property="updateBy" column="houseUpdateBy"/>
        <result property="updateTime" column="houseUpdateTime"/>
        <result property="taskId" column="task_id"/>
        <result property="clgtId" column="clgt_id"/>
        <result property="mjly" column="mjly"/>
        <result property="sjly" column="sjly"/>
<!--        <result property="xzfw" column="xzfw"/>-->
        <result property="lybz" column="lybz"/>
        <result property="bz" column="bz"/>
    </resultMap>

    <resultMap type="com.ruoyi.idle.domain.vo.DataPatrolTaskVo" id="DataPatrolTaskVo">
        <result property="id" column="id"/>
        <result property="idleLandId" column="idle_land_id"/>
        <result property="quarter" column="quarter"/>
        <result property="projectName" column="project_name"/>
        <result property="projectType" column="project_type"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionName" column="region_name"/>
        <result property="patrolType" column="patrol_type"/>
        <result property="meetingTime" column="meeting_time"/>
        <result property="meetingEndTime" column="meeting_end_time"/>
        <result property="patrolGroup" column="patrol_group"/>
        <result property="patrolGroupName" column="patrol_group_name"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="isPatrol" column="is_patrol"/>
        <result property="patrolResult" column="patrol_result"/>
        <result property="patrolOpinion" column="patrol_opinion"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="status" column="status"/>
        <association property="dataHouseCheck" resultMap="DataHouseCheckResult"></association>
        <association property="dataClgtLand" resultMap="DataClgtLandResult"></association>
    </resultMap>
    <select id="selectAllPatrolTaskList" parameterType="java.lang.String"
            resultType="com.ruoyi.idle.domain.DataPatrolTask">
        SELECT *
        FROM `data_patrol_task`
        where (create_time >= #{startTime} or update_time >= #{startTime})
          and project_type = '0'
          and delete_flag in ('0', '1')
    </select>

    <sql id="VoSql">
        task.id,
        task.idle_land_id,
        task.quarter,
        task.project_name,
        task.project_type,
        task.region_code,
        task.region_name,
        task.patrol_type,
        task.meeting_time,
        task.meeting_end_time,
        task.patrol_group,
        task.patrol_group_name,
        task.publish_status,
        task.is_patrol,
        task.patrol_result,
        task.patrol_opinion,
        task.submit_remark,
        task.remark,
        task.delete_flag,
        task.status,
        task.create_by,
        task.create_time,
        task.update_by,
        task.update_time,
        land.contract_no,
        land.digestion_type,
        land.supervision_no,
        land.year,
        v.handle_is_correct
    </sql>

    <sql id="condition">

        <if test="bo.regionCode!=null and bo.regionCode!=''">
            and region_code = #{bo.regionCode}
        </if>

        <if test="bo.publishStatus!=null and bo.publishStatus!=''">
            and publish_status = #{bo.publishStatus}
        </if>

        <if test="bo.patrolType!=null and bo.patrolType!=''">
            and patrol_type = #{bo.patrolType}
        </if>

        <if test="bo.projectName!=null and bo.projectName!=''">
            and project_name = #{bo.projectName}
        </if>

        <if test="bo.contractNo!=null and bo.contractNo!=''">
            and contract_no = #{bo.contractNo}
        </if>

        <if test="bo.supervisionNo!=null and bo.supervisionNo!=''">
            and supervision_no = #{bo.supervisionNo}
        </if>

        <if test="bo.year!=null and bo.year!=''">
            and year = #{bo.year}
        </if>

        <if test="bo.isPatrol!=null and bo.isPatrol!=''">
            and is_patrol = #{bo.isPatrol}
        </if>

        <if test="bo.orderByColumn!=null and bo.orderByColumn!=''">
            order by #{orderByColumn}
        </if>
        <if test="bo.isAsc!=null and bo.isAsc!=''">
            #{isAsc}
        </if>
    </sql>
    <select id="selectTaskList" resultType="com.ruoyi.idle.domain.DataPatrolTask">
        select
        <include refid="VoSql"/>
        from data_patrol_task task left join data_clgt_land land on task.idle_land_id = land.id
        left join data_idle_verify v on task.id = v.task_id
        <where>
            task.delete_flag = '0'
            <include refid="condition"></include>
        </where>
    </select>

    <select id="selectToDoList" resultType="com.ruoyi.idle.domain.DataPatrolTask">
        select
        <include refid="VoSql"/>
        from data_patrol_task task inner join data_task_user tu on task.id = tu.task_id
        left join data_clgt_land land on task.idle_land_id = land.id
        left join data_idle_verify v on task.id = v.task_id
        <where>
            <if test="userId!=null and userId!=''">
                and tu.user_id = #{userId}
            </if>
            <if test="type!=null and type!=''">
                and task.is_patrol = #{type}
            </if>
        </where>
    </select>

    <sql id="VoSql1">
        task
        .
        id
        ,
        task.idle_land_id,
        task.quarter,
        task.project_name,
        task.project_type,
        task.region_code,
        task.region_name,
        task.patrol_type,
        task.meeting_time,
        task.meeting_end_time,
        task.patrol_group,
        task.patrol_group_name,
        task.publish_status,
        task.is_patrol,
        task.patrol_result,
        task.patrol_opinion,
        task.submit_remark,
        task.remark,
        task.delete_flag,
        task.status,
        task.create_by,
        task.create_time,
        task.update_by,
        task.update_time,
        land.contract_no,
        land.supervision_no,
        land.year,
        '批而未供' digestion_type,
        v.handle_is_correct
    </sql>
    <select id="selectNotProTaskList" resultType="com.ruoyi.idle.domain.DataPatrolTask">
        select
        <include refid="VoSql1"/>
        from data_patrol_task task left join data_land_not_provided land on task.project_name = land.project_name
        left join data_idle_verify v on task.id = v.task_id
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <select id="countByPatrol" resultType="map">
        SELECT r.name regionName,r.area_code regionCode,IfNULL(t.zs,0) totalCount,IfNULL(t.wsc,0) unPatrolledCount,IfNULL(t.ytj,0) submittedcCount,IfNULL(t.wtj,0) unSubmittedcCount
        FROM (SELECT name, area_code
              from sys_region
              WHERE level = '4'
                and delete_flag = '0'
                and parent_id = #{pId}
             ) r
                 left join (
            SELECT region_code,
                   COUNT(*)                                                 as zs,
                   sum(CASE WHEN is_patrol = '0' THEN 1 ELSE 0 END)        as wsc,
                   sum(CASE WHEN is_patrol = '1' THEN 1 ELSE 0 END)        as ytj,
                   sum(CASE WHEN is_patrol in ('2', '3') THEN 1 ELSE 0 END) as wtj
            FROM data_patrol_task
            WHERE delete_flag = '0'
            GROUP BY region_code
        ) t on t.region_code = r.area_code
    </select>

    <select id="appCheckList" resultMap="DataPatrolTaskVo">
            select *,l.id as dataClgtLandId,
                   c.id as dataHouseCheckId,
                   l.delete_flag as clgtDeleteFlag,
                   l.create_by as clgtCreateBy,
                   l.create_time as clgtCreateTime,
                   l.update_by as clgtUpdateBy,
                   l.update_time as clgtUpdateTime,
                   c.delete_flag as houseDeleteFlag,
                   c.create_by as houseCreateBy,
                   c.create_time as houseCreateTime,
                   c.update_by as houseUpdateBy,
                   c.update_time as houseUpdateTime
                   from  data_patrol_task t left join data_clgt_land l on t.idle_land_id = l.id
                                              left  join  data_house_check c on t.id = c.task_id
            where  t.publish_status = '1' and t.patrol_type in ('0','1') and t.is_patrol in ('0','2','3')
            and t.region_code = #{regionCode}
    </select>

    <select id="submitTaskList" resultMap="DataPatrolTaskVo">
        select *,l.id as dataClgtLandId,
               c.id as dataHouseCheckId,
               l.delete_flag as clgtDeleteFlag,
               l.create_by as clgtCreateBy,
               l.create_time as clgtCreateTime,
               l.update_by as clgtUpdateBy,
               l.update_time as clgtUpdateTime,
               c.delete_flag as houseDeleteFlag,
                c.create_by as houseCreateBy,
               c.create_time as houseCreateTime,
               c.update_by as houseUpdateBy,
               c.update_time as houseUpdateTime
        from  data_patrol_task t left join data_clgt_land l on t.idle_land_id = l.id
                                          left  join  data_house_check c on t.id = c.task_id
        where
        <if test="bo.isPatrol!=null and bo.isPatrol!=''">
             t.is_patrol = #{bo.isPatrol}
        </if>

        <if test="bo.jzbh!=null and bo.jzbh!=''">
           and c.jzbh = #{bo.jzbh}
        </if>

        <if test="bo.jzybh!=null and bo.jzybh!=''">
            and c.jzybh = #{bo.jzybh}
        </if>
<!--        <if test="bo.regionCode!=null and bo.regionCode!=''">-->
<!--            and t.region_code = #{bo.regionCode}-->
<!--        </if>-->
        <if test="bo.regionCodes!=null">
            and t.region_code in
            <foreach collection="bo.regionCodes" item="regionCode" index="index" separator="," open="(" close=")">
                #{regionCode}
            </foreach>
        </if>
    </select>


    <select id="submitTaskListImages" resultType="map">
        select c.jzybh,c.xxdz,t.id,a.location,a.name,a.type,t.region_name regionName
        from  data_patrol_task t inner join data_house_check c on t.id = c.task_id
            inner join data_attachment a on t.id = a.patrol_task_id
        <where>
        <if test="bo.isPatrol!=null and bo.isPatrol!=''">
            t.is_patrol = #{bo.isPatrol}
        </if>

        <if test="bo.jzbh!=null and bo.jzbh!=''">
            and c.jzbh = #{bo.jzbh}
        </if>

        <if test="bo.jzybh!=null and bo.jzybh!=''">
            and c.jzybh = #{bo.jzybh}
        </if>
<!--                <if test="bo.regionCode!=null and bo.regionCode!=''">-->
<!--                    and t.region_code = #{bo.regionCode}-->
<!--                </if>-->
        <if test="bo.regionCodes!=null">
            and t.region_code in
            <foreach collection="bo.regionCodes" item="regionCode" index="index" separator="," open="(" close=")">
                #{regionCode}
            </foreach>
        </if>
        </where>
    </select>
</mapper>
