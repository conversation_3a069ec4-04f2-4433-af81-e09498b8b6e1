package com.ruoyi.idle.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Created with IntelliJ IDEA.
 * @Title: DataSynchroRecord
 * @Description: com.ruoyi.idle.domain
 * @Author: HongDeng
 * @Date: 2021/9/13 9:20
 * @Version: 1.0.0
 **/
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("data_synchro_record")
public class DataSynchroRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	@ApiModelProperty(name = "id", value = "主键编号", hidden = true)
	private String id;

	/**
	 * 同步数据文件名称
	 */
	@ApiModelProperty(value = "同步数据文件名称")
	private String fileName;
	/**
	 * 同步类型
	 */
	@ApiModelProperty(value = "同步类型")
	private String synchroType;
	/**
	 * 同步结果状态
	 */
	@ApiModelProperty(value = "同步结果状态")
	private String status;

	/**
	 * 同步失败原因
	 */
	@ApiModelProperty(value = "同步失败原因")
	private String errorMsg;
	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志", hidden = true)
	@TableLogic(value = "0", delval = "1")
	private String deleteFlag;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人", hidden = true)
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人", hidden = true)
	private String updateBy;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间", hidden = true)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
