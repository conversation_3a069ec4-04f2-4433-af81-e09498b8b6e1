package com.ruoyi.meet.controller;

import com.ruoyi.socket.StrConvert;
import lombok.Data;

@Data
public class RealTimeXy {

	private String name;

	private Double x;

	private Double y;

	private Float angle;

	public RealTimeXy(Double x, Double y) {
		this.x = x;
		this.y = y;
	}

	public RealTimeXy(String x, String y,String name,String angle) {
		this.x = StrConvert.toDouble(x) ;
		this.y = StrConvert.toDouble(y) ;
		this.angle = StrConvert.toFloat(angle) ;
		this.name = name ;
	}
}
