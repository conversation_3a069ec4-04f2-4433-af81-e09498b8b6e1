package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 角色和部门关联 sys_role_dept
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_role_dept")
public class SysRoleDept {

	/**
	 * 主键编号
	 */
	@TableId(value = "id", type = IdType.ASSIGN_UUID)
	private String id;
	/**
	 * 角色ID
	 */
	private String roleId;

	/**
	 * 部门ID
	 */
	private String deptId;

}
