package com.ruoyi.idle.service;


import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.DataClgtLand;
import com.ruoyi.idle.domain.DataIdleLand;
import com.ruoyi.idle.domain.bo.DataClgtLandQueryBo;
import com.ruoyi.idle.domain.vo.DataClgtLandVo;
import com.ruoyi.idle.domain.vo.ImportClgtAndHouseVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 闲置土地 Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IDataClgtLandService extends IServicePlus<DataClgtLand> {

	String selectSupervisionNoByTaskId(String taskId);
	/**
	 * 查询单个
	 *
	 * @return
	 */
	DataClgtLand queryById(String id);
	List<DataClgtLand> appPulList(String regionCode, String userId);
	/**
	 * 查询列表
	 */
	TableDataInfo<DataClgtLandVo> queryPageList(DataClgtLandQueryBo bo);

	/**
	 * 查询列表
	 */
	List<DataClgtLandVo> queryList(DataClgtLandQueryBo bo);

	/**
	 * 根据新增业务对象插入闲置土地
	 *
	 * @param bo 闲置土地 新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(DataClgtLand bo);

	/**
	 * 根据编辑业务对象修改闲置土地
	 *
	 * @param bo 闲置土地 编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(DataClgtLand bo);

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 导入
	 * @param file
	 * @return
	 */
	Boolean importData(MultipartFile file) throws IOException;

	Boolean importDataByList(List<ImportClgtAndHouseVo> boList) throws IOException;

	/**
	 *任务编号
	 */
	String taskNo();
}
