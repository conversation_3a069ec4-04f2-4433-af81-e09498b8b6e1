package org.geo.shape;


import com.ruoyi.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.apache.tomcat.util.http.fileupload.util.Streams;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
public class FileUtil {
	//上传路径
	private static final String FILE_UPLOAD_PATH = "upload" + File.separator;
	// 文件下载路径
	private static final String FILE_DOWNLOAD_PATH = "download" + File.separator;
	// 日期路径
	private static final String DATE_PATH = DateUtil.getNowStr() + File.separator;
	// 根路径
	private static final String ROOT_PATH = "E:" + File.separator;
	// 下划线
	private static final String UNDER_LINE = "_";
	// 默认字符集
	private static final String DEFAULT_CHARSET = "utf-8";

	/**
	 * zip文件解压
	 *
	 * @param destPath 解压文件路径
	 * @param zipFileName  压缩文件
	 */
	public static void unPackZip(String zipFileName,  String destPath) {

		ZipFile zipFile = null;

		try {
			destPath=destPath.replace('\\','/');
			if(!destPath.endsWith("/")){
				destPath=destPath+"/";
			}
			zipFile = new ZipFile(new File(zipFileName));
			Enumeration entrys = zipFile.entries();

			while (entrys.hasMoreElements()) {
				ZipEntry entry = (ZipEntry) entrys.nextElement();
				if (entry.isDirectory()) {
					File tempFile = new File(destPath + entry.getName());

					if ( !tempFile.exists() ) {
						if ( !tempFile.mkdirs() ) {
							//throw new RuntimeException("创建目录结构失败:" + entry.getName());
						}
					}

				} else {

					BufferedInputStream input = new BufferedInputStream(

						zipFile.getInputStream(entry));

					BufferedOutputStream output = new BufferedOutputStream(new FileOutputStream(destPath + entry.getName()));
// 写出数据
					int len = -1;

					byte[] bytes = new byte[2048];

					while ((len = input.read(bytes)) != -1) {
						output.write(bytes, 0, len);
					}

					output.close();
					input.close();

				}

			}

			zipFile.close();
		} catch (Exception ex) {

		}
	}


	/**
	 * 每次上传文件前需要清空目录
	 */
	public static boolean deletesDir(String path) {
		File file = new File(path);
		if (!file.exists()) {
			return false;
		}
		String[] content = file.list();
		for (String name : content) {
			File temp = new File(path, name);
			if (temp.isDirectory()) {
				deletesDir(temp.getAbsolutePath());
			} else {
				temp.delete();
			}
		}
		return true;

	}

	public static ArrayList<String> getShapefileFromDir(String path) {
		ArrayList<String> list=new ArrayList<>();
		File file = new File(path);
		if (!file.exists()) {
			return list;
		}
		//ArrayList<String> list=new ArrayList<>();
		String[] content = file.list();
		for (String name : content) {
			File temp = new File(path, name);
			if(name.toLowerCase().endsWith(".shp")){
				list.add(temp.getAbsolutePath());
			}
		}
		return list;

	}

	// 上传文件   添加文件重名判断（重名删除）
	public static String uploadFile(MultipartFile file) throws IOException {
		// 获取上传的文件名称（包含后缀名）
		String oldFileName = file.getOriginalFilename();
		// 获取文件后缀名，将小数点“.” 进行转译
		String[] split = oldFileName.split("\\.");

		// 文件名
		String fileName = null;
		StringBuilder builder = new StringBuilder();
		if (split.length > 0) {
			String suffix = split[split.length - 1];
			for (int i = 0; i < split.length - 1; i++) {
				builder.append(split[i]).append(UNDER_LINE);
			}
			// 防止文件名重复
//            fileName = builder.append(System.nanoTime()).append(".").append(suffix).toString();
			fileName = oldFileName;
		} else {
			fileName = builder.append(oldFileName).append(UNDER_LINE).append(System.nanoTime()).toString();

		}

		// 上传文件的存储路径
		String filePath = ROOT_PATH + FILE_UPLOAD_PATH + DATE_PATH;
		// 生成文件夹
		mkdirs(filePath);
		//删除之前上传文件
		deletesDir(filePath);
		// 文件全路径
		String fileFullPath = filePath + fileName;
		log.info("上传的文件：" + file.getName() + "，" + file.getContentType() + "，保存的路径为：" + fileFullPath);

		// 转存文件
		Streams.copy(file.getInputStream(), new FileOutputStream(fileFullPath), true);

		return fileFullPath;
	}

	// 根据文件名下载文件
	public static AjaxResult downloadFile(String fileName, HttpServletResponse response) {
		InputStream in = null;
		OutputStream out = null;
		try {
			// 获取输出流
			out = response.getOutputStream();
			setResponse(fileName, response);

			String downloadPath = new StringBuilder().append(ROOT_PATH).append(FILE_DOWNLOAD_PATH).append(fileName).toString();
			File file = new File(downloadPath);
			if (!file.exists()) {
				log.error("下载附件失败，请检查文件" + downloadPath + "是否存在");
				return AjaxResult.error("下载附件失败，请检查文件" + downloadPath + "是否存在");
			}

			// 获取输入流
			in = new FileInputStream(file);
			if (null == in) {
				log.error("下载附件失败，请检查文件" + fileName + "是否存在");
				throw new FileNotFoundException("下载附件失败，请检查文件" + fileName + "是否存在");
			}
			// 复制
			IOUtils.copy(in, response.getOutputStream());
			response.getOutputStream().flush();

			try {
				close(in, out);
			} catch (IOException e) {
				log.error("关闭流失败");
				return AjaxResult.error("关闭流失败");
			}
		} catch (IOException e) {
			log.error("响应对象response获取输出流错误");
			return AjaxResult.error("响应对象response获取输出流错误");
		}
		return AjaxResult.success("文件下载成功");
	}

	// 设置响应头
	public static void setResponse(String fileName, HttpServletResponse response) {
		// 清空输出流
		response.reset();
		response.setContentType("application/x-download;charset=GBK");
		try {
			response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(DEFAULT_CHARSET), "iso-8859-1"));
		} catch (UnsupportedEncodingException e) {
			log.error("文件名{}不支持转换为字符集{}", fileName, DEFAULT_CHARSET);
		}
	}

	// 关闭流
	public static void close(InputStream in, OutputStream out) throws IOException {
		if (null != in) {
			in.close();
		}
		if (null != out) {
			out.close();
		}
	}

	// 根据目录路径生成文件夹
	public static void mkdirs(String path) {
		File file = new File(path);
		if (!file.exists() || !file.isDirectory()) {
			file.mkdirs();
		}
	}

}
