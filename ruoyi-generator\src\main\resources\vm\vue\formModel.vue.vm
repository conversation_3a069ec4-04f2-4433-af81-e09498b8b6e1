<template>
    <div class="">

        <!-- 添加或修改${functionName}详情页面 -->
        <el-page-header @back="handleBack" :content=" title ">
        </el-page-header>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                #foreach($column in $columns)
                    #set($field=$column.javaField)
                    #if($column.insert && !$column.pk)
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        #set($dictType=$column.dictType)
                        #if($column.htmlType == "input")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-input v-model="form.${field}" placeholder="请输入${comment}" />
                            </el-form-item>
                        #elseif($column.htmlType == "imageUpload")
                            <el-form-item label="${comment}">
                                <imageUpload v-model="form.${field}"/>
                            </el-form-item>
                        #elseif($column.htmlType == "fileUpload")
                            <el-form-item label="${comment}">
                                <fileUpload v-model="form.${field}"/>
                            </el-form-item>
                        #elseif($column.htmlType == "editor")
                            <el-form-item label="${comment}">
                                <editor v-model="form.${field}" :min-height="192"/>
                            </el-form-item>
                        #elseif($column.htmlType == "select" && "" != $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                    <el-option
                                        v-for="dict in ${field}Options"
                                        :key="dict.dictValue"
                                        :label="dict.dictLabel"
                                        #if($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.dictValue)"#else:value="dict.dictValue"#end

                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        #elseif($column.htmlType == "select" && $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                    <el-option label="请选择字典生成" value="" />
                                </el-select>
                            </el-form-item>
                        #elseif($column.htmlType == "checkbox" && "" != $dictType)
                            <el-form-item label="${comment}">
                                <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox
                                        v-for="dict in ${field}Options"
                                        :key="dict.dictValue"
                                        :label="dict.dictValue">
                                        {{dict.dictLabel}}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        #elseif($column.htmlType == "checkbox" && $dictType)
                            <el-form-item label="${comment}">
                                <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox>请选择字典生成</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        #elseif($column.htmlType == "radio" && "" != $dictType)
                            <el-form-item label="${comment}">
                                <el-radio-group v-model="form.${field}">
                                    <el-radio
                                        v-for="dict in ${field}Options"
                                        :key="dict.dictValue"
                                        #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.dictValue)"#else:label="dict.dictValue"#end

                                    >{{dict.dictLabel}}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        #elseif($column.htmlType == "radio" && $dictType)
                            <el-form-item label="${comment}">
                                <el-radio-group v-model="form.${field}">
                                    <el-radio label="1">请选择字典生成</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        #elseif($column.htmlType == "datetime")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-date-picker clearable size="small"
                                                v-model="form.${field}"
                                                type="datetime"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="选择${comment}">
                                </el-date-picker>
                            </el-form-item>
                        #elseif($column.htmlType == "textarea")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        #end
                    #end
                #end
                #if($table.sub)
                    <el-divider content-position="center">${subTable.functionName}信息</el-divider>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd${subClassName}">添加</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDelete${subClassName}">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" ref="${subclassName}">
                        <el-table-column type="selection" width="50" align="center" />
                        <el-table-column label="序号" align="center" prop="index" width="50"/>
                        #foreach($column in $subTable.columns)
                            #set($javaField=$column.javaField)
                            #set($parentheseIndex=$column.columnComment.indexOf("（"))
                            #if($parentheseIndex != -1)
                                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                            #else
                                #set($comment=$column.columnComment)
                            #end
                            #if($column.pk || $javaField == ${subTableFkclassName})
                            #elseif($column.list && "" != $javaField)
                                <el-table-column label="$comment" prop="${javaField}">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.$javaField" placeholder="请输入$comment" />
                                    </template>
                                </el-table-column>
                            #end
                        #end
                    </el-table>
                #end
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>

    </div>


</template>

<script>
    import {  get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName} } from "@/api/${moduleName}/${businessName}";
        #foreach($column in $columns)
            #if($column.insert && !$column.pk && $column.htmlType == "imageUpload")
            import ImageUpload from '@/components/ImageUpload';
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.insert && !$column.pk && $column.htmlType == "fileUpload")
            import FileUpload from '@/components/FileUpload';
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.insert && !$column.pk && $column.htmlType == "editor")
            import Editor from '@/components/Editor';
                #break
            #end
        #end

    export default {
        name: "${BusinessName}model2",
        props:{
            subTitle:{
                type:String,
                default:''
            },
            formData:{
                type:Object,
                default:{}
            }
        },
        components: {
            #foreach($column in $columns)
                #if($column.insert && !$column.pk && $column.htmlType == "imageUpload")
                    ImageUpload,
                    #break
                #end
            #end
            #foreach($column in $columns)
                #if($column.insert && !$column.pk && $column.htmlType == "fileUpload")
                    FileUpload,
                    #break
                #end
            #end
            #foreach($column in $columns)
                #if($column.insert && !$column.pk && $column.htmlType == "editor")
                    Editor,
                    #break
                #end
            #end
        },
        data() {
            return {

                // 遮罩层
                loading: true,
                buttonLoading: false,
                // 弹出层标题
                title: "${BusinessName}-详情",
                // 是否显示弹出层
                open: false,
                #foreach ($column in $columns)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if(${column.dictType} != '')
                        // $comment字典
                            ${column.javaField}Options: [],
                    #elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        // $comment时间范围
                        daterange${AttrName}: [],
                    #end
                #end

                // 表单参数
                form: {},
                // 表单校验
                rules: {
                    #foreach ($column in $columns)
                        #if($column.required)
                            #set($parentheseIndex=$column.columnComment.indexOf("（"))
                            #if($parentheseIndex != -1)
                                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                            #else
                                #set($comment=$column.columnComment)
                            #end
                                $column.javaField: [
                                { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }
                            ]#if($velocityCount != $columns.size()),#end

                        #end
                    #end
                }
            };
        },
        created() {

            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    this.getDicts("${column.dictType}").then(response => {
                        this.${column.javaField}Options = response.data;
                    });
                #end
            #end
        },
        watch: {
            // form 动态赋值
            formData: function(newVal,oldVal){
                this.form = newVal;  //newVal
                //
            },
            subTitle: function(newVal,oldVal){
                this.title = newVal;  //newVal
                //
            },
            action: function(newVal,oldVal){
                this.title = newVal;  //newVal
                //
            },
        },
        methods: {
            /** 返回${functionName}列表 */
            handleBack(){
                this.$emit('back')
            },
            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    // $comment字典翻译
                        ${column.javaField}Format(row, column) {
                        return this.selectDictLabel#if($column.htmlType == "checkbox")s#end(this.${column.javaField}Options, row.${column.javaField});
                    },
                #end
            #end
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    #foreach ($column in $columns)
                        #if($column.htmlType == "radio")
                            $column.javaField: #if($column.javaType == "Integer" || $column.javaType == "Long")0#else"0"#end#if($velocityCount != $columns.size()),#end

                        #elseif($column.htmlType == "checkbox")
                                $column.javaField: []#if($velocityCount != $columns.size()),#end

                        #else
                                $column.javaField: undefined#if($velocityCount != $columns.size()),#end

                        #end
                    #end
                };
                #if($table.sub)
                    this.${subclassName}List = [];
                #end
                this.resetForm("form");
            },

            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.${pkColumn.javaField})
                this.single = selection.length!==1
                this.multiple = !selection.length
            },

            /** 提交按钮 */
            submitForm() {
                this.#[[$]]#refs["form"].validate(valid => {
                    if (valid) {
                        this.buttonLoading = true;
                        #foreach ($column in $columns)
                            #if($column.htmlType == "checkbox")
                                this.form.$column.javaField = this.form.${column.javaField}.join(",");
                            #end
                        #end
                        #if($table.sub)
                            this.form.${subclassName}List = this.${subclassName}List;
                        #end
                        if (this.form.${pkColumn.javaField} != null) {
                            update${BusinessName}(this.form).then(response => {
                                this.buttonLoading = false;
                                this.msgSuccess("修改成功");
                                this.$emit('back')
                            });
                        } else {
                            add${BusinessName}(this.form).then(response => {
                                this.buttonLoading = false;
                                this.msgSuccess("新增成功");
                                this.$emit('back')
                            });
                        }
                    }
                });
            },

            #if($table.sub)
                /** ${subTable.functionName}序号 */
                row${subClassName}Index({ row, rowIndex }) {
                    row.index = rowIndex + 1;
                },
                /** ${subTable.functionName}添加按钮操作 */
                handleAdd${subClassName}() {
                    let obj = {};
                    #foreach($column in $subTable.columns)
                        #if($column.pk || $column.javaField == ${subTableFkclassName})
                        #elseif($column.list && "" != $javaField)
                            obj.$column.javaField = "";
                        #end
                    #end
                    this.${subclassName}List.push(obj);
                },
                /** ${subTable.functionName}删除按钮操作 */
                handleDelete${subClassName}() {
                    if (this.checked${subClassName}.length == 0) {
                        this.$alert("请先选择要删除的${subTable.functionName}数据", "提示", { confirmButtonText: "确定", });
                    } else {
                        this.${subclassName}List.splice(this.checked${subClassName}[0].index - 1, 1);
                    }
                },
                /** 单选框选中数据 */
                handle${subClassName}SelectionChange(selection) {
                    if (selection.length > 1) {
                        this.$refs.${subclassName}.clearSelection();
                        this.$refs.${subclassName}.toggleRowSelection(selection.pop());
                    } else {
                        this.checked${subClassName} = selection;
                    }
                },
            #end

        }
    };
</script>
