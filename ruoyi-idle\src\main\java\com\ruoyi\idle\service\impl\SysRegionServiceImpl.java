package com.ruoyi.idle.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.core.page.PagePlus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.idle.domain.SysRegion;
import com.ruoyi.idle.domain.bo.SysRegionQueryBo;
import com.ruoyi.idle.domain.vo.RegionTreeVo;
import com.ruoyi.idle.mapper.SysRegionMapper;
import com.ruoyi.idle.service.ISysRegionService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中国行政区Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-05
 */
@Service
public class SysRegionServiceImpl extends ServicePlusImpl<SysRegionMapper, SysRegion> implements ISysRegionService {

	@Override
	public SysRegion queryById(String id) {
		return getVoById(id, SysRegion.class);
	}

	@Override
	public TableDataInfo<SysRegion> queryPageList(SysRegionQueryBo bo) {
		PagePlus<SysRegion, SysRegion> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), SysRegion.class);
		return PageUtils.buildDataInfo(result);
	}

	@Override
	public List<SysRegion> queryList(SysRegionQueryBo bo) {
		return listVo(buildQueryWrapper(bo), SysRegion.class);
	}

	@Override
	public List<String> queryChildList(String regionCode) {
		List<String> list = new ArrayList<>();
		return codeList(regionCode,list);
	}

	private List<String> codeList(String areaCode,List<String> areaCodes){
		if (StringUtils.isNotBlank(areaCode)) {
			LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(SysRegion::getAreaCode, areaCode);
			SysRegion sysRegion = baseMapper.selectOne(queryWrapper);
			if(ObjectUtils.isNotNull(sysRegion)){
				if(sysRegion.getLevel().equals("4")){
					areaCodes.add(sysRegion.getAreaCode());
					return areaCodes;
//					areaCodes.add(sysRegion);
				}
				LambdaQueryWrapper<SysRegion> queryWrapper1 = new LambdaQueryWrapper<>();
				queryWrapper1.eq(SysRegion::getParentCode, areaCode);
				List<SysRegion> sysRegions = baseMapper.selectList(queryWrapper1);
				if(ObjectUtils.isNotNull(sysRegions)){
					for (SysRegion s : sysRegions) {
						if(s.getLevel().equals("4")){
							areaCodes.add(s.getAreaCode());
//							areaCodes.add(s);
							continue;
						}
						codeList(s.getAreaCode(),areaCodes);
					}
				}
			}
		}
		return areaCodes;
	}

	private LambdaQueryWrapper<SysRegion> buildQueryWrapper(SysRegionQueryBo bo) {
		LambdaQueryWrapper<SysRegion> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getLevel()), SysRegion::getLevel, bo.getLevel());
		lqw.eq(bo.getParentId() != null, SysRegion::getParentId, bo.getParentId());
		lqw.eq(StrUtil.isNotBlank(bo.getParentCode()), SysRegion::getParentCode, bo.getParentCode());
		lqw.like(StrUtil.isNotBlank(bo.getAreaCode()), SysRegion::getAreaCode, bo.getAreaCode());
		lqw.eq(StrUtil.isNotBlank(bo.getZipCode()), SysRegion::getZipCode, bo.getZipCode());
		lqw.eq(StrUtil.isNotBlank(bo.getCityCode()), SysRegion::getCityCode, bo.getCityCode());
		lqw.like(StrUtil.isNotBlank(bo.getName()), SysRegion::getName, bo.getName());
		lqw.like(StrUtil.isNotBlank(bo.getShortName()), SysRegion::getShortName, bo.getShortName());
		return lqw;
	}

	@Override
	public Boolean insert(SysRegion region) {
		validEntityBeforeSave(region);
		return save(region);
	}

	@Override
	public Boolean update(SysRegion region) {
		validEntityBeforeSave(region);
		return updateById(region);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(SysRegion entity) {
		//TODO 做一些数据校验,如唯一约束
		if (null == entity.getDeleteFlag()) entity.setDeleteFlag("0");
		// 获取当前登录用户设置创建人和更新人字段
	}

	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public String getNameByCode(String code) {
		LambdaQueryWrapper<SysRegion> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysRegion::getAreaCode, code);
		SysRegion region = this.baseMapper.selectOne(queryWrapper);
		String regionName = region == null ? "" : region.getName();
		return regionName;
	}

	@Override
	public List<RegionTreeVo> getRegionTreeAsync(String pId) {
		String xzqdm = "530100000000";
		List<RegionTreeVo> result = new ArrayList<>();
		LambdaQueryWrapper<SysRegion> lmq = new LambdaQueryWrapper<>();
		if (pId != null) {
			lmq.eq(SysRegion::getParentId,pId);
		} else {
			lmq.eq(SysRegion::getParentId,"0");
		}
		lmq.orderByAsc(SysRegion::getAreaCode);
		List<SysRegion> regionList = baseMapper.selectList(lmq);
		if (regionList.size() != 0) {
			for (SysRegion region : regionList) {
				String areaCode = region.getAreaCode();
				if (StringUtils.isNotBlank(areaCode)) {
					if(region.getLevel().equals("2")){
						region.setAreaCode(areaCode.substring(0,6));
					}
					if(region.getLevel().equals("3")){
						region.setAreaCode(areaCode.substring(0,9));
					}
				}
				RegionTreeVo temp = new RegionTreeVo();
				BeanUtils.copyProperties(region, temp);
				temp.setHasChildren(hasChildren(region.getId()));
				result.add(temp);
			}
		}
		List<RegionTreeVo> collect = new ArrayList<>();
//		根据pid构造树形结构
		if (pId != null) {
			collect = result;
		} else {
			collect = result.stream().filter(m -> m.getAreaCode().equals(xzqdm)).collect(Collectors.toList());
		}
		List<RegionTreeVo> result1 = collect.stream().map((m) ->{
			m.setChildren(getChildrens(m,result));
			return m;
		}).collect(Collectors.toList());
		return result1;
	}

	@Override
	public Boolean exportJsonFile(HttpServletResponse response) {
		Boolean b = true;
		LambdaQueryWrapper<SysRegion> lmq = new LambdaQueryWrapper<>();
		lmq.eq(SysRegion::getParentId,"0");
		List<SysRegion> regionList = baseMapper.selectList(lmq);
		// 导出内容到浏览器
		try (
			// 获取响应输出流
			OutputStream outputStream = response.getOutputStream();){
			// 设置响应头
			response.setContentType("application/json");
			response.setHeader("Content-Disposition", "attachment; filename=\"export.json\"");
			// 将查询到的结果序列化为json格式
			//封装查询结果
			Map<String, Object> objectMap=new HashMap<>();
			objectMap.put("dataImportList", regionList);
			ObjectMapper mapper = new ObjectMapper();
			mapper.writerWithDefaultPrettyPrinter().writeValue(outputStream , regionList);
			// 推送输出流结果到浏览器
			outputStream.flush();
		}catch (Exception e){
			e.printStackTrace();
			b = false;
		}
		return b;
	}

	private boolean hasChildren(String pId) {
		LambdaQueryWrapper<SysRegion> lmq = new LambdaQueryWrapper<>();
		lmq.eq(SysRegion::getParentId, pId);
		List<SysRegion> regionList = baseMapper.selectList(lmq);
		return regionList.size() != 0;
	}

	private static List<RegionTreeVo> getChildrens(RegionTreeVo root, List<RegionTreeVo> all) {
		List<RegionTreeVo> children = all.stream().filter(m -> {
			return Objects.equals(m.getParentId(), root.getId());
		}).map((m) -> {
				m.setChildren(getChildrens(m, all));
				return m;
			}
		).collect(Collectors.toList());
		return children;
	}
}
