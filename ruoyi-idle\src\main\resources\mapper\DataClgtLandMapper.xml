<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.idle.mapper.DataClgtLandMapper">

    <resultMap type="com.ruoyi.idle.domain.DataClgtLand" id="DataClgtLandResult">
                    <result property="id" column="id"/>
                    <result property="city" column="city"/>
                    <result property="countyCode" column="county_code"/>
                    <result property="countyName" column="county_name"/>
                    <result property="contractNo" column="contract_no"/>
                    <result property="supplyType" column="supply_type"/>
                    <result property="projectName" column="project_name"/>
                    <result property="year" column="year"/>
                    <result property="quarter" column="quarter"/>
                    <result property="supervisionNo" column="supervision_no"/>
                    <result property="landUse" column="land_use"/>
                    <result property="landArea" column="land_area"/>
                    <result property="signDate" column="sign_date"/>
                    <result property="agreedStartTime" column="agreed_start_time"/>
                    <result property="actualStartTime" column="actual_start_time"/>
                    <result property="actualEndTime" column="actual_end_time"/>
                    <result property="idleStatus" column="idle_status"/>
                    <result property="spotNumber" column="spot_number"/>
                    <result property="insideRemark" column="inside_remark"/>
                    <result property="digestionType" column="digestion_type"/>
                    <result property="address" column="address"/>
                    <result property="geoData" column="geo_data"/>
                    <result property="parcelNum" column="parcel_num"/>
                    <result property="longitude" column="longitude"/>
                    <result property="latitude" column="latitude"/>
                    <result property="isAllot" column="is_allot"/>
                    <result property="warrantNum" column="warrant_num"/>
                    <result property="remark" column="remark"/>
                    <result property="taskNo" column="task_no"/>
                    <result property="deleteFlag" column="delete_flag"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
            </resultMap>

    <select id="selectSupervisionNoByTaskId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT supervision_no
        FROM `data_clgt_land`
        where id = (SELECT idle_land_id FROM data_patrol_task where id = #{taskId})
    </select>

    <select id="getTaskNo" resultType="map">
        select
            coalesce(MAX(task_no),'00000000000') AS taskNo
        from data_clgt_land
        where task_no like CONCAT('%',#{ str },'%')
    </select>
</mapper>
