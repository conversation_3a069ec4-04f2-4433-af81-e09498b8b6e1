package ${packageName}.service;

import ${packageName}.domain.${ClassName};
import ${packageName}.vo.${ClassName}Vo;
import ${packageName}.bo.${ClassName}QueryBo;
import ${packageName}.bo.${ClassName}AddBo;
import ${packageName}.bo.${ClassName}EditBo;
import com.ruoyi.common.core.mybatisplus.core.IServicePlus;
    #if($table.crud || $table.sub)
    import com.ruoyi.common.core.page.TableDataInfo;
    #end

import java.util.Collection;
import java.util.List;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IServicePlus<${ClassName}> {
    /**
     * 查询单个
     * @return
     */
        ${ClassName}Vo queryById(${pkColumn.javaType} ${pkColumn.javaField});

    #if($table.crud || $table.sub)
        /**
         * 查询列表
         */
        TableDataInfo<${ClassName}Vo> queryPageList(${ClassName}QueryBo bo);
    #end

    /**
     * 查询列表
     */
    List<${ClassName}Vo> queryList(${ClassName}QueryBo bo);

    /**
     * 根据新增业务对象插入${functionName}
     * @param bo ${functionName}新增业务对象
     * @return
     */
    Boolean insertByAddBo(${ClassName}AddBo bo);

    /**
     * 根据编辑业务对象修改${functionName}
     * @param bo ${functionName}编辑业务对象
     * @return
     */
    Boolean updateByEditBo(${ClassName}EditBo bo);

    /**
     * 校验并删除数据
     * @param ids 主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return
     */
    Boolean deleteWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid);
}
